namespace Lighthouse.Service.Data.MasterData
{
    public class TransportRequestCargoService : ITransportRequestCargoService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IMapper _mapper;
        private readonly IUserService _userService;
        private readonly IExportService _exportService;
        private readonly IVoyageCargoWeightUtility _voyageCargoWeightUtility;
        private readonly IVendorService _vendorService;
        private readonly IRequestToFlowDataSyncService _requestToFlowDataSyncExtension;
        private readonly IVoyageCargoService _voyageCargoService;
        private readonly ITimeZoneConversionService _timeZoneConversionService;
        private readonly IClientService _clientService;

        public TransportRequestCargoService(IUnitOfWork unitOfWork,
            IMapper mapper,
            IUserService userService,
            IExportService exportService,
             IVoyageCargoWeightUtility voyageCargoWeightUtility,
             IVendorService vendorService,
             IRequestToFlowDataSyncService requestToFlowDataSyncExtension,
             IVoyageCargoService voyageCargoService,
             ITimeZoneConversionService timeZoneConversionService,
             IClientService clientService)
        {
            _unitOfWork = unitOfWork;
            _mapper = mapper;
            _userService = userService;
            _exportService = exportService;
            _voyageCargoWeightUtility = voyageCargoWeightUtility;
            _vendorService = vendorService;
            _requestToFlowDataSyncExtension = requestToFlowDataSyncExtension;
            _voyageCargoService = voyageCargoService;
            _timeZoneConversionService = timeZoneConversionService;
            _clientService = clientService;
        }

        public async Task<IList<TransportRequestCargoModel>> GetAsync()
        {
            var TransportRequestCargos = await _unitOfWork.Repository<TransportRequestCargo>()
                .Query()
                .AsNoTracking()
                .AsSplitQuery()
                .Include(x => x.TransportRequest)
                .Include(x => x.Cargo)
                .Include(x => x.Vendor)
                .Include(x => x.FromAsset)
                .Include(x => x.ToAsset)
                .Include(x => x.FromLocation)
                .Include(x => x.ToLocation)
                .Include(x => x.TransportRequest)
                    .ThenInclude(tr => tr.SailingRequest)
                .Include(x => x.TransportRequestCargoDangerousGoods)
                .Include(x => x.TransportRequestCargoAttachments)
                .Include(x => x.VendorWarehouse)
                .Include(x => x.ViaVendorWarehouse)
                .ToListAsync();

            return _mapper.Map<List<TransportRequestCargoModel>>(TransportRequestCargos);
        }

        public async Task CancelAllTransportCargoesBySailingRequestId(Guid SailingReqest)
        {
            var transportRequestCargoIdsForCancellation = await GetTransportRequestCargoesBySailingRequestId(SailingReqest);

            var allTransportRequestCargoIdsTocancel = new TransportRequestCancellationReasonModel()
            {
                CancellationReason = "Parent Sailing Request Was Declined/Deleted",
                Ids = transportRequestCargoIdsForCancellation
            };

            await CancelTransportRequestCargosAsync(allTransportRequestCargoIdsTocancel);
        }

        private async Task<List<Guid>> GetTransportRequestCargoesBySailingRequestId(Guid sailingRequestId)
        {
            var transportRequestCargoIds = await _unitOfWork.Repository<TransportRequestCargo>()
                .Query(x => x.TransportRequest.SailingRequestId == sailingRequestId)
                .Include(x => x.TransportRequest)
                .Select(x => x.TransportRequestCargoId)
                .ToListAsync();

            return transportRequestCargoIds;
        }

        public async Task<IList<TransportRequestCargoModel>> GetByTransportRequestIdAsync(Guid transportRequestId)
        {
            var user = await _userService.GetCurrentUser();
            var transportRequestCargos = await _unitOfWork.Repository<TransportRequestCargo>()
                .Query()
                .AsNoTracking()
                .AsSplitQuery()
                .Include(x => x.TransportRequest)
                .Include(x => x.Cargo)
                .Include(x => x.Vendor)
                .Include(x => x.FromAsset)
                .Include(x => x.ToAsset)
                .Include(x => x.FromLocation)
                .Include(x => x.ToLocation)
                .Include(x => x.TransportRequest).ThenInclude(tr => tr.SailingRequest).ThenInclude(x => x.SailingRequestActivities)
                .Include(x => x.TransportRequestCargoDangerousGoods)
                .Include(x => x.TransportRequestCargoAttachments)
                .Include(x => x.TransportRequestCargoBundling)
                .Include(x => x.VendorWarehouse)
                .Include(x => x.ViaVendorWarehouse)
                .Where(x => x.TransportRequestId == transportRequestId)
                .OrderBy(x => x.CreatedDate)
                .ToListAsync();

            var transportRequestCargoModels = new List<TransportRequestCargoModel>();

            foreach (var cargo in transportRequestCargos)
            {
                var cargoModel = _mapper.Map<TransportRequestCargoModel>(cargo);

                if (cargoModel.LoadingDateEta.HasValue)
                {
                    cargoModel.OTO = cargoModel.LoadingDateEta > cargoModel.SubmittedDate;
                } else
                {
                    cargoModel.OTO = null;
                }
                
                var location = await _unitOfWork.Repository<Location>()
                    .Query(x => x.LocationId == cargo.LocationId)
                    .FirstOrDefaultAsync();

                cargoModel.EstimatedCargoWeight = _voyageCargoWeightUtility.ConvertWeight(
                    cargo.EstimatedCargoWeight,
                    location.MeasurementUnit,
                    true);

                transportRequestCargoModels.Add(cargoModel);
            }

            return transportRequestCargoModels;
        }

        public async Task<IList<TransportRequestCargoModel>> GetLatestSubmittedVersionByTransportRequestIdAsync(Guid transportRequestId)
        {
            var user = await _userService.GetCurrentUser();

            var latestCargoSnapshot = await _unitOfWork.Repository<TransportRequestCargoSnapshot>()
                .Query(q => q.TransportRequestId == transportRequestId)
                .OrderByDescending(o => o.CreatedDate)
                .FirstOrDefaultAsync();

            if (latestCargoSnapshot == null)
            {
                return new List<TransportRequestCargoModel>();
            }

            var cargos = JsonSerializer.Deserialize<List<TransportRequestCargoModel>>(latestCargoSnapshot.Content)
                .OrderByDescending(o => o.CreatedDate);

            var transportRequestCargos = await _unitOfWork.Repository<TransportRequestCargo>()
                .Query()
                .AsNoTracking()
                .AsSplitQuery()
                .Include(x => x.TransportRequest)
                .Include(x => x.Cargo)
                .Include(x => x.Vendor)
                .Include(x => x.FromAsset)
                .Include(x => x.ToAsset)
                .Include(x => x.FromLocation)
                .Include(x => x.ToLocation)
                .Include(x => x.TransportRequest)
                .ThenInclude(tr => tr.SailingRequest).ThenInclude(x => x.SailingRequestActivities)
                .Include(x => x.TransportRequestCargoDangerousGoods)
                .Include(x => x.TransportRequestCargoAttachments)
                .Include(x => x.TransportRequestCargoBundling)
                .Include(x => x.VendorWarehouse)
                .Include(x => x.ViaVendorWarehouse)
                .Where(x => x.TransportRequestId == transportRequestId)
                .OrderBy(x => x.CreatedDate)
                .ToListAsync();

            var transportRequestCargoModels = new List<TransportRequestCargoModel>();

            foreach (var cargo in transportRequestCargos)
            {
                var cargoModel = cargos.Where(w => w.TransportRequestCargoId == cargo.TransportRequestCargoId).FirstOrDefault();

                if (cargoModel is not null)
                {
                    cargoModel.OTO = cargoModel.LoadingDateEta > cargoModel.SubmittedDate;

                    var location = await _unitOfWork.Repository<Location>()
                     .Query(x => x.LocationId == cargo.LocationId)
                     .FirstOrDefaultAsync();

                    cargoModel.EstimatedCargoWeight = _voyageCargoWeightUtility.ConvertWeight(
                        cargo.EstimatedCargoWeight,
                        location.MeasurementUnit,
                        true);

                    transportRequestCargoModels.Add(cargoModel);
                }
            }

            return transportRequestCargoModels;
        }

        public async Task<IList<TransportRequestCargoModel>> GetByLocationIdAsync(Guid locationId, TransportRequestCargoApprovalFilterModel model)
        {
            var currentUser = await _userService.GetCurrentUser();

            List<Guid> transportRequestIdsByLocation = await _unitOfWork.Repository<TransportRequest>()
                .Query()
                .AsNoTracking()
                .Include(x => x.SailingRequest)
                .Where(x => x.SailingRequest.LocationId == currentUser.LocationId 
                         && x.VoyageDirection != VoyageDirection.Interfield
                         && x.SailingRequest.Status == SailingRequestStatus.Approved
                         && !x.IsComplete
                )
                .Select(x => x.TransportRequestId)
                .ToListAsync();

            var mappedTrcs = new List<TransportRequestCargoModel>();
            foreach (var trId in transportRequestIdsByLocation)
            {
                var snapshot = await _unitOfWork.Repository<TransportRequestCargoSnapshot>()
                    .Query(x => x.TransportRequestId == trId)
                    .AsNoTracking()
                    .OrderByDescending(x => x.CreatedDate)
                    .FirstOrDefaultAsync();

                if (snapshot is not null && !string.IsNullOrWhiteSpace(snapshot.Content))
                {
                    var deserialisedCargoes = JsonSerializer.Deserialize<List<TransportRequestCargoModel>>(snapshot.Content);
                    deserialisedCargoes = deserialisedCargoes.Where(x => x.Status == model.Status && x.AutoApprove == false).ToList();

                    if (model.LoadingTime.HasValue)
                    {
                        deserialisedCargoes = deserialisedCargoes.Where(x => x.CargoExpectedDeliveryTime == model.LoadingTime.Value.Date).ToList();
                    }

                    if (model.AssetId.HasValue)
                    {
                        deserialisedCargoes = deserialisedCargoes.Where(x =>
                              x.TransportRequestVoyageDirection == VoyageDirection.Outbound ? x.ToAssetId == model.AssetId.Value
                            : x.TransportRequestVoyageDirection == VoyageDirection.Inbound ? x.FromAssetId == model.AssetId.Value : true
                        ).ToList();
                    }

                    mappedTrcs.AddRange(deserialisedCargoes);
                }
            }
            return mappedTrcs;
        }

        public async Task<TransportRequestCargoModel> GetByIdAsync(Guid id)
        {
            var TransportRequestCargo = await _unitOfWork.Repository<TransportRequestCargo>()
               .Query()
               .AsNoTracking()
               .AsSplitQuery()
               .Include(x => x.TransportRequest)
               .ThenInclude(x => x.SailingRequest)
               .Include(x => x.Cargo)
               .Where(x => x.TransportRequestCargoId == id)
               .SingleOrDefaultAsync();
            return _mapper.Map<TransportRequestCargoModel>(TransportRequestCargo);
        }

        private async Task<Guid?> GetVoyageIdByTransportRequestCargoId(Guid id)
        {
            var TransportRequestCargo = await _unitOfWork.Repository<TransportRequestCargo>()
               .Query()
               .AsNoTracking()
               .AsSplitQuery()
               .Include(x => x.TransportRequest)
               .ThenInclude(x => x.SailingRequest)
               .Where(x => x.TransportRequestCargoId == id)
            .SingleOrDefaultAsync();

            if (TransportRequestCargo.TransportRequest.SailingRequest.InboundVoyageId.HasValue 
                && TransportRequestCargo.TransportRequest.VoyageDirection == VoyageDirection.Inbound)
            {
                return (Guid)TransportRequestCargo.TransportRequest.SailingRequest.InboundVoyageId;
            }
            if (TransportRequestCargo.TransportRequest.SailingRequest.OutboundVoyageId.HasValue 
                && TransportRequestCargo.TransportRequest.VoyageDirection == VoyageDirection.Outbound)
            {
                return (Guid)TransportRequestCargo.TransportRequest.SailingRequest?.OutboundVoyageId;
            }
            else return null;
            
        }

        public async Task<TransportRequestCargoModel> CreateAsync(TransportRequestCargoUpsertModel model)
        {
            await _unitOfWork.BeginTransactionAsync();

            const double MaxLengthFt = 100;
            const double MaxWidthFt = 40;

            if (model.CargoLength > MaxLengthFt)
                throw new Exception(
                    $"Length too long. Max {MaxLengthFt}ft ({Length.FromFeet(MaxLengthFt).Millimeters}mm).");

            if (model.CargoWidth > MaxWidthFt)
                throw new Exception(
                    $"Width too wide. Max {MaxWidthFt}ft ({Length.FromFeet(MaxWidthFt).Millimeters}mm).");

            model.CargoLength = Math.Round(Length.FromFeet(model.CargoLength).Millimeters, 3);
            model.CargoWidth = Math.Round(Length.FromFeet(model.CargoWidth).Millimeters, 3);

            UserModel currentUser = await _userService.GetCurrentUser();

            var transportRequestCargo = _mapper.Map<TransportRequestCargo>(model);

            transportRequestCargo.TransportRequestCargoId = Guid.NewGuid();
            transportRequestCargo.CreatedById = currentUser.UserId;
            transportRequestCargo.LocationId = currentUser.LocationId.Value;

            if (model.CargoFamilyName == "One-Off")
            {
                transportRequestCargo.OneOffCcuId = model.CargoCcuId;
                transportRequestCargo.ToDoSpecialCargoComplete = false;
                transportRequestCargo.CargoId = null;
            }

            if (model.IsWaste)
            {
                transportRequestCargo.ToDoWasteComplete = false;
            }

            if (model.TransportRequirement == TransportRequirement.Transfer)
            {
                transportRequestCargo.ToDoTransferComplete = false;
            }

            var location = await _unitOfWork.Repository<Location>()
                                            .Query(x => x.LocationId == transportRequestCargo.LocationId)
                                            .FirstOrDefaultAsync();

            transportRequestCargo.EstimatedCargoWeight = _voyageCargoWeightUtility.ConvertWeight(
                transportRequestCargo.EstimatedCargoWeight,
                location.MeasurementUnit,
                false);

            transportRequestCargo.Status = TransportRequestCargoStatus.Draft;

            transportRequestCargo = await _unitOfWork.Repository<TransportRequestCargo>().CreateAsync(transportRequestCargo);

            await _unitOfWork.SaveChangesAsync();
            await _unitOfWork.CommitAsync();

            return await GetByIdAsync(transportRequestCargo.TransportRequestCargoId);
        }

        public async Task BatchCreateAsync(List<TransportRequestCargo> cargoBatch, UserModel user)
        {
            if (cargoBatch.Count == 0) return;

            var repository = _unitOfWork.Repository<TransportRequestCargo>();

            var measurementUnit =
                ((await _unitOfWork.Repository<Location>().FirstOrDefaultAsync(l => l.LocationId == user.LocationId))?.MeasurementUnit) ??
                LocationMeasurementUnit.KG;

            foreach (var cargo in cargoBatch)
            {
                cargo.CargoLength = Math.Round(Length.FromFeet(cargo.CargoLength).Millimeters, 3);
                cargo.CargoWidth = Math.Round(Length.FromFeet(cargo.CargoWidth).Millimeters, 3);

                cargo.TransportRequestCargoId = Guid.NewGuid();
                cargo.CreatedById = user.UserId;
                cargo.LocationId = user.LocationId.Value;

                if (cargo.IsWaste) cargo.ToDoWasteComplete = false;
                if (cargo.TransportRequirement == TransportRequirement.Transfer) cargo.ToDoTransferComplete = false;

                cargo.EstimatedCargoWeight = _voyageCargoWeightUtility.ConvertWeight(cargo.EstimatedCargoWeight, measurementUnit, false);

                cargo.Status = TransportRequestCargoStatus.Draft;

                await repository.CreateAsync(cargo);
            }

            await _unitOfWork.SaveChangesAsync();
        }

        public async Task<TransportRequestCargoModel> UpdateAsync(Guid id, TransportRequestCargoUpsertModel model)
        {
            await _unitOfWork.BeginTransactionAsync();

            UserModel currentUser = await _userService.GetCurrentUser();

            var transportRequestCargo = await _unitOfWork.Repository<TransportRequestCargo>()
               .Query()
               .AsSplitQuery()
               .Where(x => x.TransportRequestCargoId == id)
               .SingleOrDefaultAsync();

            if (model.ToDoSpecialCargoComplete == true)
            {
                transportRequestCargo.SpecialCargoProcessedById = currentUser.UserId;
                transportRequestCargo.SpecialCargoProcessedDate = DateTime.UtcNow;
            }
            else
            {
                transportRequestCargo.SpecialCargoProcessedById = null;
                transportRequestCargo.SpecialCargoProcessedDate = null;
            }

            if (model.ToDoTransferComplete == true)
            {
                transportRequestCargo.TransferProcessedById = currentUser.UserId;
                transportRequestCargo.TransferProcessedDate = DateTime.UtcNow;
            }
            else
            {
                transportRequestCargo.TransferProcessedById = null;
                transportRequestCargo.TransferProcessedDate = null;
            }

            if (model.ToDoWasteComplete == true)
            {
                transportRequestCargo.WasteProcessedById = currentUser.UserId;
                transportRequestCargo.WasteProcessedDate = DateTime.UtcNow;
            }
            else
            {
                transportRequestCargo.WasteProcessedById = null;
                transportRequestCargo.WasteProcessedDate = null;
            }

            transportRequestCargo = _mapper.Map(model, transportRequestCargo);

            var location = await _unitOfWork.Repository<Location>()
                                            .Query(x => x.LocationId == transportRequestCargo.LocationId)
                                            .FirstOrDefaultAsync();

            transportRequestCargo.EstimatedCargoWeight = _voyageCargoWeightUtility.ConvertWeight(
                transportRequestCargo.EstimatedCargoWeight,
                location.MeasurementUnit,
                false);

            if (model.CargoFamilyName == "One-Off")
            {
                transportRequestCargo.OneOffCcuId = model.CargoCcuId;
                transportRequestCargo.ToDoSpecialCargoComplete = false;
                transportRequestCargo.CargoId = null;
            }

            transportRequestCargo.UpdatedById = currentUser.UserId;
            transportRequestCargo.UpdatedDate = DateTime.UtcNow;

            _unitOfWork.Repository<TransportRequestCargo>().Update(transportRequestCargo);

            await _unitOfWork.SaveChangesAsync();
            await _unitOfWork.CommitAsync();

            return await GetByIdAsync(transportRequestCargo.TransportRequestCargoId);
        }

        public async Task<Guid> UpdateTrcStatusSnapshotAsync(Guid id, TransportRequestCargoUpsertModel model)
        {
            await _unitOfWork.BeginTransactionAsync();
            var user = await _userService.GetCurrentUser();
            DateTime dateTimeNow = DateTime.UtcNow;
            try
            {
                await UpdateTransportRequestCreateEditCargoes(user, model, dateTimeNow);

                await CreateTransportRequestSnapshot(user, model, dateTimeNow);

                if (model.Status == TransportRequestCargoStatus.Approved)
                {
                    var voyageId = await _requestToFlowDataSyncExtension.CheckTRVoyageRelationship(model.TransportRequestId);

                    var trItems = await _requestToFlowDataSyncExtension.GetTrItemsByTransportRequestIdAsync(model.TransportRequestId);

                    await UpdateTheTransportRequestItems(trItems, id, user.UserId);
                    
                    await _requestToFlowDataSyncExtension.DataSyncBetweenRequestAndFlow(voyageId, dateTimeNow, trItems, user, false);
                }
            }
            catch (Exception ex)
            {
                await _unitOfWork.RollbackAsync();
                throw new Exception("Error creating Transport Request Snapshots", ex);
            }

            await _unitOfWork.CommitAsync();

            await _unitOfWork.SaveChangesAsync();

            return (Guid)user.LocationId;
        }

        private async Task UpdateTheTransportRequestItems(
            (List<TransportRequestCargo> trcs, List<TransportRequestBulkCargo> trbcs, List<TransportRequestMaterialDetail> trmds) trItems, 
            Guid id, 
            Guid userId
            )
        {
            trItems.trcs = trItems.trcs.Where(x => x.TransportRequestCargoId == id).ToList();
            trItems.trbcs = new();
            trItems.trmds = trItems.trmds.Where(x => x.TransportRequestCargoId == id).ToList();

            foreach (var transportRequestMaterialDetail in trItems.trmds)
            {
                await _unitOfWork.Repository<TransportRequestMaterialDetail>().Query(x => x.TransportRequestMaterialDetailId == transportRequestMaterialDetail.TransportRequestMaterialDetailId)
                    .ExecuteUpdateAsync(x => x
                        .SetProperty(p => p.Status, TransportRequestCargoStatus.Submitted)
                        .SetProperty(p => p.SubmittedById, userId)
                        .SetProperty(p => p.SubmittedDate, DateTime.UtcNow)
                );
            }
        }

        private async Task CreateTransportRequestSnapshot(UserModel user, TransportRequestCargoUpsertModel model, DateTime dateTimeNow)
        {
            Guid snapshotId = Guid.NewGuid();

            var latestCargoSnapshot = await _unitOfWork.Repository<TransportRequestCargoSnapshot>()
                    .Query(x => x.TransportRequestId == model.TransportRequestId)
                    .OrderByDescending(x => x.CreatedDate)
                    .ToListAsync();

            int version = latestCargoSnapshot.Count() + 1;
            var deserialisedCargoes = JsonSerializer.Deserialize<List<TransportRequestCargoModel>>(latestCargoSnapshot[0].Content);
            deserialisedCargoes
                .Where(x => x.TransportRequestCargoId == model.TransportRequestCargoId)
                .ToList()
                .ForEach(x =>
                {
                    x.Status = model.Status;
                    x.AutoApprove = false;
                    x.UpdatedDate = dateTimeNow;
                    x.UpdatedById = user.UserId;
                });

            var mappedTrcs = JsonSerializer.Serialize(_mapper.Map<List<TransportRequestCargoModel>>(deserialisedCargoes));

            var trcSnapshot = new TransportRequestCargoSnapshot
            {
                TransportRequestCargoSnapshotId = snapshotId,
                CreatedDate = dateTimeNow,
                CreatedById = user.UserId,
                Version = $"V{version}",
                Content = mappedTrcs,
                TransportRequestId = model.TransportRequestId,
            };

            var latestBulkSnapshot = await _unitOfWork.Repository<TransportRequestBulkCargoSnapshot>()
               .Query(x => x.TransportRequestId == model.TransportRequestId)
               .AsNoTracking()
               .OrderByDescending(x => x.CreatedDate)
               .FirstOrDefaultAsync();

            var trbcSnapshot = new TransportRequestBulkCargoSnapshot
            {
                TransportRequestBulkCargoSnapshotId = snapshotId,
                CreatedDate = dateTimeNow,
                CreatedById = user.UserId,
                Version = $"V{version}",
                Content = latestBulkSnapshot is not null ? latestBulkSnapshot.Content : "",
                TransportRequestId = model.TransportRequestId,
            };

            var latestMaterialDetailSnapshot = await _unitOfWork.Repository<TransportRequestMaterialDetailSnapshot>()
               .Query(x => x.TransportRequestId == model.TransportRequestId)
               .AsNoTracking()
               .OrderByDescending(x => x.CreatedDate)
               .FirstOrDefaultAsync();

            var trMdSnapshot = new TransportRequestMaterialDetailSnapshot
            {
                TransportRequestMaterialDetailSnapshotId = snapshotId,
                CreatedDate = dateTimeNow,
                CreatedById = user.UserId,
                Version = $"V{version}",
                Content = latestMaterialDetailSnapshot is not null ? latestMaterialDetailSnapshot.Content : "",
                TransportRequestId = model.TransportRequestId,
            };

            await _unitOfWork.Repository<TransportRequestCargoSnapshot>().CreateAsync(trcSnapshot);
            await _unitOfWork.Repository<TransportRequestBulkCargoSnapshot>().CreateAsync(trbcSnapshot);
            await _unitOfWork.Repository<TransportRequestMaterialDetailSnapshot>().CreateAsync(trMdSnapshot);
        }

        private async Task UpdateTransportRequestCreateEditCargoes(UserModel user, TransportRequestCargoUpsertModel model, DateTime dateTimeNow)
        {
            await _unitOfWork.Repository<TransportRequestCargo>()
                    .Query(x => x.TransportRequestCargoId == model.TransportRequestCargoId)
                    .ExecuteUpdateAsync(x => x
                        .SetProperty(p => p.AutoApprove, false)
                        .SetProperty(p => p.Status, model.Status)
                        .SetProperty(p => p.SubmittedById, user.UserId)
                        .SetProperty(p => p.SubmittedDate, dateTimeNow)
                    );
        }

        public async Task<IList<TransportRequestCargoModel>> BulkUpdateAsync(Guid transportRequestId, List<TransportRequestCargoUpsertModel> trcToUpdate, UserModel currentUser)
        {
            if (trcToUpdate.Where(x => x.TransportRequestCargoId.HasValue).Count()
                != trcToUpdate.Where(x => x.TransportRequestCargoId.HasValue)
                .DistinctBy(x => x.TransportRequestCargoId).Count()
               )
                throw new Exception("Duplicate Entry in the list");

            const double MaxLengthFt = 100;
            const double MaxWidthFt = 40;

            await _unitOfWork.BeginTransactionAsync();

            var currentTrCargoes = await _unitOfWork.Repository<TransportRequestCargo>().Query(x => x.TransportRequestId == transportRequestId)
                .Include(x => x.Cargo)
                .Include(x => x.FromAsset)
                .ToListAsync();

            var transportRequestMaterialDetails = await _unitOfWork.Repository<TransportRequestMaterialDetail>()
                .Query()
                .AsNoTracking()
                .Where(x => x.TransportRequestCargoId.HasValue)
                .ToListAsync();

            var itemsToDelete = currentTrCargoes.Where(x => !trcToUpdate.Any(l => l.TransportRequestCargoId == x.TransportRequestCargoId)).ToList();

            foreach (var transportRequestCargoToDelete in itemsToDelete)
            {
                if (transportRequestMaterialDetails.Any(x => x.TransportRequestCargoId == transportRequestCargoToDelete.TransportRequestCargoId))
                    throw new Exception("some Cargoes cannot get deleted since there is a material detail with this item.");
                transportRequestCargoToDelete.Deleted = true;
                currentTrCargoes.Remove(transportRequestCargoToDelete);
            }
            await _unitOfWork.SaveChangesAsync();

            var listToCreate = new List<TransportRequestCargo>();

            foreach (var trcCargoToUpsertModel in trcToUpdate)
            {
                if (trcCargoToUpsertModel.CargoLength > MaxLengthFt)
                    throw new Exception(
                        $"Length too long. Max {MaxLengthFt}ft ({Length.FromFeet(MaxLengthFt).Millimeters}mm).");

                if (trcCargoToUpsertModel.CargoWidth > MaxWidthFt)
                    throw new Exception(
                        $"Width too wide. Max {MaxWidthFt}ft ({Length.FromFeet(MaxWidthFt).Millimeters}mm).");

                trcCargoToUpsertModel.CargoLength = Math.Round(Length.FromFeet(trcCargoToUpsertModel.CargoLength).Millimeters, 3);
                trcCargoToUpsertModel.CargoWidth = Math.Round(Length.FromFeet(trcCargoToUpsertModel.CargoWidth).Millimeters, 3);

                var location = await _unitOfWork.Repository<Location>().Query(x => x.LocationId == currentUser.LocationId).FirstOrDefaultAsync();

                if (trcCargoToUpsertModel.TransportRequestCargoId.HasValue) //if it's update
                {
                    var transportRequestCargo = currentTrCargoes.SingleOrDefault(x => x.TransportRequestCargoId == trcCargoToUpsertModel.TransportRequestCargoId);
                    bool shouldBeChanged = false;
                    var status = transportRequestCargo.Status;

                    if ((transportRequestCargo.Status == TransportRequestCargoStatus.Pending || transportRequestCargo.Status == TransportRequestCargoStatus.Approved || transportRequestCargo.Status == TransportRequestCargoStatus.Rejected)
                        && (transportRequestCargo.Quantity != trcCargoToUpsertModel.Quantity
                           || transportRequestCargo.NumberOfLifts != trcCargoToUpsertModel.NumberOfLifts
                           || transportRequestCargo.CargoDescription != trcCargoToUpsertModel.CargoDescription
                           || transportRequestCargo.FromAssetId != trcCargoToUpsertModel.FromAssetId
                           || transportRequestCargo.ToAssetId != trcCargoToUpsertModel.ToAssetId
                        //The Change of Dangerous Goods is done elsewhere, please refer to the TransportRequestCargoDangerousGoodService.cs File, Thanks
                        )) shouldBeChanged = true;

                    var cargoCcuId = transportRequestCargo.Cargo != null ? transportRequestCargo.Cargo.CCUId : transportRequestCargo.OneOffCcuId;
                    if (cargoCcuId != trcCargoToUpsertModel.CargoCcuId)
                    {
                        shouldBeChanged = true;
                    }

                    if (transportRequestCargo == null) throw new Exception($"Some Transport Request Cargoes do not belong to this Transport Request or do not exist");

                    _mapper.Map(trcCargoToUpsertModel, transportRequestCargo);

                    if (shouldBeChanged)
                    {
                        transportRequestCargo.Status = TransportRequestCargoStatus.Changed;
                        transportRequestCargo.UpdatedById = currentUser.UserId;
                        transportRequestCargo.UpdatedDate = DateTime.UtcNow;
                    }
                    else
                    {
                        transportRequestCargo.Status = status;
                    }

                    transportRequestCargo.EstimatedCargoWeight = _voyageCargoWeightUtility.ConvertWeight(
                        transportRequestCargo.EstimatedCargoWeight,
                        location.MeasurementUnit,
                        false);

                    var cargo = await _unitOfWork.Repository<Cargo>().FirstOrDefaultAsync(x => x.CargoId == trcCargoToUpsertModel.CargoId);

                    if (cargo is null)
                    {
                        transportRequestCargo.OneOffCcuId = trcCargoToUpsertModel.CargoDescription;
                        transportRequestCargo.ToDoSpecialCargoComplete = false;
                        transportRequestCargo.CargoId = null;
                    }
                    else if (cargo is not null)
                    {
                        transportRequestCargo.CargoId = cargo.CargoId;
                    }
                }
                else //if it's create
                {
                    var trcCargoToUpsert = _mapper.Map<TransportRequestCargo>(trcCargoToUpsertModel);

                    trcCargoToUpsert.CreatedById = currentUser.UserId;
                    trcCargoToUpsert.LocationId = currentUser.LocationId.Value;
                    trcCargoToUpsert.Status = TransportRequestCargoStatus.Draft;
                    trcCargoToUpsert.TransportRequestCargoId = Guid.NewGuid();
                    trcCargoToUpsert.CreatedDate = DateTime.UtcNow;

                    if (trcCargoToUpsertModel.CargoFamilyName == "One-Off")
                    {
                        trcCargoToUpsert.OneOffCcuId = trcCargoToUpsertModel.CargoCcuId;
                        trcCargoToUpsert.ToDoSpecialCargoComplete = false;
                        trcCargoToUpsert.CargoId = null;
                    }

                    if (trcCargoToUpsertModel.IsWaste)
                    {
                        trcCargoToUpsert.ToDoWasteComplete = false;
                    }

                    if (trcCargoToUpsertModel.TransportRequirement == TransportRequirement.Transfer)
                    {
                        trcCargoToUpsert.ToDoTransferComplete = false;
                    }

                    trcCargoToUpsert.Status = TransportRequestCargoStatus.Draft;

                    trcCargoToUpsert.EstimatedCargoWeight = _voyageCargoWeightUtility.ConvertWeight(trcCargoToUpsert.EstimatedCargoWeight, location.MeasurementUnit, false);

                    listToCreate.Add(trcCargoToUpsert);
                }
            }

            await _unitOfWork.SaveChangesAsync(); //this applies the updates

            await _unitOfWork.Repository<TransportRequestCargo>().BulkCreateAsync(listToCreate);

            await _unitOfWork.SaveChangesAsync(); //this applies the creates

            await _unitOfWork.CommitAsync();
            return await GetByTransportRequestIdAsync(transportRequestId);
        }

        public async Task ReinstateAsync(List<Guid> transportRequestCargoIds)
        {
            var user = await _userService.GetCurrentUser();

            var transportRequestCargoesBeingReinstated = await _unitOfWork.Repository<TransportRequestCargo>()
                .GetByIdsAsync(transportRequestCargoIds, "TransportRequestCargoId");

            foreach (var transportRequestCargoBeingReinstated in transportRequestCargoesBeingReinstated)
            {
                if (transportRequestCargoBeingReinstated.Status != TransportRequestCargoStatus.Draft)
                    transportRequestCargoBeingReinstated.Status = TransportRequestCargoStatus.Changed;
                    
                transportRequestCargoBeingReinstated.IsCancelled = false;
                transportRequestCargoBeingReinstated.UpdatedById = user.UserId;
                transportRequestCargoBeingReinstated.UpdatedDate = DateTime.UtcNow;
                transportRequestCargoBeingReinstated.CancellationReason = null;
            }

            await _unitOfWork.SaveChangesAsync();
        }

        public async Task<bool> BulkDeleteAsync(List<Guid> transportRequestCargoIds)
        {
            List<Guid?> trCargoIdFoundInMaterialDetails = await _unitOfWork.Repository<TransportRequestMaterialDetail>()
                .Query()
                .AsNoTracking()
                .Where(x => transportRequestCargoIds.Contains((Guid)x.TransportRequestCargoId))
                .Select(x => x.TransportRequestCargoId)
                .ToListAsync();

            if (trCargoIdFoundInMaterialDetails.Count() > 0)
            {
                transportRequestCargoIds.RemoveAll(transportRequestCargoId => trCargoIdFoundInMaterialDetails.Contains(transportRequestCargoId));
            }

            await _unitOfWork.Repository<TransportRequestCargo>()
                .Query(x => transportRequestCargoIds.Contains(x.TransportRequestCargoId))
                .ExecuteUpdateAsync(setters => setters
                    .SetProperty(x => x.Deleted, true)
                );

            await _unitOfWork.SaveChangesAsync();

            if (trCargoIdFoundInMaterialDetails.Count() > 0)
            {
                throw new Exception("Some Cargoes could not be deleted since there is a material detail with this item.");
            }

            return true;
        }

        public async Task<bool> CancelTransportRequestCargosAsync(TransportRequestCancellationReasonModel cancellations)
        {
            var cargoesToRemoveFromCancellation = await GetCargoThatCannotBeCancelled(cancellations.Ids);

            var cargoesWereRemoved = cargoesToRemoveFromCancellation.Count > 0;

            if (cargoesWereRemoved)
            {
                cancellations.Ids.RemoveAll(x => cargoesToRemoveFromCancellation.Contains(x));
            }

            await UpdateCargoStatusesBasedOnPreviousStatus(cancellations);

            if (cargoesWereRemoved)
            {
                if (cargoesToRemoveFromCancellation.Count == 1) throw new Exception("1 cargo has been lifted/inspected/customs-cleared/marked as arrived so can't  be cancelled. Please contact the cargo team for more information");
                else throw new Exception($"{cargoesToRemoveFromCancellation.Count} cargos have been lifted/inspected/customs-cleared/marked as arrived so can't  be cancelled. Please contact the cargo team for more information.");
            }
            return true;
        }

        private async Task UpdateCargoStatusesBasedOnPreviousStatus(TransportRequestCancellationReasonModel cancellations)
        {
            var transportRequestCargos = await _unitOfWork.Repository<TransportRequestCargo>()
                .GetByIdsAsync(cancellations.Ids, "TransportRequestCargoId");

            await DeleteTransportRequestCargoes(transportRequestCargos);
            await CancelTransportRequestCargoes(transportRequestCargos, cancellations.CancellationReason);
        }

        private async Task DeleteTransportRequestCargoes(List<TransportRequestCargo> transportRequestCargos)
        {
            var cargoesToDelete = new List<TransportRequestCargo>();

            foreach (var transportRequestCargo in transportRequestCargos)
            {
                if (transportRequestCargo.Status == TransportRequestCargoStatus.Draft)
                {
                    cargoesToDelete.Add(transportRequestCargo);
                }

                if (transportRequestCargo.Status == TransportRequestCargoStatus.Pending)
                {
                    var isCargoInFlow = await _unitOfWork.Repository<VoyageCargo>()
                        .Query(x => x.TransportRequestCargoId == transportRequestCargo.TransportRequestCargoId)
                        .FirstOrDefaultAsync();

                    if (isCargoInFlow is null)
                    {
                        cargoesToDelete.Add(transportRequestCargo);
                    }
                }
            }

            await DeleteAllAssiciatedMaterialDetailsByTheCargoesToDelete(cargoesToDelete);

            foreach (var cargoToDelete in cargoesToDelete)
            {
                cargoToDelete.Deleted = true;
                cargoToDelete.UpdatedDate = DateTime.UtcNow;
            }
            await _unitOfWork.SaveChangesAsync();
        }

        private async Task DeleteAllAssiciatedMaterialDetailsByTheCargoesToDelete(List<TransportRequestCargo> cargoesToDelete)
        {
            foreach (var cargoToDelete in cargoesToDelete)
            {
                var transportRequestMaterialDetails = await _unitOfWork.Repository<TransportRequestMaterialDetail>()
                    .Query(x => x.TransportRequestCargoId == cargoToDelete.TransportRequestCargoId)
                    .ToListAsync();

                foreach(var materialDetailToDelete in transportRequestMaterialDetails)
                {
                    materialDetailToDelete.Deleted = true;
                    materialDetailToDelete.UpdatedDate = DateTime.UtcNow;
                }
            }
            await _unitOfWork.SaveChangesAsync();
        }

        private async Task CancelTransportRequestCargoes(List<TransportRequestCargo> transportRequestCargoes, string cancellationReason)
        {
            var user = await _userService.GetCurrentUser();

            foreach (var transportRequestCargo in transportRequestCargoes)
            {
                transportRequestCargo.IsCancelled = true;
                transportRequestCargo.CancellationReason = cancellationReason;
                transportRequestCargo.UpdatedById = user.UserId;
                transportRequestCargo.UpdatedDate = DateTime.UtcNow;
                transportRequestCargo.Status = TransportRequestCargoStatus.Changed;
            }
            await _unitOfWork.SaveChangesAsync();

            await CancelTransportRequestMaterialDetailsByCancelledCargoes(transportRequestCargoes, cancellationReason, user.UserId);
        }

        private async Task CancelTransportRequestMaterialDetailsByCancelledCargoes(List<TransportRequestCargo> cancelledCargoes, string cancellationReason, Guid userId)
        {
            foreach (var cancelledCargo in cancelledCargoes)
            {
                await _unitOfWork.Repository<TransportRequestMaterialDetail>()
                     .Query(x => x.TransportRequestCargoId == cancelledCargo.TransportRequestCargoId)
                     .ExecuteUpdateAsync(x => x
                         .SetProperty(p => p.Status, TransportRequestCargoStatus.Changed)
                         .SetProperty(p => p.IsCancelled, true)
                         .SetProperty(p => p.CancellationReason, cancellationReason)
                         .SetProperty(p => p.UpdatedDate, DateTime.UtcNow)
                         .SetProperty(p => p.UpdatedById, userId)
                     );
            }
            await _unitOfWork.SaveChangesAsync();
        }

        public async Task<List<Guid>> GetCargoThatCannotBeCancelled(List<Guid> cargoesToCancel)
        {
            if (cargoesToCancel.Count > 0)
            {
                var voyageId = await GetVoyageIdByTransportRequestCargoId(cargoesToCancel.First());

                if (voyageId is not null)
                {
                    var cargoesFromRequestFoundInFlow = await _voyageCargoService.GetCargoesByTransportRequestCargoIdAndVoyageId(cargoesToCancel, (Guid)voyageId);

                    if (cargoesFromRequestFoundInFlow.Any())
                    {
                        var cargoesThatCannotBeCancelled = new List<Guid>();

                        foreach (var cargoFoundInFlow in cargoesFromRequestFoundInFlow)
                        {
                            if (cargoFoundInFlow.PassedInspection
                                || cargoFoundInFlow.Dispatched
                                || cargoFoundInFlow.ArrivalTime.HasValue
                                || (cargoFoundInFlow.CustomsCleared.HasValue && cargoFoundInFlow.CustomsCleared.Value == true)
                                || cargoFoundInFlow.CompletedLift)
                            {
                                cargoesThatCannotBeCancelled.Add((Guid)cargoFoundInFlow.TransportRequestCargoId);
                            }
                        }
                        return cargoesThatCannotBeCancelled;
                    }
                }
            }

            return new();
        }

        public async Task<bool> UpdateTransportRequestCargoCommentAsync(TransportRequestCargoCommentModel cargoCommentModel)
        {
            await _unitOfWork.BeginTransactionAsync();

            await _unitOfWork.Repository<TransportRequestCargo>()
                .Query(x => x.TransportRequestCargoId == cargoCommentModel.TransportRequestCargoId)
                .ExecuteUpdateAsync(x => x
                    .SetProperty(p => p.Comments, cargoCommentModel.Comment)
                );

            await _unitOfWork.CommitAsync();

            return true;
        }

        public async Task<byte[]> ExportTransportRequestCargos(Guid id, string timezone)
        {
            var transportRequestCargoes = await _unitOfWork.Repository<TransportRequestCargo>()
                .Query()
                .Where(x => x.TransportRequestId == id)
                .AsSplitQuery()
                .Include(x => x.Vendor)
                .Include(x => x.ViaVendor)
                .Include(x => x.VendorWarehouse)
                .Include(x => x.ViaVendorWarehouse)
                .Include(x => x.TransportRequestCargoAttachments)
                .Include(x => x.TransportRequestCargoDangerousGoods)
                .Include(x => x.FromLocation)
                .Include(x => x.ToLocation)
                .Include(x => x.FromAsset)
                .Include(x => x.ToAsset)
                .Include(x => x.Cargo)
                .Include(x => x.TransportRequest).ThenInclude(x => x.Voyage)
                .OrderBy(x => x.CreatedDate)
                .Select(x => _mapper.Map<TransportRequestCargoModel>(x))
                .ToListAsync();

            return _exportService.ConvertTransportRequestCargosToExcel(transportRequestCargoes, timezone);
        }

        public async Task<(byte[] file, string name)> GetBulkUploadTemplateFile(Guid transportRequestId)
        {
            var request = await _unitOfWork.Repository<TransportRequest>()
                .Query(r => r.TransportRequestId == transportRequestId)
                .Include(r => r.SailingRequest)
                .AsNoTracking()
                .FirstOrDefaultAsync();

            if (request is null) throw new InvalidOperationException("The specified transport request cannot be found");
            if (request.VoyageDirection == VoyageDirection.Interfield)
                throw new InvalidOperationException("Interfield trasnport requests are not valid for bulk upload");

            var locationId = (await _userService.GetCurrentUser()).LocationId;

            var ccuIds = await _unitOfWork.Repository<Cargo>()
                .Query(c => c.LocationId == locationId)
                .AsNoTracking()
                .Select(c => c.CCUId)
                .ToArrayAsync();

            var dgNames = await _unitOfWork.Repository<DangerousGood>()
                .Query(d => d.DangerousGoodLocations.Any(l => l.LocationId == locationId))
                .AsNoTracking()
                .Select(d => d.UnNo)
            .ToArrayAsync();

            var vendors = (await _vendorService.GetAllByLocationAsync(request.SailingRequest.LocationId ?? Guid.Empty)).Select(v => v.VendorName).ToArray();

            var locations = await _unitOfWork.Repository<Location>()
                .Query(l => l.LocationId == locationId)
                .AsNoTracking()
                .Select(l => l.Name).
                ToArrayAsync();

            var assets = await _unitOfWork.Repository<Asset>()
                .Query(a => a.AssetLocations.Any(l => l.LocationId == locationId))
                .AsNoTracking()
                .Select(a => a.Name)
                .ToArrayAsync();

            var froms = (request.VoyageDirection == VoyageDirection.Outbound) ? locations : assets;
            var tos = (request.VoyageDirection == VoyageDirection.Outbound) ? assets : locations;

            var file = TransportRequestCargoListBulkUploadImporter.DownloadTemplateFile(
                ccuIds, froms, tos, ["Yes", "No"], Enum.GetNames<VoyageCargoPriorityOrder>(),
                dgNames, vendors, Enum.GetNames<TransportRequirement>(),
                ["Yes", "No"], Enum.GetNames<TransportRequestLoadingInstructions>(),
                request.VoyageDirection == VoyageDirection.Outbound
            );

            return (file, "Request Cargo List_Template.xlsx");
        }

        public async Task<(byte[] file, string name, int uploadedRows)> ImportBulkUploadTemplateFile(Guid transportRequestId, IFormFile file, UserModel user)
        {
            using MemoryStream stream = new();
            await file.CopyToAsync(stream);

            var importer = new TransportRequestCargoListBulkUploadImporter(_timeZoneConversionService, stream.ToArray());
            var importData = importer.ImportTemplateFile();

            if (string.IsNullOrEmpty(importData.ErrorCategory))
            {
                if (importData.ImportedRows.Count == 0)
                    throw new InvalidOperationException("The specified template was empty");

                if (await _unitOfWork.Repository<TransportRequest>().FirstOrDefaultAsync(r => r.TransportRequestId == transportRequestId) is not TransportRequest request)
                    throw new InvalidOperationException("No transport request was found for the specified TransportRequestId");

                if (request.VoyageDirection == VoyageDirection.Interfield)
                    throw new InvalidOperationException("Interfield trasnport requests are not valid for bulk upload");

                var vendorRepo = _unitOfWork.Repository<Vendor>();
                var assetRepo = _unitOfWork.Repository<Asset>();
                var locationRepo = _unitOfWork.Repository<Location>();
                var cargoRepo = _unitOfWork.Repository<Cargo>();
                var cargos = new List<TransportRequestCargo>(importData.ImportedRows.Count);
                foreach (TransportRequestCargoListBulkUploadModel row in importData.ImportedRows)
                {
                    var cargo = _mapper.Map<TransportRequestCargo>(row);
                    cargo.TransportRequestId = transportRequestId;
                    cargo.CargoId = (await cargoRepo.FirstOrDefaultAsync(c => c.CCUId == row.CcuId))?.CargoId;

                    cargo.VendorId = (await vendorRepo.FirstOrDefaultAsync(v => v.VendorName == row.Vendor))?.VendorId;
                    cargo.ViaVendorId = (await vendorRepo.FirstOrDefaultAsync(v => v.VendorName == row.ViaVendor))?.VendorId;

                    if (!string.IsNullOrEmpty(row.Dg))
                    {
                        DangerousGood dg = await _unitOfWork.Repository<DangerousGood>().FirstOrDefaultAsync(d => d.UnNo == row.Dg);
                        if (dg is null) throw new InvalidOperationException($"{row.Dg} is not a valid UN NO");

                        cargo.TransportRequestCargoDangerousGoods = [
                            new  TransportRequestCargoDangerousGood { TransportRequestCargo=cargo,  DangerousGood = dg ,CreatedById = user.UserId }
                        ];
                    }

                    if (request.VoyageDirection == VoyageDirection.Outbound)
                    {
                        cargo.FromAssetId = null;
                        cargo.FromLocationId = (await locationRepo.FirstOrDefaultAsync(l => l.Name == row.From))?.LocationId;

                        cargo.ToAssetId = (await assetRepo.FirstOrDefaultAsync(a => a.Name == row.To))?.AssetId;
                        cargo.ToLocationId = null;
                    }
                    else
                    {
                        cargo.FromAssetId = (await assetRepo.FirstOrDefaultAsync(a => a.Name == row.From))?.AssetId;
                        cargo.FromLocationId = null;

                        cargo.ToAssetId = null;
                        cargo.ToLocationId = (await locationRepo.FirstOrDefaultAsync(l => l.Name == row.To))?.LocationId;
                    }

                    cargos.Add(cargo);
                }
                await BatchCreateAsync(cargos, user);
                return ([], "Request Cargo List_Template.xlsx", importData.ImportedRows.Count);
            }
            else
                return (importData.ValidationFile, $"Request Cargo List_Template({importData.ErrorCategory}).xlsx", 0);
        }

        public async Task<(byte[] file, string name)> GetCargoReport(Guid transportRequestId, List<Guid> offshoreInstallationIds)
        {
            var tz = await _timeZoneConversionService.GetCurrentUserTimeZoneInfo();
            var dtToStr = (DateTime? value) => value.HasValue ? _timeZoneConversionService.ConvertUtcToLocalTime(value.Value, tz).ToString("dd/MM/yyyy HH:mm") : "";
            var dateToStr = (DateTime? value) => value.HasValue ? _timeZoneConversionService.ConvertUtcToLocalTime(value.Value, tz).ToString("dd/MM/yyyy") : "";
            var dateOnlyToStr = (DateOnly? value) => value.HasValue ? value.Value.ToString("dd/MM/yyyy") : "";
            var timeOnlyToStr = (TimeOnly? value) => value.HasValue ? _timeZoneConversionService.ConvertUtcToLocalTime(value.Value, tz).ToString("HH:mm") : "";

            var request = await _unitOfWork.Repository<TransportRequest>()
                .Query(r => r.TransportRequestId == transportRequestId)
                .AsNoTracking()
                .AsSplitQuery()
                .Select(tr => new TransportRequestExportModel
                {
                    AmountOfLifts =tr.TransportRequestCargos.Where(c=>!c.IsCancelled).Sum(c=>c.NumberOfLifts).ToString(),// todo: Moved lines will be done later
                    BulkLines =
                        tr.TransportRequestBulkCargos
                            .Where(b =>
                                offshoreInstallationIds.Count == 0 || tr.VoyageDirection == VoyageDirection.Interfield || (
                                    tr.VoyageDirection == VoyageDirection.Outbound ?
                                        offshoreInstallationIds.Any(i => i == b.ToAssetId) :
                                        offshoreInstallationIds.Any(i => i == b.FromAssetId))
                                    )
                            .OrderBy(b => b.CreatedDate)
                            .Select(b => new TransportRequestExportBulkLineModel
                            {
                                Comments = b.Comment,
                                DgClasses = b.TransportRequestBulkCargoDangerousGood != null ? b.TransportRequestBulkCargoDangerousGood.DangerousGood.Class : "",
                                From = tr.VoyageDirection == VoyageDirection.Outbound ? b.FromLocation.Name : b.FromAsset.Name,
                                IsGrayedout = b.IsCancelled,        // todo: Moved lines will be done later
                                Quantity = b.Quantity.ToString(),
                                Sg = b.Sg.ToString(),
                                To = tr.VoyageDirection == VoyageDirection.Inbound ? b.ToLocation.Name : b.ToAsset.Name,
                                TransportRequestBulkCargoId = b.TransportRequestBulkCargoId,
                                Unit = b.BulkType.UnitName,
                                UnNos = b.TransportRequestBulkCargoDangerousGood != null ? b.TransportRequestBulkCargoDangerousGood.DangerousGood.UnNo : "",
                                Vendor = b.Vendor != null ? b.Vendor.VendorName : "",
                                Waste = b.IsWaste ? "Yes" : "No",
                            }).ToList(),
                    BulkListVersion = tr.TransportRequestBulkCargoSnapshots.OrderByDescending(s => s.CreatedDate).Select(s => s.Version).FirstOrDefault(),
                    CargoLines =
                        tr.TransportRequestCargos
                            .Where(c =>
                                offshoreInstallationIds.Count == 0 || tr.VoyageDirection == VoyageDirection.Interfield || (
                                    tr.VoyageDirection == VoyageDirection.Outbound ?
                                        offshoreInstallationIds.Any(i => i == c.ToAssetId) :
                                        offshoreInstallationIds.Any(i => i == c.FromAssetId))
                                    )
                            .OrderBy(c => c.CreatedDate)
                            .Select(c => new TransportRequestExportCargoLineModel
                            {
                                ActualWeight = tr.Voyage == null || tr.Voyage.VoyageCargoes.FirstOrDefault(vc => vc.TransportRequestCargoId == c.TransportRequestCargoId) == null ?
                                    "" :
                                    tr.Voyage.VoyageCargoes
                                        .First(vc => vc.TransportRequestCargoId == c.TransportRequestCargoId)
                                        .VoyageCargoLifts.Where(l => !l.Inactive)
                                        .Sum(l => l.CapturedWeightKg).ToString(),
                                CcuId = c.Cargo == null ? c.OneOffCcuId : c.Cargo.CCUId,
                                ChangeReason = c.ChangeReason.HasValue ? Enum.GetName(c.ChangeReason.Value) : "",
                                CollectDate = dtToStr(c.CollectDate),
                                Comments = c.Comments,
                                Description = c.CargoDescription,
                                DgClasses = string.Join(", ", c.TransportRequestCargoDangerousGoods.Select(dg => dg.DangerousGood.Class)),
                                Diameter = c.Diameter.ToString(),
                                EstimatedWeight = c.EstimatedCargoWeight.ToString(),
                                ExpectedDeliveryTime = dtToStr(c.CargoExpectedDeliveryTime),
                                From = tr.VoyageDirection == VoyageDirection.Outbound ? c.FromLocation.Name : c.FromAsset.Name,
                                IsGrayedout = c.IsCancelled,        // todo: Moved lines will be done later
                                Length = Math.Round(Length.FromMillimeters(c.CargoLength).Feet, 2).ToString(),
                                Lifts = c.NumberOfLifts.ToString(),
                                LoadingInstructions = c.LoadingInstructions.HasValue ? Enum.GetName(c.LoadingInstructions.Value) : "",
                                PriorityOrder = c.PriorityOrder.HasValue ? Enum.GetName(c.PriorityOrder.Value) : "",
                                Quantity = c.Quantity.ToString(),
                                Requestor = c.Requestor,
                                SealNumber = c.SealNumber,
                                To = tr.VoyageDirection == VoyageDirection.Inbound ? c.ToLocation.Name : c.ToAsset.Name,
                                TransportRequestCargoId = c.TransportRequestCargoId,
                                TransportRequirement = c.TransportRequirement.HasValue ? Enum.GetName(c.TransportRequirement.Value) : "",
                                UnNos = string.Join(", ", c.TransportRequestCargoDangerousGoods.Select(dg => dg.DangerousGood.UnNo)),
                                Vendor = c.Vendor.VendorName,
                                VendorWarehouse = c.VendorWarehouse != null ? c.VendorWarehouse.Name : "",
                                ViaVendor = c.ViaVendor == null ? "" : c.ViaVendor.VendorName,
                                Waste = c.IsWaste ? "Yes" : "No",
                                Width = Math.Round(Length.FromMillimeters(c.CargoWidth).Feet, 2).ToString().ToString()
                            }).ToList(),
                    CargoListVersion = tr.TransportRequestCargoSnapshots.OrderByDescending(s => s.CreatedDate).Select(s => s.Version).FirstOrDefault(),
                    DayOfSailing = tr.Voyage != null ? dateToStr(tr.Voyage.SailingDischargeDate) : "",
                    Direction = tr.VoyageDirection,
                    DirectionName = Enum.GetName(tr.VoyageDirection),
                    Eta = $"{dateOnlyToStr(tr.SailingRequest.StartTime)} {timeOnlyToStr(tr.SailingRequest.ETA)}",
                    Etd = $"{dateOnlyToStr(tr.SailingRequest.EndTime)} {timeOnlyToStr(tr.SailingRequest.ETD)}",
                    ExpectedWorkTime = $"{tr.SailingRequest.ClusterTime} {tr.SailingRequest.TimeUnit}",
                    From =
                        tr.VoyageDirection == VoyageDirection.Inbound ?
                            (tr.TransportRequestCargos.Count > 0 ? tr.TransportRequestCargos.First().ToLocation.Name : "") :
                            (tr.TransportRequestCargos.Count > 0 ? tr.TransportRequestCargos.First().FromLocation.Name : ""),
                    HasDGs =
                        tr.TransportRequestCargos.Any(c => c.TransportRequestCargoDangerousGoods.Count != 0) ||
                        tr.TransportRequestBulkCargos.Any(b => b.TransportRequestBulkCargoDangerousGoodId == null) ||
                        tr.TransportRequestMaterialDetails.Any(m => m.TransportRequestMaterialDetailDangerousGoodId == null) ? "Yes" : "No",
                    Installations = string.Join(", ", tr.SailingRequest.SailingRequestAssets.Select(i => i.Asset.Name)),
                    IsBulkRequired = tr.SailingRequest.isBulkReq ? "Yes" : "No",
                    IsMailBag = tr.SailingRequest.isMailbag ? "Yes" : "No",
                    LengthUnit = "ft",
                    LoadingDate =
                        tr.SailingRequest.SailingRequestActivities.Any(a => a.IsInPort && a.Status == SailingRequestActivityStatus.Planned) ?
                            dtToStr(
                                tr.SailingRequest.SailingRequestActivities
                                    .Where(a => a.IsInPort && a.Status == SailingRequestActivityStatus.Planned)
                                    .OrderByDescending(a => a.CreatedDate)
                                    .First().StartTime
                            ) :
                            (tr.SailingRequest.StartTime != null ? dateOnlyToStr(tr.SailingRequest.StartTime) : "N/A"),
                    Manifest = tr.Voyage != null ? string.Join(", ", tr.Voyage.OffshoreLocations.Select(l => l.ManifestNumber)) : "",
                    MaterialDetailsLines =
                         (tr.TransportRequestMaterialDetails
                            .Where(m => offshoreInstallationIds.Count == 0 || offshoreInstallationIds.Any(i => i == m.OffshoreInstallationId))
                            .OrderBy(m => m.CreatedDate)
                            .Select(m => new TransportRequestExportMaterialDetailsLineModel
                            {
                                ChangeReason = m.ChangeReason.HasValue ? Enum.GetName(m.ChangeReason.Value) : "",
                                Comments = m.Comments,
                                CommodityCode = m.CommodityCode,
                                CountryOfOrigin = m.CountryOfOrigin,
                                CustomesDocumentDate = dateToStr(m.CustomsDocumentDate),
                                CustomesDocumentNumber = m.CustomsDocumentNumber,
                                CustomsEntryType = m.CustomsEntryType.HasValue ? Enum.GetName(m.CustomsEntryType.Value) : "",
                                CustomStatus = m.CustomsStatus.HasValue ? Enum.GetName(m.CustomsStatus.Value) : "",
                                Description = m.Description,
                                DgClasses =
                                    m.TransportRequestMaterialDetailDangerousGood == null ?
                                        "" :
                                        (
                                            m.TransportRequestMaterialDetailDangerousGood.TransportRequestCargoDangerousGood != null ?
                                                m.TransportRequestMaterialDetailDangerousGood.TransportRequestCargoDangerousGood.DangerousGood.Class :
                                                m.TransportRequestMaterialDetailDangerousGood.TransportRequestBulkCargoDangerousGood.DangerousGood.Class
                                        ),
                                EstimatedWeight = m.EstimatedWeight.ToString(),
                                Ewc = m.EWC.ToString(),
                                IsGrayedout = m.IsCancelled,     // todo: Moved lines will be done later
                                LineItem =
                                    m.TransportRequestCargoId != null ?
                                        m.TransportRequestCargo.Cargo.CCUId :
                                        (m.TransportRequestBulkCargoId != null ?
                                            (
                                                m.TransportRequestBulkCargo.BulkType.Name + ": " +
                                                (tr.VoyageDirection == VoyageDirection.Outbound ? m.TransportRequestBulkCargo.FromLocation.Name : m.TransportRequestBulkCargo.FromAsset.Name) +
                                                " - " +
                                                (tr.VoyageDirection == VoyageDirection.Outbound ? m.TransportRequestBulkCargo.ToAsset.Name : m.TransportRequestBulkCargo.ToLocation.Name)
                                            ) :
                                            ""
                                        ),
                                ManifestNumber = m.ManifestNumber,
                                OffshoreInstallation = m.OffshoreInstallation,
                                PackingUnit = m.PackagingUnit,
                                PickupLocation = m.PickupLocation,
                                PoNumber = m.PoNumber,
                                Quantity = m.Quantity.ToString(),
                                Requester = m.Requestor,
                                SerialNumber = m.SerialNumber,
                                TransportRequestCargoId = m.TransportRequestCargoId,
                                TransportRequestBulkCargoId = m.TransportRequestBulkCargoId,
                                UnNos =
                                    m.TransportRequestMaterialDetailDangerousGood == null ?
                                        "" :
                                        (
                                            m.TransportRequestMaterialDetailDangerousGood.TransportRequestCargoDangerousGood != null ?
                                                m.TransportRequestMaterialDetailDangerousGood.TransportRequestCargoDangerousGood.DangerousGood.UnNo :
                                                m.TransportRequestMaterialDetailDangerousGood.TransportRequestBulkCargoDangerousGood.DangerousGood.UnNo
                                        ),
                                Value = m.Value,
                                Vendor = m.Vendor != null ? m.Vendor.VendorName : "",
                                Waste = m.Waste ? "Yes" : "No",
                                WasteDescription = m.WasteDescription,
                                WorkOrder = m.WorkOrder
                            })).ToList(),
                    MaterialDetailsListVersion = tr.TransportRequestMaterialDetailSnapshots.OrderByDescending(s => s.CreatedDate).Select(s => s.Version).FirstOrDefault(),
                    Operator = tr.SailingRequest.Client.Name,
                    OperatorLogoId = tr.SailingRequest.Client.ClientLogoId,
                    OperatorLogo = null,    // Filled later
                    SumOfMaterials = (tr.TransportRequestMaterialDetails.Count() - tr.TransportRequestMaterialDetails.Where(m => m.IsCancelled).Count()).ToString(),
                    To =
                        tr.SailingRequest.Cluster != null ?
                            tr.SailingRequest.Cluster.Name :
                            string.Join(", ", tr.SailingRequest.SailingRequestAssets.Select(a => a.Asset.Name)),
                    User = null,            // Filled later
                    VesselName = tr.SailingRequest.Vessel.Name,
                    VoyageNumber = tr.Voyage != null ? tr.Voyage.VoyageNumber : "No voyage assigned",
                    WeightUnit = null       // Filled later
                })
                .FirstOrDefaultAsync();

            if (request is null) throw new InvalidOperationException("The specified transport request cannot be found");

            var user = await _userService.GetCurrentUser();
            var userLocation = await _unitOfWork.Repository<Location>().FirstOrDefaultAsync(l => l.LocationId == user.LocationId);
            request.OperatorLogo = request.OperatorLogoId is null ? null : await _clientService.DownloadClientLogoAsync(new Client { ClientLogoId = request.OperatorLogoId });
            request.User = user.FullName;
            request.WeightUnit = Enum.GetName(userLocation.MeasurementUnit);

            (byte[] file, DateTime time) = new TransportRequestExporter(request).Export();

            DateTime fileTime = _timeZoneConversionService.ConvertUtcToLocalTime(time, tz);
            return (file, $"{request.VesselName}_{request.VoyageNumber ?? ""}_Cargo List_{fileTime:dd_MM_yy_HH:mm_ss}.xlsx");
        }
    }
}