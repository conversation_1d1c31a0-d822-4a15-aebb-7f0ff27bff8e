namespace Lighthouse.Data.Context {
    public class DataContext : DbContext {

        public DataContext(DbContextOptions<DataContext> options) : base(options) {
        }
        public DataContext() { }
        public DbSet<User> Users { get; set; }
        public DbSet<Vessel> Vessels { get; set; }
        public DbSet<Asset> Assets { get; set; }
        public DbSet<TankType> TankTypes { get; set; }
        public DbSet<Distance> Distances { get; set; }
        public DbSet<Client> Clients { get; set; }
        public DbSet<HireStatement> HireStatements { get; set; }
        public DbSet<HireStatementBulk> HireStatementBulks { get; set; }
        public DbSet<Activity> Activities { get; set; }
        public DbSet<BulkType> BulkTypes { get; set; }
        public DbSet<Unit> Units { get; set; }
        public DbSet<VesselTank> VesselTanks { get; set; }
        public DbSet<ReportType> ReportTypes { get; set; }
        public DbSet<ClientReportType> ClientReportTypes { get; set; }
        public DbSet<ClientAsset> ClientAssets { get; set; }
        public DbSet<Voyage> Voyages { get; set; }
        public DbSet<VoyageBulk> VoyageBulks { get; set; }
        public DbSet<VesselActivity> VesselActivities { get; set; }
        public DbSet<BulkTransaction> BulkTransactions { get; set; }
        public DbSet<TankStatus> TankStatuses { get; set; }
        public DbSet<BillingPeriod> BillingPeriods { get; set; }
        public DbSet<VoyageBillingPeriod> VoyageBillingPeriods { get; set; }
        public DbSet<ClientBillingPeriodTimeAllocation> ClientBillingPeriodTimeAllocations { get; set; }
        public DbSet<DeckUsage> DeckUsage { get; set; }
        public DbSet<ParallelActivity> ParallelActivities { get; set; }
        public DbSet<BulkRequest> BulkRequests { get; set; }
        public DbSet<Setting> Settings { get; set; }
        public DbSet<MobileWell> MobileWells { get; set; }
        public DbSet<VoyageBulkQuantities> VoyageBulkQuantities { get; set; }
        public DbSet<AuditLog> AuditLogs { get; set; }
        public DbSet<BillingPeriodDocument> BillingPeriodDocuments { get; set; }
        public DbSet<SailingRequest> SailingRequests { get; set; }
        public DbSet<SailingRequestActivity> SailingRequestActivities { get; set; }
        public DbSet<SailingRequestAsset> SailingRequestAssets { get; set; }
        public DbSet<ActivityCategory> ActivityCategories { get; set; }
        public DbSet<ActivityCategoryType> ActivityCategoryTypes { get; set; }
        public DbSet<Location> Locations { get; set; }
        public DbSet<AssetLocation> AssetLocations { get; set; }
        public DbSet<ClientLocation> ClientLocations { get; set; }
        public DbSet<Site> Sites { get; set; }
        public DbSet<Area> Areas { get; set; }
        public DbSet<SailingRequestUserComment> SailingRequestUserComments { get; set; }
        public DbSet<CommentReadByUser> CommentReadByUsers { get; set; }
        public DbSet<Cargo> Cargos { get; set; }
        public DbSet<VoyageCargo> VoyageCargos { get; set; }
        public DbSet<VoyageCargoLift> VoyageCargoLifts { get; set; }
        public DbSet<VoyageCargoSailingRequestActivity> VoyageCargoSailingRequestActivities { get; set; }
        public DbSet<VoyageCargoSnapshot> VoyageCargoSnapshots { get; set; }
        public DbSet<OffshoreLocation> OffshoreLocations { get; set; }
        public DbSet<VoyageEvent> VoyageEvents { get; set; }
        public DbSet<DangerousGood> DangerousGoods { get; set; }
        public DbSet<DangerousGoodLocation> DangerousGoodLocations { get; set; }
        public DbSet<VoyageCargoDangerousGood> VoyageCargoDangerousGoods { get; set; }
        public DbSet<Vendor> Vendors { get; set; }
        public DbSet<VendorWarehouse> VendorWarehouses { get; set; }
        public DbSet<AreaBlockingActivity> AreaBlockingActivities { get; set; }
        public DbSet<BlockingActivity> BlockingActivities { get; set; }
        public DbSet<VoyageMaterialDetail> VoyageMaterialDetails { get; set; }
        public DbSet<VoyageMaterialDetailDangerousGood> VoyageMaterialDetailDangerousGoods { get; set; }
        public DbSet<LoadCell> LoadCells { get; set; }
        public DbSet<LiftingPlan> LiftingPlans { get; set; }
        public DbSet<LiftingPlanResource> LiftingPlanResources { get; set; }
        public DbSet<LiftingPlanEmployee> LiftingPlanEmployees { get; set; }

        public DbSet<VoyageAttachment> VoyageAttachments { get; set; }

        public DbSet<VoyageInspection> VoyageInspections { get; set; }

        public DbSet<CargoCertificate> CargoCertificates { get; set; }

        public DbSet<VoyageComment> VoyageComments { get; set; }

        public DbSet<VoyageCargoBulk> VoyageCargoBulks { get; set; }

        public DbSet<VoyageCargoInspection> VoyageCargoInspections { get; set; }

        public DbSet<VoyageCargoInspectionAttachment> VoyageCargoInspectionAttachments { get; set; }

        public DbSet<VoyageMaterialDetailSnapshot> VoyageMaterialDetailSnapshots { get; set; }

        public DbSet<VoyageCargoBulkSnapshot> VoyageCargoBulkSnapshots { get; set; }

        public DbSet<Crane> Cranes { get; set; }
        public DbSet<VoyageReservedArea> VoyageReservedAreas { get; set; }

        public DbSet<Squad> Squads { get; set; }
        public DbSet<ToolBoxTalk> ToolBoxTalks { get; set; }

        public DbSet<SquadEmployee> SquadEmployees { get; set; }
        public DbSet<ToolBoxTalkEmployee> ToolBoxTalkEmployees { get; set; }

        public DbSet<Employee> Employee { get; set; }
        public DbSet<ToolBoxTalkSite> ToolBoxTalkSites { get; set; }

        public DbSet<VoyagePlanningDetail> VoyagePlanningDetails { get; set; }
        public DbSet<VoyageSpecialNote> VoyageSpecialNotes { get; set; }

        public DbSet<CargoDescription> CargoDescriptions { get; set; }

        public DbSet<WeightCategory> WeightCategories { get; set; }

        public DbSet<Vehicle> Vehicles { get; set; }

        public DbSet<Driver> Drivers { get; set; }

        public DbSet<Pool> Pools { get; set; }

        public DbSet<VoyageCargoLoad> VoyageCargoLoads { get; set; }

        public DbSet<ClientNameHistory> ClientNameHistory { get; set; }

        #region Transport Request related



        public DbSet<TransportRequest> TransportRequests { get; set; }
        public DbSet<TransportRequestCargo> TransportRequestCargos { get; set; }
        public DbSet<TransportRequestCargoSnapshot> TransportRequestCargoSnapshots { get; set; }
        public DbSet<TransportRequestCargoDangerousGood> TransportRequestCargoDangerousGoods { get; set; }
        public DbSet<TransportRequestAttachment> TransportRequestAttachments { get; set; }
        public DbSet<TransportRequestCargoAttachment> TransportRequestCargoAttachments { get; set; }
        public DbSet<TransportRequestCargoBundling> TransportRequestCargoBundlings { get; set; }
        public DbSet<TransportRequestBulkCargoDangerousGood> TransportRequestBulkCargoDangerousGoods { get; set; }
        public DbSet<TransportRequestBulkCargoAttachment> TransportRequestBulkCargoAttachments { get; set; }
        public DbSet<TransportRequestMaterialDetailSnapshot> TransportRequestMaterialDetailSnapshots { get; set; }
        public DbSet<TransportRequestMaterialDetail> TransportRequestMaterialDetails { get; set; }
        public DbSet<TransportRequestMaterialDetailDangerousGood> TransportRequestMaterialDetailDangerousGoods { get; set; }
        public DbSet<TransportRequestmaterialDetailAttachment> TransportRequestmaterialDetailAttachments { get; set; }
        public DbSet<TransportRequestBulkCargoSnapshot> TransportRequestBulkCargoSnapshots { get; set; }
        public DbSet<TransportRequestBulkCargo> TransportRequestBulkCargos { get; set; }

        #endregion

        #region Contain related

        public DbSet<CargoFamily> CargoFamilies { get; set; }
        public DbSet<CargoSize> CargoSizes { get; set; }
        public DbSet<CargoType> CargoTypes { get; set; }
        public DbSet<HireRequestCargo> HireRequestCargos { get; set; }
        public DbSet<HireRequestCargoEvent> HireRequestCargoEvents { get; set; }
        public DbSet<MovementMatching> MovementMatchings { get; set; }
        public DbSet<HireRequest> HireRequests { get; set; }
        public DbSet<CargoEvent> CargoEvents { get; set; }

        #endregion


        protected override void OnModelCreating(ModelBuilder modelBuilder) {
            modelBuilder.Entity<User>().Property<bool>("Deleted");
            modelBuilder.Entity<User>().HasIndex(p => p.EmailAddress);
            modelBuilder.Entity<User>().HasIndex(p => p.Firstname);
            modelBuilder.Entity<User>().HasIndex(p => p.Lastname);
            modelBuilder.Entity<User>().OwnsMany(p => p.Roles, modelBuilder => modelBuilder.ToJson());
            modelBuilder.Entity<User>().HasKey(p => p.UserId);
            modelBuilder.Entity<User>().Property(p => p.Firstname)
                .HasMaxLength(255)
                .IsRequired(true);
            modelBuilder.Entity<User>().Property(p => p.Lastname)
                .HasMaxLength(255)
                .IsRequired(true);
            modelBuilder.Entity<User>().Property(p => p.EmailAddress)
                .HasMaxLength(255)
                .IsRequired(true);
            modelBuilder.Entity<User>().Property(p => p.Created)
                .IsRequired();
            modelBuilder.Entity<User>().Property(p => p.Deleted)
                .IsRequired();
            modelBuilder.Entity<User>().Ignore(p => p.Name);
            modelBuilder.Entity<User>()
                .HasOne(x => x.Client)
                .WithMany(x => x.Users)
                .HasForeignKey(x => x.ClientId);


            modelBuilder.Entity<Vessel>().Property<bool>("Deleted");
            modelBuilder.Entity<Vessel>().HasQueryFilter(x => EF.Property<bool>(x, "Deleted") == false);
            modelBuilder.Entity<Vessel>().HasKey(p => p.VesselId);
            modelBuilder.Entity<Vessel>().HasOne(p => p.CreatedBy)
                .WithMany()
                .HasForeignKey(f => f.CreatedById);
            modelBuilder.Entity<Vessel>().HasOne(p => p.UpdatedBy)
                .WithMany()
                .HasForeignKey(f => f.UpdatedById);
            modelBuilder.Entity<Vessel>().HasMany(p => p.HireStatements)
                .WithOne(p => p.Vessel)
                .HasForeignKey(f => f.VesselId);
            modelBuilder.Entity<Vessel>().HasMany(p => p.VesselTanks)
                .WithOne(p => p.Vessel)
                .HasForeignKey(f => f.VesselId);
            modelBuilder.Entity<Vessel>().Property(p => p.Imo)
                .HasMaxLength(255);
            modelBuilder.Entity<Vessel>().Property(p => p.Name)
                .HasMaxLength(255);
            modelBuilder.Entity<Vessel>().Property(p => p.Country)
                .HasMaxLength(255);
            modelBuilder.Entity<Vessel>().Property(p => p.VesselOwner)
                .HasMaxLength(255);
            modelBuilder.Entity<Vessel>().Property(p => p.LengthUnitName)
                .HasMaxLength(255);
            modelBuilder.Entity<Vessel>().Property(p => p.DeckLengthUnitName)
                .HasMaxLength(255);
            modelBuilder.Entity<Vessel>().Property(p => p.WidthUnitName)
                .HasMaxLength(255);
            modelBuilder.Entity<Vessel>().Property(p => p.DeckWidthUnitName)
                .HasMaxLength(255);
            modelBuilder.Entity<Vessel>().Property(p => p.DraftUnitName)
                .HasMaxLength(255);
            modelBuilder.Entity<Vessel>().Property(p => p.DeckCapacityUnitName)
                .HasMaxLength(255);
            modelBuilder.Entity<Vessel>().Property(p => p.GrossTonnageUnitName)
                .HasMaxLength(255);
            modelBuilder.Entity<Vessel>().Property(p => p.NetTonnageUnitName)
                .HasMaxLength(255);
            modelBuilder.Entity<Vessel>().Property(p => p.DeadWeightUnitName)
                .HasMaxLength(255);

            modelBuilder.Entity<Asset>().Property<bool>("Deleted");
            modelBuilder.Entity<Asset>().HasQueryFilter(x => EF.Property<bool>(x, "Deleted") == false);
            modelBuilder.Entity<Asset>().HasKey(p => p.AssetId);
            modelBuilder.Entity<Asset>().HasOne(p => p.CreatedBy)
                .WithMany()
                .HasForeignKey(f => f.CreatedById);
            modelBuilder.Entity<Asset>().HasOne(p => p.UpdatedBy)
                .WithMany()
                .HasForeignKey(f => f.UpdatedById);
            modelBuilder.Entity<Asset>().HasOne(p => p.Client)
                .WithMany()
                .HasForeignKey(p => p.ClientId);
            modelBuilder.Entity<Asset>().HasMany(p => p.VoyageCargoes)
                .WithOne(x => x.Asset)
                .HasForeignKey(p => p.AssetId);
            modelBuilder.Entity<Asset>().HasMany(p => p.ClientAssets)
                .WithOne(p => p.Asset)
                .HasForeignKey(p => p.AssetId);
            modelBuilder.Entity<Asset>().HasMany(p => p.ClusterHistory)
                .WithOne(p => p.ClusterHead)
                .HasForeignKey(p => p.ClusterHeadId);
            modelBuilder.Entity<Asset>().HasMany(p => p.AssetLocations)
                .WithOne(p => p.Asset)
                .HasForeignKey(p => p.AssetId);
            modelBuilder.Entity<Asset>().HasMany(p => p.VoyageCargoBulks)
                .WithOne(p => p.Asset)
                .HasForeignKey(p => p.AssetId);
            modelBuilder.Entity<Asset>()
                .HasMany(x => x.SailingRequestAssets)
                .WithOne(x => x.Asset)
                .HasForeignKey(x => x.AssetId);
            modelBuilder.Entity<Asset>().Property(p => p.Name)
                .HasMaxLength(255);
            modelBuilder.Entity<Asset>().Property(p => p.AssetType)
                .HasMaxLength(255);
            modelBuilder.Entity<Asset>().Property(p => p.Color)
                .HasMaxLength(9);

            modelBuilder.Entity<TankType>().Property<bool>("Deleted");
            modelBuilder.Entity<TankType>().HasQueryFilter(x => EF.Property<bool>(x, "Deleted") == false);
            modelBuilder.Entity<TankType>().Property(p => p.UnitName)
                .HasMaxLength(255);
            modelBuilder.Entity<TankType>().HasOne(p => p.CreatedBy)
                .WithMany()
                .HasForeignKey(f => f.CreatedById);
            modelBuilder.Entity<TankType>().HasOne(p => p.UpdatedBy)
                .WithMany()
                .HasForeignKey(f => f.UpdatedById);
            modelBuilder.Entity<TankType>().HasKey(p => p.TankTypeId);
            modelBuilder.Entity<TankType>().Property(p => p.Name)
                .HasMaxLength(255);

            modelBuilder.Entity<Distance>().Property<bool>("Deleted");
            modelBuilder.Entity<Distance>().HasQueryFilter(x => EF.Property<bool>(x, "Deleted") == false);
            modelBuilder.Entity<Distance>().HasKey(p => p.DistanceId);
            modelBuilder.Entity<Distance>().HasOne(p => p.BaseAsset)
                .WithMany()
                .HasForeignKey(p => p.BaseAssetId)
                .IsRequired();
            modelBuilder.Entity<Distance>().HasOne(p => p.ToAsset)
                .WithMany()
                .HasForeignKey(p => p.ToAssetId)
                .IsRequired();
            modelBuilder.Entity<Distance>().Property(p => p.DistanceInMiles)
                .IsRequired();
            modelBuilder.Entity<Distance>().HasOne(p => p.CreatedBy)
                .WithMany()
                .HasForeignKey(f => f.CreatedById);
            modelBuilder.Entity<Distance>().HasOne(p => p.UpdatedBy)
                .WithMany()
                .HasForeignKey(f => f.UpdatedById);

            modelBuilder.Entity<Client>().HasKey(p => p.ClientId);
            modelBuilder.Entity<Client>().HasOne(p => p.CreatedBy)
                .WithMany()
                .HasForeignKey(f => f.CreatedById);
            modelBuilder.Entity<Client>().HasOne(p => p.UpdatedBy)
                .WithMany()
                .HasForeignKey(f => f.UpdatedById);
            modelBuilder.Entity<Client>().HasMany(p => p.ClientAssets)
                .WithOne(p => p.Client)
                .HasForeignKey(p => p.ClientId);
            modelBuilder.Entity<Client>().HasMany(p => p.ClientReportTypes)
                .WithOne(p => p.Client)
                .HasForeignKey(p => p.ClientId);
            modelBuilder.Entity<Client>().HasMany(p => p.ClientLocations)
                .WithOne(p => p.Client)
                .HasForeignKey(p => p.ClientId);
            modelBuilder.Entity<Client>().HasMany(p => p.ClientNameHistory)
                .WithOne(p => p.Client)
                .HasForeignKey(p => p.ClientId);
            modelBuilder.Entity<Client>().Property(p => p.Name)
                .HasMaxLength(255);
            modelBuilder.Entity<Client>().Property(p => p.VATNumber)
                .HasMaxLength(255);
            modelBuilder.Entity<Client>().Property(p => p.EUNumber)
                .HasMaxLength(255);

            modelBuilder.Entity<Activity>().Property<bool>("Deleted");
            modelBuilder.Entity<Activity>().HasQueryFilter(x => EF.Property<bool>(x, "Deleted") == false);
            modelBuilder.Entity<Activity>().HasKey(p => p.ActivityId);
            modelBuilder.Entity<Activity>().HasOne(p => p.CreatedBy)
                .WithMany()
                .HasForeignKey(f => f.CreatedById);
            modelBuilder.Entity<Activity>().HasOne(p => p.UpdatedBy)
                .WithMany()
                .HasForeignKey(f => f.UpdatedById);
            modelBuilder.Entity<Activity>().HasMany(p => p.ParallelActivitiesOne)
                .WithOne(p => p.ActivityOne)
                .HasForeignKey(p => p.ActivityOneId);
            modelBuilder.Entity<Activity>().HasMany(p => p.ParallelActivitiesTwo)
                .WithOne(p => p.ActivityTwo)
                .HasForeignKey(p => p.ActivityTwoId);
            modelBuilder.Entity<Activity>().Property(p => p.Code)
                .HasMaxLength(255);
            modelBuilder.Entity<Activity>().Property(p => p.Name)
                .HasMaxLength(255);
            modelBuilder.Entity<Activity>().Property(p => p.Chargeability)
                .HasMaxLength(100);

            modelBuilder.Entity<BulkType>().Property<bool>("Deleted");
            modelBuilder.Entity<BulkType>().HasQueryFilter(x => EF.Property<bool>(x, "Deleted") == false);
            modelBuilder.Entity<BulkType>().HasKey(p => p.BulkTypeId);
            modelBuilder.Entity<BulkType>().HasOne(p => p.CreatedBy)
                .WithMany()
                .HasForeignKey(f => f.CreatedById);
            modelBuilder.Entity<BulkType>().HasOne(p => p.UpdatedBy)
                .WithMany()
                .HasForeignKey(f => f.UpdatedById);
            modelBuilder.Entity<BulkType>().Property(p => p.UnitName)
                .HasMaxLength(255);
            modelBuilder.Entity<BulkType>().Property(p => p.Name)
                .HasMaxLength(255);
            modelBuilder.Entity<BulkType>().Property(p => p.FluidType)
                .HasMaxLength(100);

            modelBuilder.Entity<Unit>().Property<bool>("Deleted");
            modelBuilder.Entity<Unit>().HasQueryFilter(x => EF.Property<bool>(x, "Deleted") == false);
            modelBuilder.Entity<Unit>().HasKey(p => p.UnitId);
            modelBuilder.Entity<Unit>().HasOne(p => p.CreatedBy)
                .WithMany()
                .HasForeignKey(f => f.CreatedById);
            modelBuilder.Entity<Unit>().HasOne(p => p.UpdatedBy)
                .WithMany()
                .HasForeignKey(f => f.UpdatedById);
            modelBuilder.Entity<Unit>().Property(p => p.Name)
                .HasMaxLength(255);

            modelBuilder.Entity<ReportType>().Property<bool>("Deleted");
            modelBuilder.Entity<ReportType>().HasQueryFilter(x => EF.Property<bool>(x, "Deleted") == false);
            modelBuilder.Entity<ReportType>().HasKey(p => p.ReportTypeId);
            modelBuilder.Entity<ReportType>().HasOne(p => p.CreatedBy)
                .WithMany()
                .HasForeignKey(f => f.CreatedById);
            modelBuilder.Entity<ReportType>().HasOne(p => p.UpdatedBy)
                .WithMany()
                .HasForeignKey(f => f.UpdatedById);
            modelBuilder.Entity<ReportType>().Property(p => p.Name)
                .HasMaxLength(255);
            modelBuilder.Entity<ReportType>().Property(p => p.Description)
                .HasMaxLength(255);

            modelBuilder.Entity<ClientReportType>().Property<bool>("Deleted");
            modelBuilder.Entity<ClientReportType>().HasQueryFilter(x => EF.Property<bool>(x, "Deleted") == false);
            modelBuilder.Entity<ClientReportType>().HasKey(p => p.ClientReportTypeId);
            modelBuilder.Entity<ClientReportType>().HasOne(p => p.CreatedBy)
                .WithMany()
                .HasForeignKey(f => f.CreatedById);
            modelBuilder.Entity<ClientReportType>().HasOne(p => p.UpdatedBy)
                .WithMany()
                .HasForeignKey(f => f.UpdatedById);
            modelBuilder.Entity<ClientReportType>().HasOne(p => p.Client)
                .WithMany(p => p.ClientReportTypes)
                .HasForeignKey(f => f.ClientId);
            modelBuilder.Entity<ClientReportType>().HasOne(p => p.ReportType)
                .WithMany()
                .HasForeignKey(f => f.ReportTypeId);

            modelBuilder.Entity<Voyage>().Property<bool>("Deleted");
            modelBuilder.Entity<Voyage>().HasQueryFilter(x => EF.Property<bool>(x, "Deleted") == false);
            modelBuilder.Entity<Voyage>().HasKey(p => p.VoyageId);
            modelBuilder.Entity<Voyage>().HasOne(p => p.CreatedBy)
                .WithMany()
                .HasForeignKey(f => f.CreatedById);
            modelBuilder.Entity<Voyage>().HasOne(p => p.UpdatedBy)
                .WithMany()
                .HasForeignKey(f => f.UpdatedById);
            modelBuilder.Entity<Voyage>().Navigation(x => x.CreatedBy)
                .AutoInclude();
            modelBuilder.Entity<Voyage>().Navigation(x => x.UpdatedBy)
                .AutoInclude();
            modelBuilder.Entity<Voyage>().HasOne(p => p.InitialAsset)
                .WithMany()
                .HasForeignKey(f => f.InitialAssetId);
            modelBuilder.Entity<Voyage>().HasOne(p => p.Area)
                .WithMany()
                .HasForeignKey(f => f.AreaId);
            modelBuilder.Entity<Voyage>().HasOne(p => p.ReleasedBy)
                .WithMany()
                .HasForeignKey(f => f.ReleasedById);
            modelBuilder.Entity<Voyage>().HasOne(p => p.FinalAsset)
                .WithMany()
                .HasForeignKey(f => f.FinalAssetId);
            modelBuilder.Entity<Voyage>().HasOne(p => p.Vessel)
                .WithMany()
                .HasForeignKey(f => f.VesselId);
            modelBuilder.Entity<Voyage>().HasMany(p => p.VesselActivities)
                .WithOne(p => p.Voyage)
                .HasForeignKey(f => f.VesselActivityId);
            modelBuilder.Entity<Voyage>().HasMany(p => p.VoyageBulks)
                .WithOne(p => p.Voyage)
                .HasForeignKey(f => f.VoyageId);
            modelBuilder.Entity<Voyage>().HasMany(p => p.DeckUsages)
                .WithOne(p => p.Voyage)
                .HasForeignKey(f => f.VoyageId);
            modelBuilder.Entity<Voyage>().HasMany(p => p.VoyageReservedAreas)
                .WithOne(p => p.Voyage)
                .HasForeignKey(f => f.VoyageId);
            modelBuilder.Entity<Voyage>().HasMany(p => p.LiftingPlans)
                .WithOne(p => p.Voyage)
                .HasForeignKey(f => f.VoyageId);
            modelBuilder.Entity<Voyage>().HasMany(p => p.VoyagePlanningDetails)
                .WithOne(p => p.Voyage)
                .HasForeignKey(f => f.VoyageId);
            modelBuilder.Entity<Voyage>().HasMany(p => p.VoyageSpecialNotes)
                .WithOne(p => p.Voyage)
                .HasForeignKey(f => f.VoyageId);
            modelBuilder.Entity<Voyage>().HasMany(p => p.MaterialDetails)
                .WithOne(p => p.Voyage)
                .HasForeignKey(f => f.VoyageId);
            modelBuilder.Entity<Voyage>().HasMany(p => p.BulkTransactions)
                .WithOne(p => p.Voyage)
                .HasForeignKey(f => f.VoyageId);
            modelBuilder.Entity<Voyage>().HasMany(p => p.BulkRequests)
                .WithOne(p => p.Voyage)
                .HasForeignKey(f => f.VoyageId);
            modelBuilder.Entity<Voyage>().HasMany(p => p.TankStatuses)
                .WithOne(p => p.Voyage)
                .HasForeignKey(f => f.VoyageId);
            modelBuilder.Entity<Voyage>().Property(p => p.VoyageNumber)
                .HasMaxLength(100);
            modelBuilder.Entity<Voyage>().Property(p => p.SpecialNotes)
                .HasMaxLength(100);
            modelBuilder.Entity<Voyage>().Property(p => p.Comments)
                .HasMaxLength(1023);
            modelBuilder.Entity<Voyage>().HasMany(p => p.VoyageCargoes)
                .WithOne(p => p.Voyage)
                .HasForeignKey(p => p.VoyageId);
            modelBuilder.Entity<Voyage>().HasMany(p => p.VoyageCargoBulks)
                .WithOne(p => p.Voyage)
                .HasForeignKey(p => p.VoyageId);
            modelBuilder.Entity<Voyage>().HasMany(p => p.VoyageCargoSnapshots)
                .WithOne(p => p.Voyage)
                .HasForeignKey(p => p.VoyageId);
            modelBuilder.Entity<Voyage>().HasMany(p => p.VoyageMaterialDetailSnapshots)
                .WithOne(p => p.Voyage)
                .HasForeignKey(p => p.VoyageId);
            modelBuilder.Entity<Voyage>().HasMany(p => p.VoyageCargoBulkSnapshots)
                .WithOne(p => p.Voyage)
                .HasForeignKey(p => p.VoyageId);
            modelBuilder.Entity<Voyage>().HasMany(x => x.Vendors)
                .WithOne(x => x.Voyage)
                .HasForeignKey(x => x.VoyageId)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<Voyage>().HasMany(x => x.VoyageAttachments)
                .WithOne(x => x.Voyage)
                .HasForeignKey(x => x.VoyageId)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<Voyage>().HasMany(x => x.VoyageComments)
                .WithOne(x => x.Voyage)
                .HasForeignKey(x => x.VoyageId)
                .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<VesselActivity>().Property<bool>("Deleted");
            modelBuilder.Entity<VesselActivity>().HasQueryFilter(x => EF.Property<bool>(x, "Deleted") == false);
            modelBuilder.Entity<VesselActivity>().HasKey(p => p.VesselActivityId);
            modelBuilder.Entity<VesselActivity>().HasOne(p => p.CreatedBy)
                .WithMany()
                .HasForeignKey(f => f.CreatedById);
            modelBuilder.Entity<VesselActivity>().HasOne(p => p.UpdatedBy)
                .WithMany()
                .HasForeignKey(f => f.UpdatedById);
            modelBuilder.Entity<VesselActivity>().HasOne(p => p.Voyage)
                .WithMany(p => p.VesselActivities)
                .HasForeignKey(f => f.VoyageId);
            modelBuilder.Entity<VesselActivity>().HasOne(p => p.Asset)
                .WithMany()
                .HasForeignKey(f => f.AssetId);
            modelBuilder.Entity<VesselActivity>().HasOne(p => p.Activity)
                .WithMany()
                .HasForeignKey(f => f.ActivityId);
            modelBuilder.Entity<VesselActivity>().HasOne(p => p.Client)
                .WithMany()
                .HasForeignKey(f => f.ClientId);
            modelBuilder.Entity<VesselActivity>().HasOne(p => p.BilledAsset)
                .WithMany()
                .HasForeignKey(f => f.BilledAssetId);
            modelBuilder.Entity<VesselActivity>().Property(p => p.Comments)
                .HasMaxLength(1023);
            modelBuilder.Entity<VesselActivity>().Property(p => p.InvalidErrors)
                .HasMaxLength(4096);

            modelBuilder.Entity<BulkTransaction>().Property<bool>("Deleted");
            modelBuilder.Entity<BulkTransaction>().HasQueryFilter(x => EF.Property<bool>(x, "Deleted") == false);
            modelBuilder.Entity<BulkTransaction>().HasKey(p => p.BulkTransactionId);
            modelBuilder.Entity<BulkTransaction>().HasOne(p => p.CreatedBy)
                .WithMany()
                .HasForeignKey(f => f.CreatedById);
            modelBuilder.Entity<BulkTransaction>().HasOne(p => p.UpdatedBy)
                .WithMany()
                .HasForeignKey(f => f.UpdatedById);
            modelBuilder.Entity<BulkTransaction>().HasOne(p => p.Voyage)
                .WithMany(p => p.BulkTransactions)
                .HasForeignKey(f => f.VoyageId);
            modelBuilder.Entity<BulkTransaction>().HasOne(p => p.Client)
                .WithMany()
                .HasForeignKey(f => f.ClientId);
            modelBuilder.Entity<BulkTransaction>().HasOne(p => p.BulkType)
                .WithMany()
                .HasForeignKey(f => f.BulkTypeId);
            modelBuilder.Entity<BulkTransaction>().HasOne(p => p.TankType)
                .WithMany()
                .HasForeignKey(f => f.TankTypeId);
            modelBuilder.Entity<BulkTransaction>().Property(p => p.Comments)
                .HasMaxLength(1023);
            modelBuilder.Entity<BulkTransaction>().Property(p => p.TransactionType)
                .HasMaxLength(100);
            modelBuilder.Entity<BulkTransaction>().Property(p => p.BulkTransactionNumber)
                .HasMaxLength(100);
            modelBuilder.Entity<BulkTransaction>().Property(p => p.DelTicket)
                .HasMaxLength(100);

            modelBuilder.Entity<TankStatus>().Property<bool>("Deleted");
            modelBuilder.Entity<TankStatus>().HasQueryFilter(x => EF.Property<bool>(x, "Deleted") == false);
            modelBuilder.Entity<TankStatus>().HasKey(p => p.TankStatusId);
            modelBuilder.Entity<TankStatus>().HasOne(p => p.CreatedBy)
                .WithMany()
                .HasForeignKey(f => f.CreatedById);
            modelBuilder.Entity<TankStatus>().HasOne(p => p.UpdatedBy)
                .WithMany()
                .HasForeignKey(f => f.UpdatedById);
            modelBuilder.Entity<TankStatus>().HasOne(p => p.Voyage)
                .WithMany(p => p.TankStatuses)
                .HasForeignKey(f => f.VoyageId);
            modelBuilder.Entity<TankStatus>().HasOne(p => p.BulkTransaction)
                .WithMany()
                .HasForeignKey(f => f.BulkTransactionId);
            modelBuilder.Entity<TankStatus>().Property(p => p.WellNumber)
                .HasMaxLength(100);
            modelBuilder.Entity<TankStatus>().Property(p => p.Comments)
                .HasMaxLength(1023);
            modelBuilder.Entity<TankStatus>().Property(p => p.RecordingType)
                .HasMaxLength(100);
            modelBuilder.Entity<TankStatus>().Property(p => p.Status)
                .HasMaxLength(100);
            modelBuilder.Entity<TankStatus>().Property(p => p.TankStatusNumber)
                .HasMaxLength(100);

            modelBuilder.Entity<ClientAsset>().Property<bool>("Deleted");
            modelBuilder.Entity<ClientAsset>().HasQueryFilter(x => EF.Property<bool>(x, "Deleted") == false);
            modelBuilder.Entity<ClientAsset>().HasKey(p => p.ClientAssetId);
            modelBuilder.Entity<ClientAsset>().HasOne(p => p.CreatedBy)
                .WithMany()
                .HasForeignKey(f => f.CreatedById);
            modelBuilder.Entity<ClientAsset>().HasOne(p => p.UpdatedBy)
                .WithMany()
                .HasForeignKey(f => f.UpdatedById);
            modelBuilder.Entity<ClientAsset>().HasOne(p => p.Client)
                .WithMany(p => p.ClientAssets)
                .HasForeignKey(f => f.ClientId);
            modelBuilder.Entity<ClientAsset>().HasOne(p => p.Asset)
                .WithMany(p => p.ClientAssets)
                .HasForeignKey(f => f.AssetId);

            modelBuilder.Entity<HireStatement>().Property<bool>("Deleted");
            modelBuilder.Entity<HireStatement>().HasQueryFilter(x => EF.Property<bool>(x, "Deleted") == false);
            modelBuilder.Entity<HireStatement>().HasKey(p => p.HireStatementId);
            modelBuilder.Entity<HireStatement>().HasOne(p => p.CreatedBy)
                .WithMany()
                .HasForeignKey(f => f.CreatedById);
            modelBuilder.Entity<HireStatement>().HasOne(p => p.UpdatedBy)
                .WithMany()
                .HasForeignKey(f => f.UpdatedById);
            modelBuilder.Entity<HireStatement>().HasOne(p => p.Vessel)
                .WithMany(p => p.HireStatements)
                .HasForeignKey(f => f.VesselId);
            modelBuilder.Entity<HireStatement>().HasMany(p => p.HireStatementBulks)
                .WithOne()
                .HasForeignKey(f => f.HireStatementId);
            modelBuilder.Entity<HireStatement>().Property(p => p.DeliveryPlace)
                .HasMaxLength(255);
            modelBuilder.Entity<HireStatement>().Property(p => p.RedeliveryPlace)
                .HasMaxLength(255);
            modelBuilder.Entity<HireStatement>().Property(p => p.Type)
                .HasMaxLength(100);

            modelBuilder.Entity<HireStatementBulk>().Property<bool>("Deleted");
            modelBuilder.Entity<HireStatementBulk>().HasQueryFilter(x => EF.Property<bool>(x, "Deleted") == false);
            modelBuilder.Entity<HireStatementBulk>().HasKey(p => p.HireStatementBulkId);
            modelBuilder.Entity<HireStatementBulk>().HasOne(p => p.CreatedBy)
                .WithMany()
                .HasForeignKey(f => f.CreatedById);
            modelBuilder.Entity<HireStatementBulk>().HasOne(p => p.UpdatedBy)
                .WithMany()
                .HasForeignKey(f => f.UpdatedById);
            modelBuilder.Entity<HireStatementBulk>().HasOne(p => p.BulkType)
                .WithMany()
                .HasForeignKey(f => f.BulkTypeId);

            modelBuilder.Entity<DeckUsage>().Property<bool>("Deleted");
            modelBuilder.Entity<DeckUsage>().HasQueryFilter(x => EF.Property<bool>(x, "Deleted") == false);
            modelBuilder.Entity<DeckUsage>().HasKey(p => p.DeckUsageId);
            modelBuilder.Entity<DeckUsage>().HasOne(p => p.Voyage)
                .WithMany(p => p.DeckUsages)
                .HasForeignKey(f => f.VoyageId);
            modelBuilder.Entity<DeckUsage>().HasOne(p => p.Client)
                .WithMany()
                .HasForeignKey(f => f.ClientId);
            modelBuilder.Entity<DeckUsage>().HasOne(p => p.Asset)
                .WithMany()
                .HasForeignKey(f => f.AssetId);

            modelBuilder.Entity<MobileWell>().Property<bool>("Deleted");
            modelBuilder.Entity<MobileWell>().HasQueryFilter(x => EF.Property<bool>(x, "Deleted") == false);
            modelBuilder.Entity<MobileWell>().HasKey(p => p.MobileWellId);
            modelBuilder.Entity<MobileWell>().HasOne(p => p.CreatedBy)
                .WithMany()
                .HasForeignKey(f => f.CreatedById);
            modelBuilder.Entity<MobileWell>().HasOne(p => p.UpdatedBy)
                .WithMany()
                .HasForeignKey(f => f.UpdatedById);
            modelBuilder.Entity<MobileWell>().HasOne(p => p.Mobile)
                .WithMany()
                .HasForeignKey(f => f.MobileId);
            modelBuilder.Entity<MobileWell>().HasOne(p => p.Well)
                .WithMany()
                .HasForeignKey(f => f.WellId);

            modelBuilder.Entity<AuditLog>().HasKey(p => p.AuditLogId);
            modelBuilder.Entity<AuditLog>().HasOne(p => p.User)
                .WithMany()
                .HasForeignKey(f => f.UserId);
            modelBuilder.Entity<AuditLog>().Property(p => p.TableName)
                .HasMaxLength(100);
            modelBuilder.Entity<AuditLog>().Property(p => p.TypeOfAction)
                .HasMaxLength(100);
            modelBuilder.Entity<AuditLog>().Property(p => p.PrimaryIndicator)
                .HasMaxLength(1023);
            modelBuilder.Entity<AuditLog>().Property(p => p.FieldName)
                .HasMaxLength(100);
            modelBuilder.Entity<AuditLog>().Property(p => p.OldValue)
                .HasMaxLength(100);
            modelBuilder.Entity<AuditLog>().Property(p => p.NewValue)
                .HasMaxLength(100);

            modelBuilder.Entity<ParallelActivity>().HasKey(p => p.ParallelActivityId);
            modelBuilder.Entity<ParallelActivity>()
                .HasOne(a => a.ActivityOne)
                .WithMany(a => a.ParallelActivitiesOne)
                .HasForeignKey(pa => pa.ActivityOneId);
            modelBuilder.Entity<ParallelActivity>()
                .HasOne(a => a.ActivityTwo)
                .WithMany(a => a.ParallelActivitiesTwo)
                .HasForeignKey(pa => pa.ActivityTwoId);

            modelBuilder.Entity<BillingPeriod>().HasKey(p => p.BillingPeriodId);
            modelBuilder.Entity<BillingPeriod>().HasMany(p => p.VoyageBillingPeriods)
                .WithOne(p => p.BillingPeriod)
                .HasForeignKey(f => f.BillingPeriodId);
            modelBuilder.Entity<BillingPeriod>().Property(p => p.BillingPeriodMonth)
                .HasMaxLength(20);
            modelBuilder.Entity<BillingPeriod>().Property(p => p.Status)
                .HasMaxLength(100);

            modelBuilder.Entity<BillingPeriodDocument>().HasKey(p => p.BillingPeriodDocumentId);
            modelBuilder.Entity<BillingPeriodDocument>().HasOne(p => p.BillingPeriod)
                .WithMany()
                .HasForeignKey(f => f.BillingPeriodId);
            modelBuilder.Entity<BillingPeriodDocument>().HasOne(p => p.CreatedBy)
                .WithMany()
                .HasForeignKey(f => f.CreatedById);
            modelBuilder.Entity<BillingPeriodDocument>().Property(p => p.FileName)
                .HasMaxLength(255);
            modelBuilder.Entity<BillingPeriodDocument>().Property(p => p.Comments)
                .HasMaxLength(1023);

            modelBuilder.Entity<ClientBillingPeriodTimeAllocation>().HasKey(p => p.ClientBillingPeriodTimeAllocationId);
            modelBuilder.Entity<ClientBillingPeriodTimeAllocation>().HasOne(p => p.BillingPeriod)
                .WithMany()
                .HasForeignKey(f => f.BillingPeriodId);
            modelBuilder.Entity<ClientBillingPeriodTimeAllocation>().HasOne(p => p.Client)
                .WithMany()
                .HasForeignKey(f => f.ClientId);

            modelBuilder.Entity<Setting>().HasKey(p => p.SettingId);
            modelBuilder.Entity<Setting>().Property(p => p.Currency)
                .HasMaxLength(20);
            modelBuilder.Entity<Setting>().HasOne(p => p.SettingsDefaultInitialPort)
                .WithMany()
                .HasForeignKey(p => p.SettingsDefaultInitialPortId)
                .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<VesselTank>().HasKey(p => p.VesselTankId);
            modelBuilder.Entity<VesselTank>().HasOne(p => p.CreatedBy)
                .WithMany()
                .HasForeignKey(f => f.CreatedById);
            modelBuilder.Entity<VesselTank>().HasOne(p => p.UpdatedBy)
                .WithMany()
                .HasForeignKey(f => f.UpdatedById);
            modelBuilder.Entity<VesselTank>().HasOne(p => p.TankType)
                .WithMany()
                .HasForeignKey(f => f.TankTypeId);
            modelBuilder.Entity<VesselTank>().HasOne(p => p.Vessel)
                .WithMany(p => p.VesselTanks)
                .HasForeignKey(f => f.VesselId);
            modelBuilder.Entity<VesselTank>().HasOne(p => p.BulkType)
                .WithMany()
                .HasForeignKey(f => f.BulkTypeId);
            modelBuilder.Entity<VesselTank>().Property(p => p.Name)
                .HasMaxLength(255);

            modelBuilder.Entity<VoyageBillingPeriod>().HasKey(p => p.VoyageBillingPeriodId);
            modelBuilder.Entity<VoyageBillingPeriod>().HasOne(p => p.BillingPeriod)
                .WithMany(p => p.VoyageBillingPeriods)
                .HasForeignKey(f => f.BillingPeriodId);
            modelBuilder.Entity<VoyageBillingPeriod>().HasOne(p => p.Voyage)
                .WithMany()
                .HasForeignKey(f => f.VoyageId);

            modelBuilder.Entity<VoyageBulk>().HasKey(p => p.VoyageBulkId);
            modelBuilder.Entity<VoyageBulk>().HasOne(p => p.BulkType)
                .WithMany()
                .HasForeignKey(f => f.BulkTypeId);
            modelBuilder.Entity<VoyageBulk>().HasOne(p => p.Voyage)
                .WithMany(x => x.VoyageBulks)
                .HasForeignKey(f => f.VoyageId);

            modelBuilder.Entity<VoyageBulkQuantities>().HasKey(p => p.VoyageBulkQuantitiesId);
            modelBuilder.Entity<VoyageBulkQuantities>().HasOne(p => p.BulkType)
                .WithMany()
                .HasForeignKey(f => f.BulkTypeId);
            modelBuilder.Entity<VoyageBulkQuantities>().HasOne(p => p.Voyage)
                .WithMany()
                .HasForeignKey(f => f.VoyageId);

            modelBuilder.Entity<SailingRequest>().HasKey(p => p.SailingRequestId);
            modelBuilder.Entity<SailingRequest>().Property<bool>("Deleted");
            modelBuilder.Entity<SailingRequest>().HasQueryFilter(x => EF.Property<bool>(x, "Deleted") == false);
            modelBuilder.Entity<SailingRequest>()
                .HasMany(x => x.SailingRequestActivities)
                .WithOne(x => x.SailingRequest)
                .HasForeignKey(x => x.SailingRequestId);
            modelBuilder.Entity<SailingRequest>()
                .HasMany(x => x.SailingRequestAssets)
                .WithOne(x => x.SailingRequest)
                .HasForeignKey(x => x.SailingRequestId);
            modelBuilder.Entity<SailingRequest>()
                .HasMany(x => x.SailingRequestUserComments)
                .WithOne(x => x.SailingRequest)
                .HasForeignKey(x => x.SailingRequestId);
            modelBuilder.Entity<SailingRequest>()
                .HasMany(x => x.TransportRequests)
                .WithOne(x => x.SailingRequest)
                .HasForeignKey(x => x.SailingRequestId)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<SailingRequest>()
                .HasMany(x => x.Children)
                .WithOne(x => x.Parent)
                .HasForeignKey(x => x.ParentId);
            modelBuilder.Entity<SailingRequest>().HasOne(p => p.CreatedBy)
                .WithMany()
                .HasForeignKey(f => f.CreatedById);
            modelBuilder.Entity<SailingRequest>().HasOne(p => p.UpdatedBy)
                .WithMany()
                .HasForeignKey(f => f.UpdatedById);
            modelBuilder.Entity<SailingRequest>().HasOne(p => p.Vessel)
                .WithMany()
                .HasForeignKey(f => f.VesselId);
            modelBuilder.Entity<SailingRequest>().HasOne(p => p.Client)
                .WithMany()
                .HasForeignKey(f => f.ClientId);
            modelBuilder.Entity<SailingRequest>().HasOne(p => p.OutboundVoyage)
                .WithMany()
                .HasForeignKey(f => f.OutboundVoyageId);
            modelBuilder.Entity<SailingRequest>().HasOne(p => p.InboundVoyage)
                .WithMany()
                .HasForeignKey(f => f.InboundVoyageId);
            modelBuilder.Entity<SailingRequest>().HasOne(p => p.Location)
                .WithMany(x => x.SailingRequests)
                .HasForeignKey(f => f.LocationId);
            modelBuilder.Entity<SailingRequest>().HasOne(p => p.Cluster)
                .WithMany()
                .HasForeignKey(f => f.ClusterID);
            modelBuilder.Entity<SailingRequest>().Property(p => p.FirstInstallationTime).IsRequired(false);
            modelBuilder.Entity<SailingRequest>().Property(p => p.WeeklyPattern)
                .HasMaxLength(56);
            modelBuilder.Entity<SailingRequest>().Property(p => p.ClientReference)
                .HasMaxLength(255);
            modelBuilder.Entity<SailingRequest>().Property(p => p.Remarks)
                .HasMaxLength(255);
            modelBuilder.Entity<SailingRequest>().Property(p => p.Comment)
                .HasMaxLength(1023);
            modelBuilder.Entity<SailingRequest>().Property(p => p.TimeUnit)
                .HasMaxLength(255);
            modelBuilder.Entity<SailingRequest>().Property(p => p.LatestArrivalTime).IsRequired(false);

            modelBuilder.Entity<SailingRequestActivity>().Property<bool>("Deleted");
            modelBuilder.Entity<SailingRequestActivity>().HasQueryFilter(x => EF.Property<bool>(x, "Deleted") == false);
            modelBuilder.Entity<SailingRequestActivity>().HasKey(p => p.SailingRequestActivityId);
            modelBuilder.Entity<SailingRequestActivity>().HasOne(p => p.SailingRequest)
                .WithMany(x => x.SailingRequestActivities)
                .HasForeignKey(f => f.SailingRequestId);
            modelBuilder.Entity<SailingRequestActivity>().HasOne(p => p.DependantActivity)
                .WithOne()
                .HasForeignKey<SailingRequestActivity>(f => f.DependantActivityId)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<SailingRequestActivity>().HasOne(p => p.ActivityCategoryType)
                .WithMany()
                .HasForeignKey(f => f.ActivityCategoryTypeId);
            modelBuilder.Entity<SailingRequestActivity>().HasOne(p => p.Area)
                .WithMany()
                .HasForeignKey(f => f.AreaId);
            modelBuilder.Entity<SailingRequestActivity>().HasOne(p => p.Asset)
                .WithMany()
                .HasForeignKey(f => f.AssetId);
            modelBuilder.Entity<SailingRequestActivity>().HasOne(p => p.CreatedBy)
                .WithMany()
                .HasForeignKey(f => f.CreatedById);
            modelBuilder.Entity<SailingRequestActivity>().HasOne(p => p.UpdatedBy)
                .WithMany()
                .HasForeignKey(f => f.UpdatedById);
            modelBuilder.Entity<SailingRequestActivity>().Property(p => p.Notes)
                .HasMaxLength(255);
            modelBuilder.Entity<SailingRequestActivity>().Property(p => p.UnitName)
                .HasMaxLength(255);
            modelBuilder.Entity<SailingRequestActivity>().HasMany(p => p.VoyageCargoSailingRequestActivities)
                .WithOne(x => x.SailingRequestActivity)
                .HasForeignKey(x => x.SailingRequestActivityId);

            modelBuilder.Entity<SailingRequestAsset>().Property<bool>("Deleted");
            modelBuilder.Entity<SailingRequestAsset>().HasQueryFilter(x => EF.Property<bool>(x, "Deleted") == false);
            modelBuilder.Entity<SailingRequestAsset>().HasKey(p => p.SailingRequestAssetId);
            modelBuilder.Entity<SailingRequestAsset>().HasOne(p => p.SailingRequest)
                .WithMany(x => x.SailingRequestAssets)
                .HasForeignKey(f => f.SailingRequestId);
            modelBuilder.Entity<SailingRequestAsset>().HasOne(p => p.Asset)
                .WithMany(x => x.SailingRequestAssets)
                .HasForeignKey(f => f.AssetId);
            modelBuilder.Entity<SailingRequestAsset>().HasOne(p => p.CreatedBy)
                .WithMany()
                .HasForeignKey(f => f.CreatedById);
            modelBuilder.Entity<SailingRequestAsset>().HasOne(p => p.UpdatedBy)
                .WithMany()
                .HasForeignKey(f => f.UpdatedById);

            modelBuilder.Entity<ActivityCategory>().HasKey(p => p.ActivityCategoryId);
            modelBuilder.Entity<ActivityCategory>().Property<bool>("Deleted");
            modelBuilder.Entity<ActivityCategory>().HasQueryFilter(x => EF.Property<bool>(x, "Deleted") == false);
            modelBuilder.Entity<ActivityCategory>().HasOne(p => p.CreatedBy)
                .WithMany()
                .HasForeignKey(f => f.CreatedById);
            modelBuilder.Entity<ActivityCategory>()
                .HasMany(x => x.ActivityCategoryTypes)
                .WithOne(x => x.ActivityCategory)
                .HasForeignKey(x => x.ActivityCategoryId);

            modelBuilder.Entity<ActivityCategoryType>().HasKey(p => p.ActivityCategoryTypeId);
            modelBuilder.Entity<ActivityCategoryType>().Property<bool>("Deleted");
            modelBuilder.Entity<ActivityCategoryType>().HasQueryFilter(x => EF.Property<bool>(x, "Deleted") == false);
            modelBuilder.Entity<ActivityCategoryType>().HasOne(p => p.CreatedBy)
                .WithMany()
                .HasForeignKey(f => f.CreatedById);
            modelBuilder.Entity<ActivityCategoryType>().HasOne(p => p.UpdatedBy)
                .WithMany()
                .HasForeignKey(f => f.UpdatedById);


            modelBuilder.Entity<AssetLocation>().Property<bool>("Deleted");
            modelBuilder.Entity<AssetLocation>().HasQueryFilter(x => EF.Property<bool>(x, "Deleted") == false);
            modelBuilder.Entity<AssetLocation>().HasKey(p => p.AssetLocationId);
            modelBuilder.Entity<AssetLocation>().HasOne(p => p.CreatedBy)
                .WithMany()
                .HasForeignKey(f => f.CreatedById);
            modelBuilder.Entity<AssetLocation>().HasOne(p => p.UpdatedBy)
                .WithMany()
                .HasForeignKey(f => f.UpdatedById);
            modelBuilder.Entity<AssetLocation>().HasOne(p => p.Location)
                .WithMany(p => p.AssetLocations)
                .HasForeignKey(f => f.LocationId);
            modelBuilder.Entity<AssetLocation>().HasOne(p => p.Asset)
                .WithMany(p => p.AssetLocations)
                .HasForeignKey(f => f.AssetId);

            modelBuilder.Entity<ClientLocation>().Property<bool>("Deleted");
            modelBuilder.Entity<ClientLocation>().HasQueryFilter(x => EF.Property<bool>(x, "Deleted") == false);
            modelBuilder.Entity<ClientLocation>().HasKey(p => p.ClientLocationId);
            modelBuilder.Entity<ClientLocation>().HasOne(p => p.CreatedBy)
                .WithMany()
                .HasForeignKey(f => f.CreatedById);
            modelBuilder.Entity<ClientLocation>().HasOne(p => p.UpdatedBy)
                .WithMany()
                .HasForeignKey(f => f.UpdatedById);
            modelBuilder.Entity<ClientLocation>().HasOne(p => p.Location)
                .WithMany(p => p.ClientLocations)
                .HasForeignKey(f => f.LocationId);
            modelBuilder.Entity<ClientLocation>().HasOne(p => p.Client)
                .WithMany(p => p.ClientLocations)
                .HasForeignKey(f => f.ClientId);

            modelBuilder.Entity<Location>().Property<bool>("Deleted");
            modelBuilder.Entity<Location>().HasQueryFilter(x => EF.Property<bool>(x, "Deleted") == false);
            modelBuilder.Entity<Location>().HasKey(p => p.LocationId);
            modelBuilder.Entity<Location>().HasOne(p => p.CreatedBy)
                .WithMany()
                .HasForeignKey(f => f.CreatedById);
            modelBuilder.Entity<Location>().HasOne(p => p.UpdatedBy)
                .WithMany()
                .HasForeignKey(f => f.UpdatedById);
            modelBuilder.Entity<Location>().HasMany(p => p.AssetLocations)
                .WithOne(p => p.Location)
                .HasForeignKey(p => p.LocationId)
                .HasForeignKey(p => p.LocationId);
            modelBuilder.Entity<Location>().HasMany(p => p.ClientLocations)
                .WithOne(p => p.Location)
                .HasForeignKey(p => p.LocationId);
            modelBuilder.Entity<Location>().Property(p => p.Name)
                .HasMaxLength(255);
            modelBuilder.Entity<Location>().Property(p => p.WasteCarrierCode)
                .HasMaxLength(50);
            modelBuilder.Entity<Location>().Property(p => p.BusinessHoursFrom)
                .HasMaxLength(5);
            modelBuilder.Entity<Location>().Property(p => p.BusinessHoursTo)
                .HasMaxLength(5);
            modelBuilder.Entity<Location>().Property(p => p.TimeZoneInfoId)
                .HasMaxLength(50);
            modelBuilder.Entity<Location>()
                .HasMany(x => x.Sites)
                .WithOne(x => x.Location)
                .HasForeignKey(x => x.LocationId);
            modelBuilder.Entity<Location>()
                .HasMany(x => x.Cargoes)
                .WithOne(x => x.Location)
                .HasForeignKey(x => x.LocationId);
            modelBuilder.Entity<Location>()
                .HasMany(x => x.Districts)
                .WithOne(x => x.Location)
                .HasForeignKey(x => x.LocationId);

            modelBuilder.Entity<Site>().HasKey(p => p.SiteId);
            modelBuilder.Entity<Site>().Property<bool>("Deleted");
            modelBuilder.Entity<Site>().HasQueryFilter(x => EF.Property<bool>(x, "Deleted") == false);
            modelBuilder.Entity<Site>().HasOne(p => p.CreatedBy)
                .WithMany()
                .HasForeignKey(f => f.CreatedById);
            modelBuilder.Entity<Site>().HasOne(p => p.UpdatedBy)
                .WithMany()
                .HasForeignKey(f => f.UpdatedById);
            modelBuilder.Entity<Site>()
                .HasMany(x => x.Areas)
                .WithOne(x => x.Site)
                .HasForeignKey(x => x.SiteId);
            modelBuilder.Entity<Site>()
                .HasMany(x => x.ToolBoxTalkSites)
                .WithOne(x => x.Site)
                .HasForeignKey(x => x.SiteId);
            modelBuilder.Entity<Site>().Property(p => p.Name)
                .HasMaxLength(255);

            modelBuilder.Entity<Area>().HasKey(p => p.AreaId);
            modelBuilder.Entity<Area>().Property<bool>("Deleted");
            modelBuilder.Entity<Area>().HasQueryFilter(x => EF.Property<bool>(x, "Deleted") == false);
            modelBuilder.Entity<Area>().HasOne(p => p.CreatedBy)
                .WithMany()
                .HasForeignKey(f => f.CreatedById);
            modelBuilder.Entity<Area>().HasOne(p => p.UpdatedBy)
                .WithMany()
                .HasForeignKey(f => f.UpdatedById);
            modelBuilder.Entity<Area>().Property(p => p.Name)
                .HasMaxLength(255);
            modelBuilder.Entity<Area>().HasMany(p => p.AreaBlockingActivities)
                .WithOne(p => p.Area)
                .HasForeignKey(p => p.AreaId);
            
            modelBuilder.Entity<District>().HasKey(p => p.DistrictId);
            modelBuilder.Entity<District>().Property<bool>("Deleted");
            modelBuilder.Entity<District>().HasQueryFilter(x => EF.Property<bool>(x, "Deleted") == false);
            modelBuilder.Entity<District>().HasOne(p => p.CreatedBy)
                .WithMany()
                .HasForeignKey(f => f.CreatedById);
            modelBuilder.Entity<District>().HasOne(p => p.UpdatedBy)
                .WithMany()
                .HasForeignKey(f => f.UpdatedById);
            modelBuilder.Entity<District>().Navigation(x => x.CreatedBy)
                .AutoInclude();
            modelBuilder.Entity<District>().Navigation(x => x.UpdatedBy)
                .AutoInclude();
            modelBuilder.Entity<District>().Property(p => p.DistrictName)
                .HasMaxLength(50);

            modelBuilder.Entity<AreaBlockingActivity>().HasKey(p => p.AreaBlockingActivityId);
            modelBuilder.Entity<AreaBlockingActivity>().Property<bool>("Deleted");
            modelBuilder.Entity<AreaBlockingActivity>().HasQueryFilter(x => EF.Property<bool>(x, "Deleted") == false);
            modelBuilder.Entity<AreaBlockingActivity>().HasOne(p => p.CreatedBy)
                .WithMany()
                .HasForeignKey(f => f.CreatedById);
            modelBuilder.Entity<AreaBlockingActivity>().HasOne(p => p.UpdatedBy)
                .WithMany()
                .HasForeignKey(f => f.UpdatedById);
            modelBuilder.Entity<AreaBlockingActivity>().HasOne(p => p.BlockingActivity)
                .WithMany()
                .HasForeignKey(p => p.BlockingActivityId);

            modelBuilder.Entity<BlockingActivity>().HasKey(p => p.BlockingActivityId);
            modelBuilder.Entity<BlockingActivity>().Property<bool>("Deleted");
            modelBuilder.Entity<BlockingActivity>().HasQueryFilter(x => EF.Property<bool>(x, "Deleted") == false);
            modelBuilder.Entity<BlockingActivity>().HasOne(p => p.CreatedBy)
                .WithMany()
                .HasForeignKey(f => f.CreatedById);
            modelBuilder.Entity<BlockingActivity>().HasOne(p => p.UpdatedBy)
                .WithMany()
                .HasForeignKey(f => f.UpdatedById);
            modelBuilder.Entity<BlockingActivity>().Property(p => p.Name)
                .HasMaxLength(255);

            modelBuilder.Entity<SailingRequestUserComment>().Property<bool>("Deleted");
            modelBuilder.Entity<SailingRequestUserComment>().HasQueryFilter(x => EF.Property<bool>(x, "Deleted") == false);
            modelBuilder.Entity<SailingRequestUserComment>().HasKey(p => p.SailingRequestUserCommentId);
            modelBuilder.Entity<SailingRequestUserComment>().HasOne(p => p.SailingRequest)
               .WithMany(f => f.SailingRequestUserComments)
               .HasForeignKey(f => f.SailingRequestId);
            modelBuilder.Entity<SailingRequestUserComment>()
                .HasMany(x => x.CommentReadByUsers)
                .WithOne(x => x.SailingRequestUserComment)
                .HasForeignKey(x => x.SailingRequestUserCommentId);
            modelBuilder.Entity<SailingRequestUserComment>().HasOne(p => p.CreatedByUser)
               .WithMany()
               .HasForeignKey(f => f.CreatedByUserId);
            modelBuilder.Entity<SailingRequestUserComment>().Property(p => p.Comment)
                .HasColumnType("nvarchar(max)");

            modelBuilder.Entity<CommentReadByUser>().Property<bool>("Deleted");
            modelBuilder.Entity<CommentReadByUser>().HasQueryFilter(x => EF.Property<bool>(x, "Deleted") == false);
            modelBuilder.Entity<CommentReadByUser>().HasKey(p => p.CommentReadByUserId);
            modelBuilder.Entity<CommentReadByUser>().HasOne(p => p.SailingRequestUserComment)
               .WithMany(f => f.CommentReadByUsers)
               .HasForeignKey(f => f.SailingRequestUserCommentId);
            modelBuilder.Entity<CommentReadByUser>().HasOne(p => p.ReaderUser)
               .WithMany()
               .HasForeignKey(f => f.ReaderUserId);

            modelBuilder.Entity<Cargo>().HasKey(x => x.CargoId);
            modelBuilder.Entity<Cargo>().Property(x => x.CCUId)
                .HasMaxLength(255)
                .IsRequired();
            modelBuilder.Entity<Cargo>().Property(x => x.IsApproved)
                .IsRequired();
            modelBuilder.Entity<Cargo>().Navigation(x => x.CreatedBy)
                .AutoInclude();
            modelBuilder.Entity<Cargo>().Navigation(x => x.UpdatedBy)
                .AutoInclude();
            modelBuilder.Entity<Cargo>().Property<bool>("Deleted");
            modelBuilder.Entity<Cargo>().Property<bool>("IsApproved");
            modelBuilder.Entity<Cargo>().HasQueryFilter(x => !EF.Property<bool>(x, "Deleted") && EF.Property<bool>(x, "IsApproved"));
            modelBuilder.Entity<Cargo>().HasOne(p => p.CreatedBy)
                .WithMany()
                .HasForeignKey(f => f.CreatedById);
            modelBuilder.Entity<Cargo>().HasOne(p => p.UpdatedBy)
                .WithMany()
                .HasForeignKey(f => f.UpdatedById);
            modelBuilder.Entity<Cargo>().HasOne(p => p.Location)
                .WithMany(x => x.Cargoes)
                .HasForeignKey(f => f.LocationId);
            modelBuilder.Entity<Cargo>().HasOne(p => p.Vendor)
                .WithMany()
                .HasForeignKey(f => f.VendorId);
            modelBuilder.Entity<Cargo>().Navigation(x => x.CargoFamily)
                .AutoInclude();
            modelBuilder.Entity<Cargo>().HasOne(p => p.CargoFamily)
                .WithMany()
                .HasForeignKey(f => f.FamilyId)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<Cargo>().Navigation(x => x.CargoSize)
                .AutoInclude();
            modelBuilder.Entity<Cargo>().HasOne(p => p.CargoSize)
                .WithMany()
                .HasForeignKey(f => f.SizeId)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<Cargo>().Navigation(x => x.CargoType)
                .AutoInclude();
            modelBuilder.Entity<Cargo>().HasOne(p => p.CargoType)
                .WithMany()
                .HasForeignKey(f => f.TypeId)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<Cargo>().HasMany(p => p.CargoCertificates)
                .WithOne(x => x.Cargo)
                .HasForeignKey(f => f.CargoId)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<Cargo>().HasOne(p => p.CargoDescription)
                .WithMany()
                .HasForeignKey(f => f.CargoDescriptionId)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<Cargo>().Property(p => p.CargoStatus).HasDefaultValue(null);
            modelBuilder.Entity<Cargo>().Property(p => p.CcuHireStatus).IsRequired().HasDefaultValue(CargoHireStatus.OffHire);
            modelBuilder.Entity<Cargo>().HasOne(p => p.Pool)
                .WithMany()
                .HasForeignKey(f => f.PoolId)
                .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<CargoDescription>().HasKey(x => x.CargoDescriptionId);
            modelBuilder.Entity<CargoDescription>().Property<bool>("Deleted");
            modelBuilder.Entity<CargoDescription>().HasQueryFilter(x => EF.Property<bool>(x, "Deleted") == false);
            modelBuilder.Entity<CargoDescription>().HasQueryFilter(x => EF.Property<bool>(x, "IsAdhoc") == false);
            modelBuilder.Entity<CargoDescription>().Navigation(x => x.CreatedBy)
                .AutoInclude();
            modelBuilder.Entity<CargoDescription>().Navigation(x => x.UpdatedBy)
                .AutoInclude();
            modelBuilder.Entity<CargoDescription>().HasOne(p => p.CreatedBy)
                .WithMany()
                .HasForeignKey(f => f.CreatedById);
            modelBuilder.Entity<CargoDescription>().HasOne(p => p.UpdatedBy)
                .WithMany()
                .HasForeignKey(f => f.UpdatedById);
            modelBuilder.Entity<CargoDescription>().Property(p => p.Description)
                .HasMaxLength(100);

            modelBuilder.Entity<VoyageCargo>().HasKey(x => x.VoyageCargoId);
            modelBuilder.Entity<VoyageCargo>().Property<bool>("Deleted");
            modelBuilder.Entity<VoyageCargo>().HasQueryFilter(x => EF.Property<bool>(x, "Deleted") == false);
            modelBuilder.Entity<VoyageCargo>().HasOne(p => p.CreatedBy)
                .WithMany()
                .HasForeignKey(f => f.CreatedById);
            modelBuilder.Entity<VoyageCargo>().HasOne(p => p.UpdatedBy)
                .WithMany()
                .HasForeignKey(f => f.UpdatedById);
            modelBuilder.Entity<VoyageCargo>().Navigation(x => x.CreatedBy)
                .AutoInclude();
            modelBuilder.Entity<VoyageCargo>().Navigation(x => x.UpdatedBy)
                .AutoInclude();
            modelBuilder.Entity<VoyageCargo>().HasOne(p => p.VoyageCargoParent)
                .WithMany()
                .HasForeignKey(f => f.VoyageCargoParentId);
            modelBuilder.Entity<VoyageCargo>().HasOne(p => p.Voyage)
                .WithMany(p => p.VoyageCargoes)
                .HasForeignKey(f => f.VoyageId);
            modelBuilder.Entity<VoyageCargo>().HasOne(p => p.Cargo)
                .WithMany(p => p.VoyageCargoes)
                .HasForeignKey(f => f.CargoId);
            modelBuilder.Entity<VoyageCargo>().HasOne(p => p.Vendor)
                .WithMany()
                .HasForeignKey(f => f.VendorId);
            modelBuilder.Entity<VoyageCargo>().HasOne(p => p.VendorWarehouse)
                .WithMany()
                .HasForeignKey(f => f.VendorWarehouseId);
            modelBuilder.Entity<VoyageCargo>().HasOne(p => p.District)
                .WithMany()
                .HasForeignKey(f => f.DistrictId);
            modelBuilder.Entity<VoyageCargo>().HasOne(p => p.ViaVendor)
                .WithMany()
                .HasForeignKey(f => f.ViaVendorId);
            modelBuilder.Entity<VoyageCargo>().HasOne(p => p.ViaVendorWarehouse)
                .WithMany()
                .HasForeignKey(f => f.ViaVendorWarehouseId);
            modelBuilder.Entity<VoyageCargo>().HasMany(p => p.VoyageCargoLifts)
                .WithOne(x => x.VoyageCargo)
                .HasForeignKey(f => f.VoyageCargoId);
            modelBuilder.Entity<VoyageCargo>().HasMany(p => p.MaterialDetails)
                .WithOne(x => x.VoyageCargo)
                .HasForeignKey(f => f.VoyageCargoId);
            modelBuilder.Entity<VoyageCargo>().HasMany(p => p.VoyageCargoDangerousGoods)
                .WithOne(x => x.VoyageCargo)
                .HasForeignKey(f => f.VoyageCargoId)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<VoyageCargo>().HasOne(p => p.Asset)
                .WithMany(x => x.VoyageCargoes)
                .HasForeignKey(f => f.AssetId);
            modelBuilder.Entity<VoyageCargo>().HasOne(p => p.LiftedAtArea)
                .WithMany()
                .HasForeignKey(f => f.LiftedAtAreaId);
            modelBuilder.Entity<VoyageCargo>().HasOne(p => p.WeightCategory)
                .WithMany()
                .HasForeignKey(f => f.WeightCategoryId)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<VoyageCargo>().HasOne(p => p.Trailer)
                .WithMany()
                .HasForeignKey(f => f.TrailerId)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<VoyageCargo>().HasOne(p => p.Site)
                .WithMany()
                .HasForeignKey(f => f.SiteId);
            modelBuilder.Entity<VoyageCargo>().HasOne(p => p.Client)
                .WithMany()
                .HasForeignKey(f => f.ClientId);
            modelBuilder.Entity<VoyageCargo>().HasOne(p => p.VoyageCargoLoad)
                .WithMany()
                .HasForeignKey(f => f.VoyageCargoLoadId)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<VoyageCargo>().Property(p => p.CustomReferenceNo)
                .HasMaxLength(255);
            modelBuilder.Entity<VoyageCargo>().Property(p => p.CcuId)
                .HasMaxLength(255);
            modelBuilder.Entity<VoyageCargo>().Property(p => p.CargoUnitType)
                .HasMaxLength(255);
            modelBuilder.Entity<VoyageCargo>().Property(p => p.CargoDescription)
                .HasMaxLength(255);
            modelBuilder.Entity<VoyageCargo>().Property(p => p.Comments)
                .HasMaxLength(200);
            modelBuilder.Entity<VoyageCargo>().Property(p => p.PickupAddress)
                .HasMaxLength(255);
            modelBuilder.Entity<VoyageCargo>().Property(p => p.TransportAddress)
                .HasMaxLength(255);
            modelBuilder.Entity<VoyageCargo>().Property(p => p.TransportName)
                .HasMaxLength(255);
            modelBuilder.Entity<VoyageCargo>().Property(p => p.TransportPhoneNumber)
                .HasMaxLength(255);
            modelBuilder.Entity<VoyageCargo>().Property(p => p.TrailerNumber)
                .HasMaxLength(255);
            modelBuilder.Entity<VoyageCargo>().Property(p => p.Owner)
                .HasMaxLength(255);
            modelBuilder.Entity<VoyageCargo>().Property(p => p.SepaNumber)
                            .HasMaxLength(30);
            modelBuilder.Entity<VoyageCargo>().HasIndex(x => new { x.RowNumber, x.VoyageId })
                .HasDatabaseName("IX_VoyageCargoes_RowNumber_VoyageId");

            modelBuilder.Entity<VoyageCargoLift>().HasKey(x => x.VoyageCargoLiftId);
            modelBuilder.Entity<VoyageCargoLift>().Property<bool>("Deleted");
            modelBuilder.Entity<VoyageCargoLift>().HasQueryFilter(x => EF.Property<bool>(x, "Deleted") == false);
            modelBuilder.Entity<VoyageCargoLift>().HasOne(p => p.CreatedBy)
                .WithMany()
                .HasForeignKey(f => f.CreatedById);
            modelBuilder.Entity<VoyageCargoLift>().HasOne(p => p.UpdatedBy)
                .WithMany()
                .HasForeignKey(f => f.UpdatedById);
            modelBuilder.Entity<VoyageCargoLift>().Navigation(x => x.CreatedBy)
                .AutoInclude();
            modelBuilder.Entity<VoyageCargoLift>().Navigation(x => x.UpdatedBy)
                .AutoInclude();
            modelBuilder.Entity<VoyageCargoLift>().HasOne(p => p.VoyageCargo)
                .WithMany(x => x.VoyageCargoLifts)
                .HasForeignKey(f => f.VoyageCargoId);
            modelBuilder.Entity<VoyageCargoLift>().HasOne(p => p.LoadCell)
                .WithMany()
                .HasForeignKey(f => f.LoadCellId);
            modelBuilder.Entity<VoyageCargoLift>().HasOne(p => p.Area)
                .WithMany()
                .HasForeignKey(f => f.AreaId);

            modelBuilder.Entity<VoyageCargoSailingRequestActivity>().HasKey(x => x.VoyageCargoSailingRequestActivityId);
            modelBuilder.Entity<VoyageCargoSailingRequestActivity>().Property<bool>("Deleted");
            modelBuilder.Entity<VoyageCargoSailingRequestActivity>().HasQueryFilter(x => EF.Property<bool>(x, "Deleted") == false);
            modelBuilder.Entity<VoyageCargoSailingRequestActivity>().HasOne(p => p.CreatedBy)
                .WithMany()
                .HasForeignKey(f => f.CreatedById);
            modelBuilder.Entity<VoyageCargoSailingRequestActivity>().HasOne(p => p.UpdatedBy)
                .WithMany()
                .HasForeignKey(f => f.UpdatedById);
            modelBuilder.Entity<VoyageCargoSailingRequestActivity>().Navigation(x => x.CreatedBy)
                .AutoInclude();
            modelBuilder.Entity<VoyageCargoSailingRequestActivity>().Navigation(x => x.UpdatedBy)
                .AutoInclude();
            modelBuilder.Entity<VoyageCargoSailingRequestActivity>().HasOne(p => p.VoyageCargo)
                .WithMany(x => x.VoyageCargoSailingRequestActivities)
                .HasForeignKey(f => f.VoyageCargoId);
            modelBuilder.Entity<VoyageCargoSailingRequestActivity>().HasOne(p => p.SailingRequestActivity)
                .WithMany(x => x.VoyageCargoSailingRequestActivities)
                .HasForeignKey(f => f.SailingRequestActivityId);

            modelBuilder.Entity<VoyageCargoSnapshot>().HasKey(x => x.VoyageCargoSnapshotId);
            modelBuilder.Entity<VoyageCargoSnapshot>().Property<bool>("Deleted");
            modelBuilder.Entity<VoyageCargoSnapshot>().HasQueryFilter(x => EF.Property<bool>(x, "Deleted") == false);
            modelBuilder.Entity<VoyageCargoSnapshot>().HasOne(p => p.CreatedBy)
                .WithMany()
                .HasForeignKey(f => f.CreatedById);
            modelBuilder.Entity<VoyageCargoSnapshot>().Navigation(x => x.CreatedBy)
                .AutoInclude();
            modelBuilder.Entity<VoyageCargoSnapshot>().HasOne(p => p.Voyage)
                .WithMany(x => x.VoyageCargoSnapshots)
                .HasForeignKey(f => f.VoyageId);
            modelBuilder.Entity<VoyageCargoSnapshot>().Property(p => p.Version)
                .HasMaxLength(255);
            modelBuilder.Entity<VoyageCargoSnapshot>().Property(p => p.Content)
                .HasMaxLength(4096);

            modelBuilder.Entity<VoyageMaterialDetailSnapshot>().HasKey(x => x.VoyageMaterialDetailSnapshotId);
            modelBuilder.Entity<VoyageMaterialDetailSnapshot>().HasOne(p => p.CreatedBy)
                .WithMany()
                .HasForeignKey(f => f.CreatedById);
            modelBuilder.Entity<VoyageMaterialDetailSnapshot>().Navigation(x => x.CreatedBy)
                .AutoInclude();
            modelBuilder.Entity<VoyageMaterialDetailSnapshot>().HasOne(p => p.Voyage)
                .WithMany(x => x.VoyageMaterialDetailSnapshots)
                .HasForeignKey(f => f.VoyageId);
            modelBuilder.Entity<VoyageMaterialDetailSnapshot>().Property(p => p.Version)
                .HasMaxLength(255);
            modelBuilder.Entity<VoyageMaterialDetailSnapshot>().Property(p => p.Content)
                .HasMaxLength(4096);

            modelBuilder.Entity<VoyageCargoBulkSnapshot>().HasKey(x => x.VoyageCargoBulkSnapshotId);
            modelBuilder.Entity<VoyageCargoBulkSnapshot>().HasOne(p => p.CreatedBy)
                .WithMany()
                .HasForeignKey(f => f.CreatedById);
            modelBuilder.Entity<VoyageCargoBulkSnapshot>().Navigation(x => x.CreatedBy)
                .AutoInclude();
            modelBuilder.Entity<VoyageCargoBulkSnapshot>().HasOne(p => p.Voyage)
                .WithMany(x => x.VoyageCargoBulkSnapshots)
                .HasForeignKey(f => f.VoyageId);
            modelBuilder.Entity<VoyageCargoBulkSnapshot>().Property(p => p.Version)
                .HasMaxLength(255);
            modelBuilder.Entity<VoyageCargoBulkSnapshot>().Property(p => p.Content)
                .HasMaxLength(4096);

            modelBuilder.Entity<OffshoreLocation>().HasKey(x => x.OffshoreLocationId);
            modelBuilder.Entity<OffshoreLocation>().Property<bool>("Deleted");
            modelBuilder.Entity<OffshoreLocation>().HasQueryFilter(x => EF.Property<bool>(x, "Deleted") == false);
            modelBuilder.Entity<OffshoreLocation>().HasOne(p => p.CreatedBy)
                .WithMany()
                .HasForeignKey(f => f.CreatedById)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<OffshoreLocation>().HasOne(p => p.UpdatedBy)
                .WithMany()
                .HasForeignKey(f => f.UpdatedById)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<OffshoreLocation>().Navigation(x => x.CreatedBy)
                .AutoInclude();
            modelBuilder.Entity<OffshoreLocation>().Navigation(x => x.UpdatedBy)
                .AutoInclude();
            modelBuilder.Entity<OffshoreLocation>().Property(x => x.ManifestNumber)
                .HasMaxLength(255);
            modelBuilder.Entity<OffshoreLocation>().HasOne(x => x.Voyage)
                .WithMany(x => x.OffshoreLocations)
                .HasForeignKey(x => x.VoyageId);
            modelBuilder.Entity<OffshoreLocation>().HasOne(x => x.Asset)
                .WithMany(x => x.VoyageOffshoreLocations)
                .HasForeignKey(x => x.AssetId);

            modelBuilder.Entity<VoyageSharer>().HasKey(x => x.VoyageSharerId);
            modelBuilder.Entity<VoyageSharer>().Property<bool>("Deleted");
            modelBuilder.Entity<VoyageSharer>().HasQueryFilter(x => EF.Property<bool>(x, "Deleted") == false);
            modelBuilder.Entity<VoyageSharer>().HasOne(p => p.CreatedBy)
                .WithMany()
                .HasForeignKey(f => f.CreatedById)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<VoyageSharer>().HasOne(p => p.UpdatedBy)
                .WithMany()
                .HasForeignKey(f => f.UpdatedById)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<VoyageSharer>().Navigation(x => x.CreatedBy)
                .AutoInclude();
            modelBuilder.Entity<VoyageSharer>().Navigation(x => x.UpdatedBy)
                .AutoInclude();
            modelBuilder.Entity<VoyageSharer>().HasOne(x => x.Voyage)
                .WithMany(x => x.VoyageSharers)
                .HasForeignKey(x => x.VoyageId);
            modelBuilder.Entity<VoyageSharer>().HasOne(x => x.Client)
                .WithMany()
                .HasForeignKey(x => x.ClientId);

            modelBuilder.Entity<VoyageEvent>().HasKey(x => x.VoyageEventId);
            modelBuilder.Entity<VoyageEvent>().HasOne(p => p.CreatedBy)
                .WithMany()
                .HasForeignKey(f => f.CreatedById)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<VoyageEvent>().Navigation(x => x.CreatedBy)
                .AutoInclude();
            modelBuilder.Entity<VoyageEvent>().HasOne(x => x.Voyage)
                .WithMany(x => x.VoyageEvents)
                .HasForeignKey(x => x.VoyageId)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<VoyageEvent>().Property(x => x.Title)
                .HasMaxLength(255);
            modelBuilder.Entity<VoyageEvent>().Property(x => x.Description)
                .HasMaxLength(255);

            modelBuilder.Entity<DangerousGoodLocation>().HasKey(p => p.DangerousGoodLocationId);
            modelBuilder.Entity<DangerousGoodLocation>().HasOne(p => p.CreatedBy)
                .WithMany()
                .HasForeignKey(f => f.CreatedById);
            modelBuilder.Entity<DangerousGoodLocation>().HasOne(p => p.UpdatedBy)
                .WithMany()
                .HasForeignKey(f => f.UpdatedById);
            modelBuilder.Entity<DangerousGoodLocation>().HasOne(p => p.Location)
                .WithMany(p => p.DangerousGoodLocations)
                .HasForeignKey(f => f.LocationId);
            modelBuilder.Entity<DangerousGoodLocation>().HasOne(p => p.DangerousGood)
                .WithMany(p => p.DangerousGoodLocations)
                .HasForeignKey(f => f.DangerousGoodId)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<DangerousGoodLocation>().Property<bool>("Deleted");
            modelBuilder.Entity<DangerousGoodLocation>().HasQueryFilter(x => EF.Property<bool>(x, "Deleted") == false);

            modelBuilder.Entity<DangerousGood>().HasKey(x => x.DangerousGoodId);
            modelBuilder.Entity<DangerousGood>().HasOne(p => p.CreatedBy)
                .WithMany()
                .HasForeignKey(f => f.CreatedById)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<DangerousGood>().HasOne(p => p.UpdatedBy)
                .WithMany()
                .HasForeignKey(f => f.UpdatedById)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<DangerousGood>().Navigation(x => x.CreatedBy)
                .AutoInclude();
            modelBuilder.Entity<DangerousGood>().Navigation(x => x.UpdatedBy)
                .AutoInclude();
            modelBuilder.Entity<DangerousGood>().HasMany(p => p.DangerousGoodLocations)
                .WithOne(p => p.DangerousGood)
                .HasForeignKey(p => p.DangerousGoodId);
            modelBuilder.Entity<DangerousGood>()
                .HasMany(x => x.TransportRequestCargoDangerousGoods)
                .WithOne(x => x.DangerousGood)
                .HasForeignKey(x => x.DangerousGoodId)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<DangerousGood>().HasMany(p => p.TransportRequestBulkCargoDangerousGoods)
               .WithOne(x => x.DangerousGood)
               .HasForeignKey(f => f.DangerousGoodId)
               .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<DangerousGood>().HasMany(p => p.VoyageCargoBulkDangerousGoods)
                .WithOne(x => x.DangerousGood)
                .HasForeignKey(f => f.DangerousGoodId)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<DangerousGood>().Property<bool>("Deleted");
            modelBuilder.Entity<DangerousGood>().HasQueryFilter(x => EF.Property<bool>(x, "Deleted") == false);
            modelBuilder.Entity<DangerousGood>().Property(x => x.UnNo)
                .HasMaxLength(255);
            modelBuilder.Entity<DangerousGood>().Property(p => p.PackingGroup).HasDefaultValue(PackingGroup.I);
            modelBuilder.Entity<DangerousGood>().Property(x => x.Class)
                .HasMaxLength(255);
            modelBuilder.Entity<DangerousGood>().Property(x => x.SubClass)
                .HasMaxLength(255);
            modelBuilder.Entity<DangerousGood>().Property(x => x.ProperShippingName)
                .HasMaxLength(255);

            modelBuilder.Entity<VoyageCargoDangerousGood>().HasKey(x => x.VoyageCargoDangerousGoodId);
            modelBuilder.Entity<VoyageCargoDangerousGood>().HasOne(p => p.VoyageCargo)
                .WithMany(x => x.VoyageCargoDangerousGoods)
                .HasForeignKey(f => f.VoyageCargoId)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<VoyageCargoDangerousGood>().HasOne(p => p.DangerousGood)
                .WithMany(x => x.VoyageCargoDangerousGoods)
                .HasForeignKey(f => f.DangerousGoodId)
                .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<Vendor>().HasKey(x => x.VendorId);
            modelBuilder.Entity<Vendor>().Property<bool>("Deleted");
            modelBuilder.Entity<Vendor>().HasQueryFilter(x => EF.Property<bool>(x, "Deleted") == false);
            modelBuilder.Entity<Vendor>().HasOne(p => p.CreatedBy)
                .WithMany()
                .HasForeignKey(f => f.CreatedById)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<Vendor>().HasOne(p => p.Voyage)
                .WithMany(x => x.Vendors)
                .HasForeignKey(f => f.VoyageId)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<Vendor>().HasOne(p => p.Location)
                .WithMany()
                .HasForeignKey(f => f.LocationId)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<Vendor>().HasOne(p => p.UpdatedBy)
                .WithMany()
                .HasForeignKey(f => f.UpdatedById)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<Vendor>().Navigation(x => x.CreatedBy)
                .AutoInclude();
            modelBuilder.Entity<Vendor>().Navigation(x => x.UpdatedBy)
                .AutoInclude();
            modelBuilder.Entity<Vendor>().Property(x => x.VendorName)
                .HasMaxLength(255);
            modelBuilder.Entity<Vendor>().Property(x => x.Address)
                .HasMaxLength(255);
            modelBuilder.Entity<Vendor>().Property(x => x.PostCode)
                .HasMaxLength(255);
            modelBuilder.Entity<Vendor>().Property(x => x.City)
                .HasMaxLength(255);
            modelBuilder.Entity<Vendor>().Property(x => x.Country)
                .HasMaxLength(255);
            modelBuilder.Entity<Vendor>().HasMany(x => x.VendorWarehouses)
                .WithOne(x => x.Vendor)
                .HasForeignKey(x => x.VendorId);

            modelBuilder.Entity<VendorWarehouse>().HasKey(p => p.VendorWarehouseId);
            modelBuilder.Entity<VendorWarehouse>().Property<bool>("Deleted");
            modelBuilder.Entity<VendorWarehouse>().HasQueryFilter(x => EF.Property<bool>(x, "Deleted") == false);
            modelBuilder.Entity<VendorWarehouse>().HasOne(p => p.CreatedBy)
                .WithMany()
                .HasForeignKey(f => f.CreatedById)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<VendorWarehouse>().HasOne(p => p.UpdatedBy)
                .WithMany()
                .HasForeignKey(f => f.UpdatedById)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<VendorWarehouse>().HasOne(p => p.District)
                .WithMany()
                .HasForeignKey(f => f.DistrictId)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<VendorWarehouse>().Property(x => x.Name)
                .HasMaxLength(255);
            modelBuilder.Entity<VendorWarehouse>().Property(x => x.Address)
                .HasMaxLength(255);
            modelBuilder.Entity<VendorWarehouse>().Property(x => x.City)
                .HasMaxLength(255);
            modelBuilder.Entity<VendorWarehouse>().Property(x => x.PostCode)
                .HasMaxLength(255);
            modelBuilder.Entity<VendorWarehouse>().Property(x => x.Country)
                .HasMaxLength(255);

            modelBuilder.Entity<CargoCertificate>().HasKey(x => x.CargoCertificateId);
            modelBuilder.Entity<CargoCertificate>().Property<bool>("Deleted");
            modelBuilder.Entity<CargoCertificate>().HasQueryFilter(x => EF.Property<bool>(x, "Deleted") == false);
            modelBuilder.Entity<CargoCertificate>().HasOne(p => p.CreatedBy)
                .WithMany()
                .HasForeignKey(f => f.CreatedById)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<CargoCertificate>().HasOne(p => p.UpdatedBy)
                .WithMany()
                .HasForeignKey(f => f.UpdatedById)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<CargoCertificate>().Navigation(x => x.CreatedBy)
                .AutoInclude();
            modelBuilder.Entity<CargoCertificate>().Navigation(x => x.UpdatedBy)
                .AutoInclude();
            modelBuilder.Entity<CargoCertificate>().HasOne(x => x.Cargo)
                .WithMany(x => x.CargoCertificates)
                .HasForeignKey(x => x.CargoId)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<CargoCertificate>().Property(x => x.DocumentName)
                .HasMaxLength(255);

            #region Transport Request related entities

            #region Transport Request
            modelBuilder.Entity<TransportRequest>().HasKey(x => x.TransportRequestId);
            modelBuilder.Entity<TransportRequest>().HasOne(p => p.CreatedBy)
                .WithMany()
                .HasForeignKey(f => f.CreatedById)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<TransportRequest>().HasOne(p => p.UpdatedBy)
                .WithMany()
                .HasForeignKey(f => f.UpdatedById)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<TransportRequest>().Navigation(x => x.CreatedBy)
                .AutoInclude();
            modelBuilder.Entity<TransportRequest>().Navigation(x => x.UpdatedBy)
                .AutoInclude();
            modelBuilder.Entity<TransportRequest>().HasOne(p => p.SubmittedBy)
                .WithMany()
                .HasForeignKey(f => f.SubmittedById)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<TransportRequest>().Navigation(x => x.SubmittedBy)
                .AutoInclude();
            modelBuilder.Entity<TransportRequest>().HasOne(p => p.Voyage)
               .WithMany(x => x.TransportRequests)
               .HasForeignKey(x => x.VoyageId)
               .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<TransportRequest>().HasOne(p => p.SailingRequest)
               .WithMany(f => f.TransportRequests)
               .HasForeignKey(f => f.SailingRequestId)
               .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<TransportRequest>()
               .HasMany(transportRequest => transportRequest.TransportRequestCargos)
               .WithOne(transportRequestCargos => transportRequestCargos.TransportRequest)
               .HasForeignKey(transportRequestCargos => transportRequestCargos.TransportRequestId)
               .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<TransportRequest>()
               .HasMany(transportRequest => transportRequest.TransportRequestMaterialDetails)
               .WithOne(transportRequestMaterialDetails => transportRequestMaterialDetails.TransportRequest)
               .HasForeignKey(transportRequestMaterialDetails => transportRequestMaterialDetails.TransportRequestId)
               .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<TransportRequest>()
               .HasMany(transportRequest => transportRequest.TransportRequestBulkCargos)
               .WithOne(transportRequestBulkCargos => transportRequestBulkCargos.TransportRequest)
               .HasForeignKey(transportRequestBulkCargos => transportRequestBulkCargos.TransportRequestId)
               .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<TransportRequest>()
               .HasMany(transportRequest => transportRequest.TransportRequestAttachments)
               .WithOne(transportRequestAttachments => transportRequestAttachments.TransportRequest)
               .HasForeignKey(transportRequestAttachments => transportRequestAttachments.TransportRequestId)
               .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<TransportRequest>()
               .HasMany(transportRequest => transportRequest.TransportRequestBulkCargoSnapshots)
               .WithOne(transportRequestBulkCargoSnapshots => transportRequestBulkCargoSnapshots.TransportRequest)
               .HasForeignKey(transportRequestBulkCargoSnapshots => transportRequestBulkCargoSnapshots.TransportRequestId)
               .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<TransportRequest>()
               .HasMany(transportRequest => transportRequest.TransportRequestCargoSnapshots)
               .WithOne(transportRequestCargoSnapshots => transportRequestCargoSnapshots.TransportRequest)
               .HasForeignKey(transportRequestCargoSnapshots => transportRequestCargoSnapshots.TransportRequestId)
               .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<TransportRequest>()
               .HasMany(transportRequest => transportRequest.TransportRequestMaterialDetailSnapshots)
               .WithOne(transportRequestMaterialDetailSnapshots => transportRequestMaterialDetailSnapshots.TransportRequest)
               .HasForeignKey(transportRequestMaterialDetailSnapshots => transportRequestMaterialDetailSnapshots.TransportRequestId)
               .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<TransportRequest>().Property<bool>("Deleted");
            modelBuilder.Entity<TransportRequest>().HasQueryFilter(x => EF.Property<bool>(x, "Deleted") == false);
            modelBuilder.Entity<TransportRequest>().Property(x => x.TodoSpecialComments)
                .HasMaxLength(250);

            #endregion

            #region Transport Request Attachment
            modelBuilder.Entity<TransportRequestAttachment>().HasKey(x => x.TransportRequestAttachmentId);
            modelBuilder.Entity<TransportRequestAttachment>().Property<bool>("Deleted");
            modelBuilder.Entity<TransportRequestAttachment>().HasQueryFilter(x => EF.Property<bool>(x, "Deleted") == false);
            modelBuilder.Entity<TransportRequestAttachment>()
                .HasOne(transportRequestAttachment => transportRequestAttachment.TransportRequest)
                .WithMany(transportRequest => transportRequest.TransportRequestAttachments)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<TransportRequestAttachment>().HasOne(p => p.CreatedBy)
                .WithMany()
                .HasForeignKey(f => f.CreatedById)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<TransportRequestAttachment>().HasOne(p => p.UpdatedBy)
                .WithMany()
                .HasForeignKey(f => f.UpdatedById)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<TransportRequestAttachment>().Navigation(x => x.CreatedBy)
                .AutoInclude();
            modelBuilder.Entity<TransportRequestAttachment>().Navigation(x => x.UpdatedBy)
                .AutoInclude();

            modelBuilder.Entity<TransportRequestAttachment>().Property(x => x.DocumentName)
                .HasMaxLength(255);
            #endregion



            #region Transport Request Cargo
            modelBuilder.Entity<TransportRequestCargo>().HasKey(x => x.TransportRequestCargoId);
            modelBuilder.Entity<TransportRequestCargo>().Property<bool>("Deleted");
            modelBuilder.Entity<TransportRequestCargo>().HasQueryFilter(x => EF.Property<bool>(x, "Deleted") == false);
            modelBuilder.Entity<TransportRequestCargo>()
                .HasOne(transportRequestCargo => transportRequestCargo.TransportRequest)
                .WithMany(transportRequest => transportRequest.TransportRequestCargos)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<TransportRequestCargo>()
                .HasMany(x => x.TransportRequestCargoDangerousGoods)
                .WithOne(x => x.TransportRequestCargo)
                .HasForeignKey(x => x.TransportRequestCargoId)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<TransportRequestCargo>()
                .HasMany(x => x.TransportRequestCargoAttachments)
                .WithOne(x => x.TransportRequestCargo)
                .HasForeignKey(x => x.TransportRequestCargoId)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<TransportRequestCargo>()
                .HasOne(p => p.TransportRequestCargoBundling)
                .WithMany()
                .HasForeignKey(f => f.TransportRequestCargoBundlingId)
                .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<TransportRequestCargo>().HasOne(p => p.SpecialCargoProcessedBy)
                 .WithMany()
                 .HasForeignKey(f => f.SpecialCargoProcessedById)
                 .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<TransportRequestCargo>().HasOne(p => p.TransferProcessedBy)
                .WithMany()
                .HasForeignKey(f => f.TransferProcessedById)
                .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<TransportRequestCargo>().HasOne(p => p.WasteProcessedBy)
                .WithMany()
                .HasForeignKey(f => f.WasteProcessedById)
                .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<TransportRequestCargo>().HasOne(p => p.Cargo)
                .WithMany()
                .HasForeignKey(f => f.CargoId)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<TransportRequestCargo>().HasOne(p => p.TransportRequestMovedFrom)
               .WithMany()
               .HasForeignKey(f => f.TransportRequestMovedFromId)
               .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<TransportRequestCargo>().HasOne(p => p.Vendor)
               .WithMany()
               .HasForeignKey(f => f.VendorId)
               .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<TransportRequestCargo>().HasOne(p => p.FromAsset)
               .WithMany()
               .HasForeignKey(f => f.FromAssetId)
               .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<TransportRequestCargo>().HasOne(p => p.FromLocation)
               .WithMany()
               .HasForeignKey(f => f.FromLocationId)
               .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<TransportRequestCargo>().HasOne(p => p.Location)
              .WithMany()
              .HasForeignKey(f => f.LocationId)
              .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<TransportRequestCargo>().HasOne(p => p.ToAsset)
               .WithMany()
               .HasForeignKey(f => f.ToAssetId)
               .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<TransportRequestCargo>().HasOne(p => p.ToLocation)
               .WithMany()
               .HasForeignKey(f => f.ToLocationId)
               .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<TransportRequestCargo>().HasOne(p => p.CreatedBy)
                .WithMany()
                .HasForeignKey(f => f.CreatedById)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<TransportRequestCargo>().HasOne(p => p.ApprovedBy)
                .WithMany()
                .HasForeignKey(f => f.ApprovedById)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<TransportRequestCargo>().HasOne(p => p.SubmittedBy)
                .WithMany()
                .HasForeignKey(f => f.SubmittedById)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<TransportRequestCargo>().HasOne(p => p.MovedBy)
               .WithMany()
               .HasForeignKey(f => f.MovedById)
               .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<TransportRequestCargo>().HasOne(p => p.UpdatedBy)
                .WithMany()
                .HasForeignKey(f => f.UpdatedById)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<TransportRequestCargo>().HasOne(p => p.StatusUpdatedBy)
                .WithMany()
                .HasForeignKey(f => f.StatusUpdatedById)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<TransportRequestCargo>().Navigation(x => x.CreatedBy)
                .AutoInclude();
            modelBuilder.Entity<TransportRequestCargo>().Navigation(x => x.UpdatedBy)
                .AutoInclude();
            modelBuilder.Entity<TransportRequestCargo>().Property(x => x.CancellationReason)
                .HasMaxLength(255);
            modelBuilder.Entity<TransportRequestCargo>().Property(x => x.SealNumber)
                .HasMaxLength(255);
            modelBuilder.Entity<TransportRequestCargo>().Property(x => x.Comments)
                .HasMaxLength(1023);
            modelBuilder.Entity<TransportRequestCargo>().Property(x => x.Requestor)
                .HasMaxLength(255);
            modelBuilder.Entity<TransportRequestCargo>().Property(x => x.PoNumber)
                .HasMaxLength(255);
            #endregion

            #region Transport Request Cargo Dangerous Good
            modelBuilder.Entity<TransportRequestCargoDangerousGood>().HasKey(x => x.TransportRequestCargoDangerousGoodId);
            modelBuilder.Entity<TransportRequestCargoDangerousGood>()
                .HasOne(x => x.TransportRequestCargo)
                .WithMany(x => x.TransportRequestCargoDangerousGoods)
                .HasForeignKey(x => x.TransportRequestCargoId)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<TransportRequestCargoDangerousGood>()
                .HasOne(x => x.DangerousGood)
                .WithMany(x => x.TransportRequestCargoDangerousGoods)
                .HasForeignKey(x => x.DangerousGoodId)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<TransportRequestCargoDangerousGood>().HasOne(p => p.CreatedBy)
                .WithMany()
                .HasForeignKey(f => f.CreatedById)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<TransportRequestCargoDangerousGood>().HasOne(p => p.UpdatedBy)
                .WithMany()
                .HasForeignKey(f => f.UpdatedById)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<TransportRequestCargoDangerousGood>().Navigation(x => x.CreatedBy)
                .AutoInclude();
            modelBuilder.Entity<TransportRequestCargoDangerousGood>().Navigation(x => x.UpdatedBy)
                .AutoInclude();
            modelBuilder.Entity<TransportRequestCargoDangerousGood>().Property<bool>("Deleted");
            modelBuilder.Entity<TransportRequestCargoDangerousGood>().HasQueryFilter(x => EF.Property<bool>(x, "Deleted") == false);
            #endregion

            #region Transport Request Cargo Attachment
            modelBuilder.Entity<TransportRequestCargoAttachment>().HasKey(x => x.TransportRequestCargoAttachmentId);
            modelBuilder.Entity<TransportRequestCargoAttachment>().Property<bool>("Deleted");
            modelBuilder.Entity<TransportRequestCargoAttachment>().HasQueryFilter(x => EF.Property<bool>(x, "Deleted") == false);
            modelBuilder.Entity<TransportRequestCargoAttachment>()
               .HasOne(transportRequestCargoAttachment => transportRequestCargoAttachment.TransportRequestCargo)
               .WithMany(transportRequestCargo => transportRequestCargo.TransportRequestCargoAttachments)
               .HasForeignKey(transportRequestCargoAttachment => transportRequestCargoAttachment.TransportRequestCargoId)
               .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<TransportRequestCargoAttachment>().HasOne(p => p.CreatedBy)
                .WithMany()
                .HasForeignKey(f => f.CreatedById);
            modelBuilder.Entity<TransportRequestCargoAttachment>().Navigation(x => x.CreatedBy)
                .AutoInclude();
            modelBuilder.Entity<TransportRequestCargoAttachment>().Property(p => p.DocumentName)
                .HasMaxLength(255);
            #endregion

            #region Transport Request Cargo Bundling
            modelBuilder.Entity<TransportRequestCargoBundling>().HasKey(x => x.TransportRequestCargoBundlingId);
            modelBuilder.Entity<TransportRequestCargoBundling>().Property<bool>("Deleted");
            modelBuilder.Entity<TransportRequestCargoBundling>().HasQueryFilter(x => EF.Property<bool>(x, "Deleted") == false);
            modelBuilder.Entity<TransportRequestCargoBundling>().HasOne(p => p.CreatedBy)
                .WithMany()
                .HasForeignKey(f => f.CreatedById);
            modelBuilder.Entity<TransportRequestCargoBundling>().HasOne(p => p.UpdatedBy)
                .WithMany()
                .HasForeignKey(f => f.UpdatedById);
            modelBuilder.Entity<TransportRequestCargoBundling>().Navigation(x => x.CreatedBy)
                .AutoInclude();
            modelBuilder.Entity<TransportRequestCargoBundling>().Property(p => p.Description)
                .HasMaxLength(255);
            modelBuilder.Entity<TransportRequestCargoBundling>().Navigation(x => x.CreatedBy)
                .AutoInclude();
            modelBuilder.Entity<TransportRequestCargoBundling>().Property(p => p.Description)
                .HasMaxLength(255);
            #endregion

            #region Transport Request Cargo Snapshot
            modelBuilder.Entity<TransportRequestCargoSnapshot>().HasKey(x => x.TransportRequestCargoSnapshotId);
            modelBuilder.Entity<TransportRequestCargoSnapshot>().Property<bool>("Deleted");
            modelBuilder.Entity<TransportRequestCargoSnapshot>().HasQueryFilter(x => EF.Property<bool>(x, "Deleted") == false);
            modelBuilder.Entity<TransportRequestCargoSnapshot>()
                .HasOne(transportRequestCargoSnapshot => transportRequestCargoSnapshot.TransportRequest)
                .WithMany(transportRequest => transportRequest.TransportRequestCargoSnapshots)
                .HasForeignKey(transportRequestCargoSnapshot => transportRequestCargoSnapshot.TransportRequestId);
            modelBuilder.Entity<TransportRequestCargoSnapshot>().HasOne(p => p.CreatedBy)
                .WithMany()
                .HasForeignKey(f => f.CreatedById)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<TransportRequestCargoSnapshot>().Navigation(x => x.CreatedBy)
                .AutoInclude();
            modelBuilder.Entity<TransportRequestCargoSnapshot>().Property(p => p.Content)
                .HasMaxLength(4096);
            modelBuilder.Entity<TransportRequestCargoSnapshot>().Property(p => p.Version)
                .HasMaxLength(255);
            #endregion



            #region Transport Request Material Detail
            modelBuilder.Entity<TransportRequestMaterialDetail>().HasKey(x => x.TransportRequestMaterialDetailId);
            modelBuilder.Entity<TransportRequestMaterialDetail>()
                .HasOne(transportRequestMaterialDetail => transportRequestMaterialDetail.TransportRequest)
                .WithMany(transportRequest => transportRequest.TransportRequestMaterialDetails)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<TransportRequestMaterialDetail>().HasOne(p => p.Vendor)
                .WithMany()
                .HasForeignKey(f => f.VendorId)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<TransportRequestMaterialDetail>().HasOne(p => p.OffshoreInstallationAsset)
                .WithMany()
                .HasForeignKey(f => f.OffshoreInstallationId)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<TransportRequestMaterialDetail>().HasOne(p => p.TransportRequestCargo)
               .WithMany()
               .HasForeignKey(f => f.TransportRequestCargoId)
               .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<TransportRequestMaterialDetail>().HasOne(p => p.TransportRequestBulkCargo)
              .WithMany()
              .HasForeignKey(f => f.TransportRequestBulkCargoId)
              .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<TransportRequestMaterialDetail>().HasOne(p => p.CreatedBy)
                .WithMany()
                .HasForeignKey(f => f.CreatedById)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<TransportRequestMaterialDetail>().HasOne(p => p.UpdatedBy)
                .WithMany()
                .HasForeignKey(f => f.UpdatedById)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<TransportRequestMaterialDetail>().HasOne(p => p.SubmittedBy)
               .WithMany()
               .HasForeignKey(f => f.SubmittedById)
               .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<TransportRequestMaterialDetail>().Navigation(x => x.CreatedBy)
                .AutoInclude();
            modelBuilder.Entity<TransportRequestMaterialDetail>().Navigation(x => x.UpdatedBy)
                .AutoInclude();
            modelBuilder.Entity<TransportRequestMaterialDetail>().Navigation(x => x.SubmittedBy)
                .AutoInclude();
            modelBuilder.Entity<TransportRequestMaterialDetail>().HasOne(p => p.TransportRequestMaterialDetailDangerousGood)
                .WithMany()
                .HasForeignKey(f => f.TransportRequestMaterialDetailDangerousGoodId)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<TransportRequestMaterialDetail>().HasMany(x => x.TransportRequestMaterialDetailAttachments)
                .WithOne(x => x.TransportRequestmaterialDetail)
                .HasForeignKey(x => x.TransportRequestmaterialDetailId)
                .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<TransportRequestMaterialDetail>().Property<bool>("Deleted");
            modelBuilder.Entity<TransportRequestMaterialDetail>().HasQueryFilter(x => EF.Property<bool>(x, "Deleted") == false);
            modelBuilder.Entity<TransportRequestMaterialDetail>().Property(x => x.PackagingUnit)
                .HasMaxLength(255);
            modelBuilder.Entity<TransportRequestMaterialDetail>().Property(x => x.Comments)
                .HasMaxLength(1023);
            modelBuilder.Entity<TransportRequestMaterialDetail>().Property(x => x.CancellationReason)
                .HasMaxLength(255);
            modelBuilder.Entity<TransportRequestMaterialDetail>().Property(x => x.PackagingUnit)
                .HasMaxLength(255);
            modelBuilder.Entity<TransportRequestMaterialDetail>().Property(x => x.OffshoreInstallation)
                .HasMaxLength(255);
            modelBuilder.Entity<TransportRequestMaterialDetail>().Property(x => x.Description)
                .HasMaxLength(255);
            modelBuilder.Entity<TransportRequestMaterialDetail>().Property(x => x.CountryOfOrigin)
                .HasMaxLength(255);
            modelBuilder.Entity<TransportRequestMaterialDetail>().Property(x => x.PoNumber)
                .HasMaxLength(255);
            modelBuilder.Entity<TransportRequestMaterialDetail>().Property(x => x.WorkOrder)
                .HasMaxLength(255);
            modelBuilder.Entity<TransportRequestMaterialDetail>().Property(x => x.PickupLocation)
                .HasMaxLength(255);
            modelBuilder.Entity<TransportRequestMaterialDetail>().Property(x => x.CustomsDocumentNumber)
                .HasMaxLength(255);
            modelBuilder.Entity<TransportRequestMaterialDetail>().Property(x => x.CommodityCode)
                .HasMaxLength(255);
            modelBuilder.Entity<TransportRequestMaterialDetail>().Property(x => x.SerialNumber)
                .HasMaxLength(255);
            modelBuilder.Entity<TransportRequestMaterialDetail>().Property(x => x.WasteDescription)
                .HasMaxLength(255);
            modelBuilder.Entity<TransportRequestMaterialDetail>().Property(x => x.ManifestNumber)
                .HasMaxLength(255);
            modelBuilder.Entity<TransportRequestMaterialDetail>().Property(x => x.Value)
                .HasMaxLength(255);
            modelBuilder.Entity<TransportRequestMaterialDetail>().Property(x => x.Requestor)
                .HasMaxLength(255);
            modelBuilder.Entity<TransportRequestMaterialDetail>().Property(x => x.ProperShippingName)
                .HasMaxLength(255);
            modelBuilder.Entity<TransportRequestMaterialDetail>().Property(x => x.MaxStockValue)
                .HasMaxLength(255);
            modelBuilder.Entity<TransportRequestMaterialDetail>().Property(x => x.StockMaterialReturnedAs)
                .HasMaxLength(255);
            modelBuilder.Entity<TransportRequestMaterialDetail>().Property(x => x.ParentCargoItemStatus)
                .HasMaxLength(255);
            modelBuilder.Entity<TransportRequestMaterialDetail>().Property(x => x.ParentCargoItemCategory)
                .HasMaxLength(255);
            #endregion

            #region Transport Request material Detail Attachment
            modelBuilder.Entity<TransportRequestmaterialDetailAttachment>().HasKey(x => x.TransportRequestmaterialDetailAttachmentId);
            modelBuilder.Entity<TransportRequestmaterialDetailAttachment>()
                .HasOne(x => x.TransportRequestmaterialDetail)
                .WithMany(x => x.TransportRequestMaterialDetailAttachments)
                .HasForeignKey(x => x.TransportRequestmaterialDetailId)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<TransportRequestmaterialDetailAttachment>().HasOne(p => p.CreatedBy)
                .WithMany()
                .HasForeignKey(f => f.CreatedById)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<TransportRequestmaterialDetailAttachment>().HasOne(p => p.UpdatedBy)
                .WithMany()
                .HasForeignKey(f => f.UpdatedById)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<TransportRequestmaterialDetailAttachment>().Navigation(x => x.CreatedBy)
                .AutoInclude();
            modelBuilder.Entity<TransportRequestmaterialDetailAttachment>().Navigation(x => x.UpdatedBy)
                .AutoInclude();

            modelBuilder.Entity<TransportRequestmaterialDetailAttachment>().Property(x => x.DocumentName)
                .HasMaxLength(255);
            modelBuilder.Entity<TransportRequestmaterialDetailAttachment>().Property<bool>("Deleted");
            modelBuilder.Entity<TransportRequestmaterialDetailAttachment>().HasQueryFilter(x => EF.Property<bool>(x, "Deleted") == false);
            modelBuilder.Entity<TransportRequestmaterialDetailAttachment>().Property(x => x.DocumentName)
                .HasMaxLength(255);
            #endregion

            #region Transport Request Material Detail Dangerous Good
            modelBuilder.Entity<TransportRequestMaterialDetailDangerousGood>().HasKey(x => x.TransportRequestMaterialDetailDangerousGoodId);
            modelBuilder.Entity<TransportRequestMaterialDetailDangerousGood>().HasOne(x => x.TransportRequestCargoDangerousGood)
                .WithMany()
                .HasForeignKey(x => x.TransportRequestCargoDangerousGoodId)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<TransportRequestMaterialDetailDangerousGood>().HasOne(x => x.TransportRequestBulkCargoDangerousGood)
                .WithMany()
                .HasForeignKey(x => x.TransportRequestBulkCargoDangerousGoodId)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<TransportRequestMaterialDetailDangerousGood>().HasOne(x => x.CreatedBy).WithMany().HasForeignKey(x => x.CreatedById);
            modelBuilder.Entity<TransportRequestMaterialDetailDangerousGood>().HasOne(x => x.UpdatedBy).WithMany().HasForeignKey(x => x.UpdatedById);
            modelBuilder.Entity<TransportRequestMaterialDetail>().Navigation(x => x.CreatedBy).AutoInclude();
            modelBuilder.Entity<TransportRequestMaterialDetail>().Navigation(x => x.UpdatedBy).AutoInclude();
            #endregion

            #region Transport Request Material Detail Snapshot
            modelBuilder.Entity<TransportRequestMaterialDetailSnapshot>().HasKey(x => x.TransportRequestMaterialDetailSnapshotId);
            modelBuilder.Entity<TransportRequestMaterialDetailSnapshot>()
                .HasOne(transportRequestMaterialDetailSnapshot => transportRequestMaterialDetailSnapshot.TransportRequest)
                .WithMany(transportRequest => transportRequest.TransportRequestMaterialDetailSnapshots)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<TransportRequestMaterialDetailSnapshot>().HasOne(p => p.CreatedBy)
                .WithMany()
                .HasForeignKey(f => f.CreatedById)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<TransportRequestMaterialDetailSnapshot>().Navigation(x => x.CreatedBy).AutoInclude();
            modelBuilder.Entity<TransportRequestMaterialDetailSnapshot>().Property(x => x.Version).HasMaxLength(255);
            modelBuilder.Entity<TransportRequestMaterialDetailSnapshot>().Property(x => x.Content).HasMaxLength(4096);
            modelBuilder.Entity<TransportRequestMaterialDetailSnapshot>().Property<bool>("Deleted");
            modelBuilder.Entity<TransportRequestMaterialDetailSnapshot>().HasQueryFilter(x => EF.Property<bool>(x, "Deleted") == false);
            #endregion



            #region Transport Request Bulk Cargo
            modelBuilder.Entity<TransportRequestBulkCargo>().HasKey(x => x.TransportRequestBulkCargoId);
            modelBuilder.Entity<TransportRequestBulkCargo>()
                .HasOne(transportRequestBulkCargo => transportRequestBulkCargo.TransportRequest)
                .WithMany(transportRequest => transportRequest.TransportRequestBulkCargos)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<TransportRequestBulkCargo>().HasOne(p => p.TransportRequestBulkCargoDangerousGood)
               .WithMany()
               .HasForeignKey(f => f.TransportRequestBulkCargoDangerousGoodId)
               .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<TransportRequestBulkCargo>()
                .HasMany(x => x.TransportRequestBulkCargoAttachments)
                .WithOne(x => x.TransportRequestBulkCargo)
                .HasForeignKey(x => x.TransportRequestBulkCargoId)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<TransportRequestBulkCargo>().HasOne(p => p.BulkType)
               .WithMany()
               .HasForeignKey(f => f.BulkTypeId)
               .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<TransportRequestBulkCargo>().HasOne(p => p.Vendor)
               .WithMany()
               .HasForeignKey(f => f.VendorId)
               .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<TransportRequestBulkCargo>().HasOne(p => p.FromAsset)
              .WithMany()
              .HasForeignKey(f => f.FromAssetId)
              .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<TransportRequestBulkCargo>().HasOne(p => p.FromLocation)
              .WithMany()
              .HasForeignKey(f => f.FromLocationId)
              .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<TransportRequestBulkCargo>().HasOne(p => p.ToAsset)
              .WithMany()
              .HasForeignKey(f => f.ToAssetId)
              .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<TransportRequestBulkCargo>().HasOne(p => p.ToLocation)
              .WithMany()
              .HasForeignKey(f => f.ToLocationId)
              .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<TransportRequestBulkCargo>().HasOne(p => p.CreatedBy)
                .WithMany()
                .HasForeignKey(f => f.CreatedById)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<TransportRequestBulkCargo>().HasOne(p => p.UpdatedBy)
                .WithMany()
                .HasForeignKey(f => f.UpdatedById)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<TransportRequestBulkCargo>().HasOne(p => p.SubmittedBy)
                .WithMany()
                .HasForeignKey(f => f.SubmittedById)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<TransportRequestBulkCargo>().Navigation(x => x.CreatedBy)
                .AutoInclude();
            modelBuilder.Entity<TransportRequestBulkCargo>().Navigation(x => x.UpdatedBy)
                .AutoInclude();
            modelBuilder.Entity<TransportRequestBulkCargo>().HasOne(p => p.StatusUpdatedBy)
                .WithMany()
                .HasForeignKey(f => f.StatusUpdatedById)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<TransportRequestBulkCargo>().Property<bool>("Deleted");
            modelBuilder.Entity<TransportRequestBulkCargo>().HasQueryFilter(x => EF.Property<bool>(x, "Deleted") == false);
            modelBuilder.Entity<TransportRequestBulkCargo>().Property(x => x.CancellationReason)
               .HasMaxLength(255);
            modelBuilder.Entity<TransportRequestBulkCargo>().Property(x => x.Name)
                .HasMaxLength(255);
            modelBuilder.Entity<TransportRequestBulkCargo>().Property(x => x.Comment)
                .HasMaxLength(1023);
            #endregion

            #region TransportRequest Bulk Cargo Dangerous Good
            modelBuilder.Entity<TransportRequestBulkCargoDangerousGood>().HasKey(x => x.TransportRequestBulkCargoDangerousGoodId);
            modelBuilder.Entity<TransportRequestBulkCargoDangerousGood>().HasOne(x => x.DangerousGood)
                .WithMany(x => x.TransportRequestBulkCargoDangerousGoods)
                .HasForeignKey(x => x.DangerousGoodId)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<TransportRequestBulkCargoDangerousGood>().HasOne(p => p.CreatedBy)
                .WithMany()
                .HasForeignKey(f => f.CreatedById)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<TransportRequestBulkCargoDangerousGood>().HasOne(p => p.UpdatedBy)
                .WithMany()
                .HasForeignKey(f => f.UpdatedById)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<TransportRequestBulkCargoDangerousGood>().Navigation(x => x.CreatedBy)
                .AutoInclude();
            modelBuilder.Entity<TransportRequestBulkCargoDangerousGood>().Navigation(x => x.UpdatedBy)
                .AutoInclude();
            modelBuilder.Entity<TransportRequestBulkCargoDangerousGood>().Property<bool>("Deleted");
            modelBuilder.Entity<TransportRequestBulkCargoDangerousGood>().HasQueryFilter(x => EF.Property<bool>(x, "Deleted") == false);

            #endregion

            #region Transport Request Bulk Cargo Attachment
            modelBuilder.Entity<TransportRequestBulkCargoAttachment>().HasKey(x => x.TransportRequestBulkCargoAttachmentId);
            modelBuilder.Entity<TransportRequestBulkCargoAttachment>()
                .HasOne(p => p.TransportRequestBulkCargo)
                .WithMany(f => f.TransportRequestBulkCargoAttachments)
                .HasForeignKey(f => f.TransportRequestBulkCargoId)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<TransportRequestBulkCargoAttachment>().HasOne(p => p.CreatedBy)
               .WithMany()
               .HasForeignKey(f => f.CreatedById)
               .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<TransportRequestBulkCargoAttachment>().HasOne(p => p.UpdatedBy)
                .WithMany()
                .HasForeignKey(f => f.UpdatedById)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<TransportRequestBulkCargoAttachment>().Navigation(x => x.CreatedBy)
                .AutoInclude();
            modelBuilder.Entity<TransportRequestBulkCargoAttachment>().Navigation(x => x.UpdatedBy)
                .AutoInclude();
            modelBuilder.Entity<TransportRequestBulkCargoAttachment>().Property(p => p.DocumentName)
                .HasMaxLength(255);
            modelBuilder.Entity<TransportRequestBulkCargoAttachment>().Property<bool>("Deleted");
            modelBuilder.Entity<TransportRequestBulkCargoAttachment>().HasQueryFilter(x => EF.Property<bool>(x, "Deleted") == false);
            #endregion

            #region Transport Request Bulk Cargo Snapshot
            modelBuilder.Entity<TransportRequestBulkCargoSnapshot>().HasKey(x => x.TransportRequestBulkCargoSnapshotId);
            modelBuilder.Entity<TransportRequestBulkCargoSnapshot>()
                .HasOne(transportRequestBulkCargoSnapshot => transportRequestBulkCargoSnapshot.TransportRequest)
                .WithMany(transportRequest => transportRequest.TransportRequestBulkCargoSnapshots)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<TransportRequestBulkCargoSnapshot>().HasOne(p => p.CreatedBy)
                .WithMany()
                .HasForeignKey(f => f.CreatedById)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<TransportRequestBulkCargoSnapshot>().Navigation(x => x.CreatedBy)
                .AutoInclude();
            modelBuilder.Entity<TransportRequestBulkCargoSnapshot>().Property(p => p.Content)
                .HasMaxLength(4096);
            modelBuilder.Entity<TransportRequestBulkCargoSnapshot>().Property(p => p.Version)
                .HasMaxLength(255);
            modelBuilder.Entity<TransportRequestBulkCargoSnapshot>().Property<bool>("Deleted");
            modelBuilder.Entity<TransportRequestBulkCargoSnapshot>().HasQueryFilter(x => EF.Property<bool>(x, "Deleted") == false);
            #endregion

            #endregion

            modelBuilder.Entity<VoyageComment>().HasKey(x => x.VoyageCommentId);
            modelBuilder.Entity<VoyageComment>().Property<bool>("Deleted");
            modelBuilder.Entity<VoyageComment>().HasQueryFilter(x => EF.Property<bool>(x, "Deleted") == false);
            modelBuilder.Entity<VoyageComment>().HasOne(p => p.CreatedBy)
                .WithMany()
                .HasForeignKey(f => f.CreatedById)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<VoyageComment>().HasOne(p => p.UpdatedBy)
                .WithMany()
                .HasForeignKey(f => f.UpdatedById)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<VoyageComment>().Navigation(x => x.CreatedBy)
                .AutoInclude();
            modelBuilder.Entity<VoyageComment>().Navigation(x => x.UpdatedBy)
                .AutoInclude();
            modelBuilder.Entity<VoyageComment>().HasOne(p => p.Voyage)
               .WithMany(f => f.VoyageComments)
               .HasForeignKey(f => f.VoyageId)
               .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<VoyageComment>().Property(x => x.Comment)
                .HasMaxLength(255);

            modelBuilder.Entity<VoyageCargoBulk>().HasKey(x => x.VoyageCargoBulkId);
            modelBuilder.Entity<VoyageCargoBulk>().Property<bool>("Deleted");
            modelBuilder.Entity<VoyageCargoBulk>().HasQueryFilter(x => EF.Property<bool>(x, "Deleted") == false);
            modelBuilder.Entity<VoyageCargoBulk>().HasOne(p => p.CreatedBy)
                .WithMany()
                .HasForeignKey(f => f.CreatedById);
            modelBuilder.Entity<VoyageCargoBulk>().HasOne(p => p.UpdatedBy)
                .WithMany()
                .HasForeignKey(f => f.UpdatedById);
            modelBuilder.Entity<VoyageCargoBulk>().Navigation(x => x.CreatedBy)
                .AutoInclude();
            modelBuilder.Entity<VoyageCargoBulk>().Navigation(x => x.UpdatedBy)
                .AutoInclude();
            modelBuilder.Entity<VoyageCargoBulk>().HasOne(p => p.Voyage)
                .WithMany(p => p.VoyageCargoBulks)
                .HasForeignKey(f => f.VoyageId);
            modelBuilder.Entity<VoyageCargoBulk>().HasOne(p => p.Vendor)
                .WithMany()
                .HasForeignKey(f => f.VendorId);
            modelBuilder.Entity<VoyageCargoBulk>().HasOne(p => p.VoyageCargoBulkDangerousGood)
                .WithMany()
                .HasForeignKey(f => f.VoyageCargoBulkDangerousGoodId)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<VoyageCargoBulk>().HasOne(p => p.Asset)
                .WithMany(x => x.VoyageCargoBulks)
                .HasForeignKey(f => f.AssetId);
            modelBuilder.Entity<VoyageCargoBulk>().HasOne(p => p.BulkType)
                .WithMany()
                .HasForeignKey(f => f.BulkTypeId)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<VoyageCargoBulk>().HasOne(p => p.Site)
                .WithMany()
                .HasForeignKey(f => f.SiteId);
            modelBuilder.Entity<VoyageCargoBulk>().Property(p => p.UnitName)
                .HasMaxLength(255);
            modelBuilder.Entity<VoyageCargoBulk>().Property(p => p.TypeOfBulk)
                .HasMaxLength(255);
            modelBuilder.Entity<VoyageCargoBulk>().HasOne(p => p.WeightCategory)
                .WithMany()
                .HasForeignKey(f => f.WeightCategoryId)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<VoyageCargoBulk>().HasIndex(x => new { x.RowNumber, x.VoyageId })
                .HasDatabaseName("IX_VoyageCargoBulks_RowNumber_VoyageId");

            #region Flow Voyage Bulk Cargo Dangerous Good

            modelBuilder.Entity<VoyageCargoBulkDangerousGood>().HasKey(x => x.VoyageCargoBulkDangerousGoodId);
            modelBuilder.Entity<VoyageCargoBulkDangerousGood>().HasOne(x => x.DangerousGood)
                .WithMany(x => x.VoyageCargoBulkDangerousGoods)
                .HasForeignKey(x => x.DangerousGoodId)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<VoyageCargoBulkDangerousGood>().HasOne(p => p.CreatedBy)
                .WithMany()
                .HasForeignKey(f => f.CreatedById)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<VoyageCargoBulkDangerousGood>().HasOne(p => p.UpdatedBy)
                .WithMany()
                .HasForeignKey(f => f.UpdatedById)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<VoyageCargoBulkDangerousGood>().Navigation(x => x.CreatedBy)
                .AutoInclude();
            modelBuilder.Entity<VoyageCargoBulkDangerousGood>().Navigation(x => x.UpdatedBy)
                .AutoInclude();
            modelBuilder.Entity<VoyageCargoBulkDangerousGood>().Property<bool>("Deleted");
            modelBuilder.Entity<VoyageCargoBulkDangerousGood>().HasQueryFilter(x => EF.Property<bool>(x, "Deleted") == false);

            #endregion

            modelBuilder.Entity<VoyageAttachment>().HasKey(x => x.VoyageAttachmentId);
            modelBuilder.Entity<VoyageAttachment>().Property<bool>("Deleted");
            modelBuilder.Entity<VoyageAttachment>().HasQueryFilter(x => EF.Property<bool>(x, "Deleted") == false);
            modelBuilder.Entity<VoyageAttachment>().HasOne(p => p.CreatedBy)
                .WithMany()
                .HasForeignKey(f => f.CreatedById);
            modelBuilder.Entity<VoyageAttachment>().Navigation(x => x.CreatedBy)
                .AutoInclude();
            modelBuilder.Entity<VoyageAttachment>().HasOne(p => p.Voyage)
               .WithMany(f => f.VoyageAttachments)
               .HasForeignKey(f => f.VoyageId)
               .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<VoyageAttachment>().Property(p => p.FileName)
                .HasMaxLength(255);
            modelBuilder.Entity<VoyageAttachment>().Property(p => p.Comment)
                .HasMaxLength(255);

            modelBuilder.Entity<VoyageMaterialDetail>().HasKey(x => x.VoyageMaterialDetailId);
            modelBuilder.Entity<VoyageMaterialDetail>().Property<bool>("Deleted");
            modelBuilder.Entity<VoyageMaterialDetail>().HasQueryFilter(x => EF.Property<bool>(x, "Deleted") == false);
            modelBuilder.Entity<VoyageMaterialDetail>().HasOne(p => p.CreatedBy)
                .WithMany()
                .HasForeignKey(f => f.CreatedById);
            modelBuilder.Entity<VoyageMaterialDetail>().HasOne(p => p.UpdatedBy)
                .WithMany()
                .HasForeignKey(f => f.UpdatedById);
            modelBuilder.Entity<VoyageMaterialDetail>().Navigation(x => x.CreatedBy)
                .AutoInclude();
            modelBuilder.Entity<VoyageMaterialDetail>().Navigation(x => x.UpdatedBy)
                .AutoInclude();
            modelBuilder.Entity<VoyageMaterialDetail>().HasOne(p => p.Voyage)
                .WithMany(p => p.MaterialDetails)
                .HasForeignKey(f => f.VoyageId);
            modelBuilder.Entity<VoyageMaterialDetail>().HasOne(p => p.Vendor)
                .WithMany()
                .HasForeignKey(f => f.VendorId);
            modelBuilder.Entity<VoyageMaterialDetail>().HasOne(p => p.VoyageMaterialDetailDangerousGood)
                .WithMany()
                .HasForeignKey(f => f.VoyageMaterialDetailDangerousGoodId)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<VoyageMaterialDetail>().HasOne(p => p.VoyageCargoBulk)
                .WithMany()
                .HasForeignKey(f => f.VoyageCargoBulkId)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<VoyageMaterialDetail>().HasOne(p => p.VoyageCargo)
                .WithMany(vc => vc.MaterialDetails)
                .HasForeignKey(f => f.VoyageCargoId)
                .OnDelete(DeleteBehavior.Restrict);
            modelBuilder.Entity<VoyageMaterialDetail>().Property(p => p.PackingUnit)
                .HasMaxLength(255);
            modelBuilder.Entity<VoyageMaterialDetail>().Property(p => p.MaterialDescription)
                .HasMaxLength(255);
            modelBuilder.Entity<VoyageMaterialDetail>().Property(p => p.PONo)
                .HasMaxLength(255);
            modelBuilder.Entity<VoyageMaterialDetail>().Property(p => p.Requester)
                .HasMaxLength(255);
            modelBuilder.Entity<VoyageMaterialDetail>().Property(p => p.WhsLocation)
                .HasMaxLength(255);
            modelBuilder.Entity<VoyageMaterialDetail>().Property(p => p.DestinationLocation)
                .HasMaxLength(255);
            modelBuilder.Entity<VoyageMaterialDetail>().Property(p => p.Phone)
                .HasMaxLength(255);
            modelBuilder.Entity<VoyageMaterialDetail>().Property(p => p.POTransport)
                .HasMaxLength(255);
            modelBuilder.Entity<VoyageMaterialDetail>().Property(p => p.Comments)
                .HasMaxLength(1023);
            modelBuilder.Entity<VoyageMaterialDetail>().Property(p => p.DocumentNo)
                .HasMaxLength(255);
            modelBuilder.Entity<VoyageMaterialDetail>().Property(p => p.SerialNo)
                .HasMaxLength(255);
            modelBuilder.Entity<VoyageMaterialDetail>().Property(p => p.ManifestNo)
                .HasMaxLength(255);
            modelBuilder.Entity<VoyageMaterialDetail>().Property(p => p.Value)
                .HasMaxLength(255);
            modelBuilder.Entity<VoyageMaterialDetail>().Property(p => p.Category)
                .HasMaxLength(255);
            modelBuilder.Entity<VoyageMaterialDetail>().Property(p => p.ProperShippingName)
                .HasMaxLength(255);
            modelBuilder.Entity<VoyageMaterialDetail>().Property(p => p.Class)
                .HasMaxLength(255);
            modelBuilder.Entity<VoyageMaterialDetail>().Property(p => p.SubClass)
                .HasMaxLength(255);
            modelBuilder.Entity<VoyageMaterialDetail>().Property(p => p.COO)
                .HasMaxLength(255);
            modelBuilder.Entity<VoyageMaterialDetail>().Property(p => p.CommodityCode)
                .HasMaxLength(255);
            modelBuilder.Entity<VoyageMaterialDetail>().Property(p => p.JobCardNumber)
                .HasMaxLength(255);
            modelBuilder.Entity<VoyageMaterialDetail>().Property(p => p.WorkOrderNumber)
                .HasMaxLength(255);
            modelBuilder.Entity<VoyageMaterialDetail>().Property(p => p.MIVMMT)
                .HasMaxLength(255);
            modelBuilder.Entity<VoyageMaterialDetail>().Property(p => p.Currency)
                .HasMaxLength(255);
            modelBuilder.Entity<VoyageMaterialDetail>().HasIndex(x => new { x.RowNumber, x.VoyageId })
                .HasDatabaseName("IX_VoyageMaterialDetails_RowNumber_VoyageId");

            modelBuilder.Entity<VoyageMaterialDetailDangerousGood>().HasKey(x => x.VoyageMaterialDetailDangerousGoodId);
            modelBuilder.Entity<VoyageMaterialDetailDangerousGood>().HasOne(x => x.VoyageCargoDangerousGood)
                .WithMany()
                .HasForeignKey(x => x.VoyageCargoDangerousGoodId)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<VoyageMaterialDetailDangerousGood>().HasOne(x => x.VoyageCargoBulkDangerousGood)
                .WithMany()
                .HasForeignKey(x => x.VoyageBulkCargoDangerousGoodId)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<VoyageMaterialDetailDangerousGood>().HasOne(x => x.CreatedBy).WithMany().HasForeignKey(x => x.CreatedById);
            modelBuilder.Entity<VoyageMaterialDetailDangerousGood>().HasOne(x => x.UpdatedBy).WithMany().HasForeignKey(x => x.UpdatedById);
            modelBuilder.Entity<VoyageMaterialDetailDangerousGood>().Navigation(x => x.CreatedBy).AutoInclude();
            modelBuilder.Entity<VoyageMaterialDetailDangerousGood>().Navigation(x => x.UpdatedBy).AutoInclude();

            modelBuilder.Entity<VoyageInspection>().HasKey(x => x.VoyageInspectionId);
            modelBuilder.Entity<VoyageInspection>().Property<bool>("Deleted");
            modelBuilder.Entity<VoyageInspection>().HasQueryFilter(x => EF.Property<bool>(x, "Deleted") == false);
            modelBuilder.Entity<VoyageInspection>().HasOne(p => p.CreatedBy)
                .WithMany()
                .HasForeignKey(f => f.CreatedById);
            modelBuilder.Entity<VoyageInspection>().HasOne(p => p.UpdatedBy)
                .WithMany()
                .HasForeignKey(f => f.UpdatedById);
            modelBuilder.Entity<VoyageInspection>().Navigation(x => x.CreatedBy)
                .AutoInclude();
            modelBuilder.Entity<VoyageInspection>().Navigation(x => x.UpdatedBy)
                .AutoInclude();
            modelBuilder.Entity<VoyageInspection>().HasOne(p => p.Voyage)
                .WithOne()
                .HasForeignKey<VoyageInspection>(x => x.VoyageId)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<VoyageInspection>().HasMany(p => p.InspectionCargos)
                .WithOne(p => p.VoyageInspection)
                .HasForeignKey(f => f.VoyageInspectionId)
                .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<VoyageCargoInspection>().HasKey(x => x.VoyageCargoInspectionId);
            modelBuilder.Entity<VoyageCargoInspection>().Property<bool>("Deleted");
            modelBuilder.Entity<VoyageCargoInspection>().HasQueryFilter(x => EF.Property<bool>(x, "Deleted") == false);
            modelBuilder.Entity<VoyageCargoInspection>().HasOne(p => p.CreatedBy)
                .WithMany()
                .HasForeignKey(f => f.CreatedById);
            modelBuilder.Entity<VoyageCargoInspection>().HasOne(p => p.UpdatedBy)
                .WithMany()
                .HasForeignKey(f => f.UpdatedById);
            modelBuilder.Entity<VoyageCargoInspection>().Navigation(x => x.CreatedBy)
                .AutoInclude();
            modelBuilder.Entity<VoyageCargoInspection>().Navigation(x => x.UpdatedBy)
                .AutoInclude();
            modelBuilder.Entity<VoyageCargoInspection>().HasOne(p => p.VoyageInspection)
                .WithMany(p => p.InspectionCargos)
                .HasForeignKey(f => f.VoyageInspectionId)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<VoyageCargoInspection>().HasOne(p => p.VoyageCargo)
                .WithOne(x => x.VoyageCargoInspection)
                .HasForeignKey<VoyageCargoInspection>(f => f.VoyageCargoId)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<VoyageCargoInspection>().HasMany(p => p.Attachments)
                .WithOne(p => p.VoyageCargoInspection)
                .HasForeignKey(f => f.VoyageCargoInspectionId)
                .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<VoyageCargoInspectionAttachment>().HasKey(x => x.VoyageCargoInspectionAttachmentId);
            modelBuilder.Entity<VoyageCargoInspectionAttachment>().Property<bool>("Deleted");
            modelBuilder.Entity<VoyageCargoInspectionAttachment>().HasQueryFilter(x => EF.Property<bool>(x, "Deleted") == false);
            modelBuilder.Entity<VoyageCargoInspectionAttachment>().HasOne(p => p.CreatedBy)
                .WithMany()
                .HasForeignKey(f => f.CreatedById);
            modelBuilder.Entity<VoyageCargoInspectionAttachment>().Navigation(x => x.CreatedBy)
                .AutoInclude();
            modelBuilder.Entity<VoyageCargoInspectionAttachment>().HasOne(p => p.VoyageCargoInspection)
                .WithMany(f => f.Attachments)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<VoyageCargoInspectionAttachment>().Property(p => p.FileName)
                .HasMaxLength(255);



            modelBuilder.Entity<Crane>().HasKey(x => x.CraneId);
            modelBuilder.Entity<Crane>().Property<bool>("Deleted");
            modelBuilder.Entity<Crane>().HasQueryFilter(x => EF.Property<bool>(x, "Deleted") == false);

            modelBuilder.Entity<Crane>().HasOne(p => p.CreatedBy)

                .WithMany()

                .HasForeignKey(f => f.CreatedById);
            modelBuilder.Entity<Crane>().Navigation(x => x.CreatedBy)
                .AutoInclude();
            modelBuilder.Entity<Crane>().HasOne(p => p.Location)
               .WithMany()
               .HasForeignKey(f => f.LocationId)
               .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<Crane>().Property(p => p.Name)
                .HasMaxLength(255);
            modelBuilder.Entity<Crane>().Property(p => p.Type)
                .HasMaxLength(255);

            modelBuilder.Entity<VoyageReservedArea>().HasKey(x => x.VoyageReservedAreaId);
            modelBuilder.Entity<VoyageReservedArea>().Property<bool>("Deleted");
            modelBuilder.Entity<VoyageReservedArea>().HasQueryFilter(x => EF.Property<bool>(x, "Deleted") == false);
            modelBuilder.Entity<VoyageReservedArea>().HasOne(p => p.CreatedBy)
                .WithMany()
                .HasForeignKey(f => f.CreatedById)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<VoyageReservedArea>().HasOne(p => p.UpdatedBy)
                .WithMany()
                .HasForeignKey(f => f.UpdatedById)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<VoyageReservedArea>().Navigation(x => x.CreatedBy)
                .AutoInclude();
            modelBuilder.Entity<VoyageReservedArea>().Navigation(x => x.UpdatedBy)
                .AutoInclude();
            modelBuilder.Entity<VoyageReservedArea>().HasOne(p => p.Voyage)
               .WithMany(f => f.VoyageReservedAreas)
               .HasForeignKey(f => f.VoyageId)
               .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<VoyageReservedArea>().Property(x => x.Name)
                .HasMaxLength(255);

            modelBuilder.Entity<VoyagePlanningDetail>().HasKey(x => x.VoyagePlanningDetailId);
            modelBuilder.Entity<VoyagePlanningDetail>().HasOne(p => p.CreatedBy)
                .WithMany()
                .HasForeignKey(f => f.CreatedById)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<VoyagePlanningDetail>().Navigation(x => x.CreatedBy)
                .AutoInclude();
            modelBuilder.Entity<VoyagePlanningDetail>().HasOne(p => p.Voyage)
               .WithMany(f => f.VoyagePlanningDetails)
               .HasForeignKey(f => f.VoyageId)
               .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<VoyagePlanningDetail>().HasOne(p => p.Area)
               .WithMany()
               .HasForeignKey(f => f.AreaId)
               .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<VoyagePlanningDetail>().Property(x => x.Comment)
                .HasMaxLength(1024);
            modelBuilder.Entity<VoyagePlanningDetail>().Property(x => x.TuNumber)
                .HasMaxLength(255);

            modelBuilder.Entity<VoyageSpecialNote>().HasKey(x => x.VoyageSpecialNoteId);
            modelBuilder.Entity<VoyageSpecialNote>().HasOne(p => p.CreatedBy)
                .WithMany()
                .HasForeignKey(f => f.CreatedById)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<VoyageSpecialNote>().Navigation(x => x.CreatedBy)
                .AutoInclude();
            modelBuilder.Entity<VoyageSpecialNote>().HasOne(p => p.Voyage)
               .WithMany(f => f.VoyageSpecialNotes)
               .HasForeignKey(f => f.VoyageId)
               .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<VoyageSpecialNote>().Property(x => x.Comment)
                .HasMaxLength(1024);

            modelBuilder.Entity<Squad>().HasKey(x => x.SquadId);
            modelBuilder.Entity<Squad>().Property<bool>("Deleted");
            modelBuilder.Entity<Squad>().HasQueryFilter(x => EF.Property<bool>(x, "Deleted") == false);
            modelBuilder.Entity<Squad>().HasOne(p => p.CreatedBy)
                .WithMany()
                .HasForeignKey(f => f.CreatedById)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<Squad>().HasOne(p => p.UpdatedBy)
                .WithMany()
                .HasForeignKey(f => f.UpdatedById)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<Squad>().Navigation(x => x.CreatedBy)
                .AutoInclude();
            modelBuilder.Entity<Squad>().Navigation(x => x.UpdatedBy)
                .AutoInclude();
            modelBuilder.Entity<Squad>().HasMany(p => p.SquadEmployees)
               .WithOne(x => x.Squad)
               .HasForeignKey(f => f.SquadId)
               .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<Squad>().HasOne(p => p.Location)
               .WithMany()
               .HasForeignKey(f => f.LocationId)
               .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<Squad>().Property(x => x.SquadName)
                .HasMaxLength(255);

            modelBuilder.Entity<Employee>().HasKey(x => x.EmployeeId);
            modelBuilder.Entity<Employee>().Property<bool>("Deleted");
            modelBuilder.Entity<Employee>().HasQueryFilter(x => EF.Property<bool>(x, "Deleted") == false);
            modelBuilder.Entity<Employee>().HasOne(p => p.CreatedBy)
                .WithMany()
                .HasForeignKey(f => f.CreatedById)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<Employee>().HasOne(p => p.UpdatedBy)
                .WithMany()
                .HasForeignKey(f => f.UpdatedById)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<Employee>().Navigation(x => x.CreatedBy)
                .AutoInclude();
            modelBuilder.Entity<Employee>().Navigation(x => x.UpdatedBy)
                .AutoInclude();
            modelBuilder.Entity<Employee>().HasMany(p => p.SquadEmployees)
               .WithOne(x => x.Employee)
               .HasForeignKey(f => f.EmployeeId)
               .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<Employee>().Property(x => x.Name)
                .HasMaxLength(255);

            modelBuilder.Entity<SquadEmployee>().HasKey(x => x.SquadEmployeeId);
            modelBuilder.Entity<SquadEmployee>().Property<bool>("Deleted");
            modelBuilder.Entity<SquadEmployee>().HasQueryFilter(x => EF.Property<bool>(x, "Deleted") == false);
            modelBuilder.Entity<SquadEmployee>().HasOne(p => p.CreatedBy)
                .WithMany()
                .HasForeignKey(f => f.CreatedById)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<SquadEmployee>().HasOne(p => p.Employee)
                .WithMany(x => x.SquadEmployees)
                .HasForeignKey(f => f.EmployeeId)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<SquadEmployee>().HasOne(p => p.Squad)
                .WithMany(x => x.SquadEmployees)
                .HasForeignKey(f => f.SquadId)
                .OnDelete(DeleteBehavior.NoAction);


            modelBuilder.Entity<LoadCell>().HasKey(x => x.LoadCellId);
            modelBuilder.Entity<LoadCell>().Property<bool>("Deleted");
            modelBuilder.Entity<LoadCell>().HasQueryFilter(x => EF.Property<bool>(x, "Deleted") == false);
            modelBuilder.Entity<LoadCell>().HasOne(p => p.CreatedBy)
                .WithMany()
                .HasForeignKey(f => f.CreatedById)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<LoadCell>().HasOne(p => p.UpdatedBy)
                .WithMany()
                .HasForeignKey(f => f.UpdatedById)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<LoadCell>().Navigation(x => x.CreatedBy)
                .AutoInclude();
            modelBuilder.Entity<LoadCell>().Navigation(x => x.UpdatedBy)
                .AutoInclude();
            modelBuilder.Entity<LoadCell>().HasOne(p => p.Location)
               .WithMany()
               .HasForeignKey(f => f.LocationId)
               .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<LoadCell>().Property(x => x.IPAddress)
                .HasMaxLength(45);
            modelBuilder.Entity<LoadCell>().Property(x => x.Id)
                .HasMaxLength(255);
            modelBuilder.Entity<LoadCell>().Property(x => x.DataTag)
                .HasMaxLength(255);
            modelBuilder.Entity<LoadCell>().Property(x => x.Description)
                .HasMaxLength(255);
            modelBuilder.Entity<LoadCell>().Property(x => x.PortNumber)
                .HasMaxLength(255);
            modelBuilder.Entity<LoadCell>().Property(x => x.Name)
                .HasMaxLength(255);


            modelBuilder.Entity<LiftingPlan>().HasKey(x => x.LiftingPlanId);
            modelBuilder.Entity<LiftingPlan>().HasOne(p => p.CreatedBy)
                .WithMany()
                .HasForeignKey(f => f.CreatedById)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<LiftingPlan>().HasOne(p => p.UpdatedBy)
                .WithMany()
                .HasForeignKey(f => f.UpdatedById)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<LiftingPlan>().Navigation(x => x.LiftingPlanEmployees)
                .AutoInclude();
            modelBuilder.Entity<LiftingPlan>().Navigation(x => x.LiftingPlanResources)
                .AutoInclude();
            modelBuilder.Entity<LiftingPlan>().Navigation(x => x.CreatedBy)
                .AutoInclude();
            modelBuilder.Entity<LiftingPlan>().Navigation(x => x.UpdatedBy)
                .AutoInclude();
            modelBuilder.Entity<LiftingPlan>().HasOne(p => p.Location)
               .WithMany()
               .HasForeignKey(f => f.LocationId)
               .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<LiftingPlan>().HasOne(p => p.Squad)
               .WithMany()
               .HasForeignKey(f => f.SquadId)
               .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<LiftingPlan>().HasOne(p => p.Area)
               .WithMany()
               .HasForeignKey(f => f.AreaId)
               .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<LiftingPlan>().HasOne(p => p.Crane)
               .WithMany()
               .HasForeignKey(f => f.CraneId)
               .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<LiftingPlan>().HasOne(p => p.Voyage)
               .WithMany(x => x.LiftingPlans)
               .HasForeignKey(f => f.VoyageId)
               .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<LiftingPlan>().Property(x => x.FirstColorCode)
                .HasMaxLength(9);
            modelBuilder.Entity<LiftingPlan>().Property(x => x.SecondColorCode)
                .HasMaxLength(9);
            modelBuilder.Entity<LiftingPlan>().Property(x => x.PlannerSignature)
                .HasMaxLength(8128);
            modelBuilder.Entity<LiftingPlan>().Property(x => x.PlannerName)
                .HasMaxLength(255);


            modelBuilder.Entity<LiftingPlanEmployee>().HasKey(x => x.LiftingPlanEmployeeId);
            modelBuilder.Entity<LiftingPlanEmployee>().Property<bool>("Deleted");
            modelBuilder.Entity<LiftingPlanEmployee>().HasQueryFilter(x => EF.Property<bool>(x, "Deleted") == false);
            modelBuilder.Entity<LiftingPlanEmployee>().HasOne(p => p.CreatedBy)
                .WithMany()
                .HasForeignKey(f => f.CreatedById)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<LiftingPlanEmployee>().HasOne(p => p.UpdatedBy)
                .WithMany()
                .HasForeignKey(f => f.UpdatedById)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<LiftingPlanEmployee>().Navigation(x => x.CreatedBy)
                .AutoInclude();
            modelBuilder.Entity<LiftingPlanEmployee>().Navigation(x => x.UpdatedBy)
                .AutoInclude();
            modelBuilder.Entity<LiftingPlanEmployee>().HasOne(p => p.Employee)
               .WithMany()
               .HasForeignKey(f => f.EmployeeId)
               .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<LiftingPlanEmployee>().HasOne(p => p.LiftingPlan)
               .WithMany(x => x.LiftingPlanEmployees)
               .HasForeignKey(f => f.LiftingPlanId)
               .OnDelete(DeleteBehavior.NoAction);


            modelBuilder.Entity<LiftingPlanResource>().HasKey(x => x.LiftingPlanResourceId);
            modelBuilder.Entity<LiftingPlanResource>().Property<bool>("Deleted");
            modelBuilder.Entity<LiftingPlanResource>().HasQueryFilter(x => EF.Property<bool>(x, "Deleted") == false);
            modelBuilder.Entity<LiftingPlanResource>().HasOne(p => p.CreatedBy)
               .WithMany()
               .HasForeignKey(f => f.CreatedById)
               .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<LiftingPlanResource>().HasOne(p => p.UpdatedBy)
               .WithMany()
               .HasForeignKey(f => f.UpdatedById)
               .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<LiftingPlanResource>().Navigation(x => x.CreatedBy)
               .AutoInclude();
            modelBuilder.Entity<LiftingPlanResource>().Navigation(x => x.UpdatedBy)
               .AutoInclude();
            modelBuilder.Entity<LiftingPlanResource>().HasOne(p => p.LiftingPlan)
               .WithMany(x => x.LiftingPlanResources)
               .HasForeignKey(f => f.LiftingPlanId)
               .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<LiftingPlanResource>().Property(x => x.Name)
               .HasMaxLength(255);
            modelBuilder.Entity<LiftingPlanResource>().Property(x => x.EquipmentType)
               .HasMaxLength(255);
            modelBuilder.Entity<LiftingPlanResource>().Property(x => x.Equipment)
               .HasMaxLength(255);


            modelBuilder.Entity<PauseReason>().HasKey(x => x.PauseReasonId);
            modelBuilder.Entity<PauseReason>().Property<bool>("Deleted");
            modelBuilder.Entity<PauseReason>().HasQueryFilter(x => EF.Property<bool>(x, "Deleted") == false);
            modelBuilder.Entity<PauseReason>().HasOne(p => p.CreatedBy)
               .WithMany()
               .HasForeignKey(f => f.CreatedById)
               .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<PauseReason>().HasOne(p => p.UpdatedBy)
               .WithMany()
               .HasForeignKey(f => f.UpdatedById)
               .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<PauseReason>().Navigation(x => x.CreatedBy)
               .AutoInclude();
            modelBuilder.Entity<PauseReason>().Navigation(x => x.UpdatedBy)
               .AutoInclude();
            modelBuilder.Entity<PauseReason>().HasMany(p => p.VoyageLiftingJobs)
               .WithOne(x => x.PauseReason)
               .HasForeignKey(f => f.PauseReasonId)
               .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<PauseReason>().Property(x => x.Name)
               .HasMaxLength(255);
            modelBuilder.Entity<PauseReason>().Property(x => x.Description)
               .HasMaxLength(255);


            modelBuilder.Entity<VoyageLiftingJob>().HasKey(x => x.VoyageLiftingJobId);
            modelBuilder.Entity<VoyageLiftingJob>().HasOne(p => p.CreatedBy)
               .WithMany()
               .HasForeignKey(f => f.CreatedById)
               .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<VoyageLiftingJob>().HasOne(p => p.UpdatedBy)
               .WithMany()
               .HasForeignKey(f => f.UpdatedById)
               .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<VoyageLiftingJob>().Navigation(x => x.CreatedBy)
               .AutoInclude();
            modelBuilder.Entity<VoyageLiftingJob>().Navigation(x => x.UpdatedBy)
               .AutoInclude();
            modelBuilder.Entity<VoyageLiftingJob>().HasOne(p => p.PauseReason)
               .WithMany(x => x.VoyageLiftingJobs)
               .HasForeignKey(f => f.PauseReasonId)
               .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<VoyageLiftingJob>().HasOne(p => p.Area)
               .WithMany()
               .HasForeignKey(f => f.AreaId)
               .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<VoyageLiftingJob>().HasOne(p => p.Voyage)
               .WithMany(x => x.VoyageLiftingJobs)
               .HasForeignKey(f => f.VoyageId)
               .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<VoyageLiftingJob>().Property(x => x.PauseDetail)
               .HasMaxLength(200);

            modelBuilder.Entity<WeightCategory>().HasKey(x => x.WeightCategoryId);
            modelBuilder.Entity<WeightCategory>().Property<bool>("Deleted");
            modelBuilder.Entity<WeightCategory>().HasQueryFilter(x => EF.Property<bool>(x, "Deleted") == false);
            modelBuilder.Entity<WeightCategory>().HasOne(p => p.CreatedBy)
                .WithMany()
                .HasForeignKey(f => f.CreatedById)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<WeightCategory>().HasOne(p => p.Location)
                .WithMany()
                .HasForeignKey(f => f.LocationId)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<WeightCategory>().HasOne(p => p.UpdatedBy)
                .WithMany()
                .HasForeignKey(f => f.UpdatedById)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<WeightCategory>().Navigation(x => x.CreatedBy)
                .AutoInclude();
            modelBuilder.Entity<WeightCategory>().Navigation(x => x.UpdatedBy)
                .AutoInclude();
            modelBuilder.Entity<WeightCategory>().Property(x => x.WeightType)
                .HasMaxLength(255);
            modelBuilder.Entity<WeightCategory>().Property(x => x.Price);
            modelBuilder.Entity<WeightCategory>().Property(x => x.IsActive).HasDefaultValue(true);
            
            modelBuilder.Entity<Trailer>().HasKey(p => p.TrailerId);
            modelBuilder.Entity<Trailer>().Property<bool>("Deleted");
            modelBuilder.Entity<Trailer>().HasQueryFilter(x => EF.Property<bool>(x, "Deleted") == false);
            modelBuilder.Entity<Trailer>().HasOne(p => p.CreatedBy)
                .WithMany()
                .HasForeignKey(f => f.CreatedById)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<Trailer>().HasOne(p => p.UpdatedBy)
                .WithMany()
                .HasForeignKey(f => f.UpdatedById)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<Trailer>().HasOne(p => p.Location)
                .WithMany()
                .HasForeignKey(f => f.LocationId)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<Trailer>().Navigation(x => x.CreatedBy)
                .AutoInclude();
            modelBuilder.Entity<Trailer>().Navigation(x => x.UpdatedBy)
                .AutoInclude();
            modelBuilder.Entity<Trailer>().Property(p => p.RegistrationNumber)
                .HasMaxLength(30);

            modelBuilder.Entity<Vehicle>().HasKey(x => x.VehicleId);
            modelBuilder.Entity<Vehicle>().Property<bool>("Deleted");
            modelBuilder.Entity<Vehicle>().HasQueryFilter(x => EF.Property<bool>(x, "Deleted") == false);
            modelBuilder.Entity<Vehicle>().HasOne(p => p.CreatedBy)
                .WithMany()
                .HasForeignKey(f => f.CreatedById)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<Vehicle>().HasOne(p => p.Location)
                .WithMany()
                .HasForeignKey(f => f.LocationId)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<Vehicle>().HasOne(p => p.UpdatedBy)
                .WithMany()
                .HasForeignKey(f => f.UpdatedById)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<Vehicle>().Navigation(x => x.CreatedBy)
                .AutoInclude();
            modelBuilder.Entity<Vehicle>().Navigation(x => x.UpdatedBy)
                .AutoInclude();
            modelBuilder.Entity<Vehicle>().Property(x => x.RegistrationNumber)
                .HasMaxLength(30);

            modelBuilder.Entity<Driver>().HasKey(x => x.DriverId);
            modelBuilder.Entity<Driver>().Property<bool>("Deleted");
            modelBuilder.Entity<Driver>().HasQueryFilter(x => EF.Property<bool>(x, "Deleted") == false);
            modelBuilder.Entity<Driver>().HasOne(p => p.CreatedBy)
                .WithMany()
                .HasForeignKey(f => f.CreatedById)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<Driver>().HasOne(p => p.UpdatedBy)
                .WithMany()
                .HasForeignKey(f => f.UpdatedById)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<Driver>().Navigation(x => x.CreatedBy)
                .AutoInclude();
            modelBuilder.Entity<Driver>().Navigation(x => x.UpdatedBy)
                .AutoInclude();
            modelBuilder.Entity<Driver>().HasOne(p => p.Location)
                .WithMany()
                .HasForeignKey(f => f.LocationId)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<Driver>().HasOne(p => p.Vendor)
                .WithMany()
                .HasForeignKey(f => f.VendorId)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<Driver>().HasOne(p => p.VendorWarehouse)
                .WithMany()
                .HasForeignKey(f => f.VendorWarehouseId)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<Driver>().HasOne(p => p.Vehicle)
                .WithMany()
                .HasForeignKey(f => f.VehicleId)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<Driver>().HasOne(p => p.Trailer)
                .WithMany()
                .HasForeignKey(f => f.TrailerId)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<Driver>().Property(x => x.FirstName)
                .HasMaxLength(50);
            modelBuilder.Entity<Driver>().Property(x => x.LastName)
                .HasMaxLength(50);

            modelBuilder.Entity<Pool>().HasKey(x => x.PoolId);
            modelBuilder.Entity<Pool>().Property<bool>("Deleted");
            modelBuilder.Entity<Pool>().HasQueryFilter(x => EF.Property<bool>(x, "Deleted") == false);
            modelBuilder.Entity<Pool>().HasOne(p => p.CreatedBy)
                            .WithMany()
                            .HasForeignKey(f => f.CreatedById)
                            .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<Pool>().HasOne(p => p.UpdatedBy)
                            .WithMany()
                            .HasForeignKey(f => f.UpdatedById)
                            .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<Pool>().Navigation(x => x.CreatedBy)
                            .AutoInclude();
            modelBuilder.Entity<Pool>().Navigation(x => x.UpdatedBy)
                            .AutoInclude();
            modelBuilder.Entity<Pool>().Property(x => x.Name)
                            .HasMaxLength(100);
            modelBuilder.Entity<Pool>().Property(x => x.IsActive).HasDefaultValue(true);


            modelBuilder.Entity<VoyageCargoLoad>().HasKey(x => x.VoyageCargoLoadId);
            modelBuilder.Entity<VoyageCargoLoad>().Property<bool>("Deleted");
            modelBuilder.Entity<VoyageCargoLoad>().HasQueryFilter(x => EF.Property<bool>(x, "Deleted") == false);
            modelBuilder.Entity<VoyageCargoLoad>().HasOne(p => p.CreatedBy)
                .WithMany()
                .HasForeignKey(f => f.CreatedById)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<VoyageCargoLoad>().HasOne(p => p.UpdatedBy)
                .WithMany()
                .HasForeignKey(f => f.UpdatedById)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<VoyageCargoLoad>().HasOne(p => p.Location)
                .WithMany()
                .HasForeignKey(f => f.LocationId)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<VoyageCargoLoad>().HasOne(p => p.Vehicle)
                .WithMany()
                .HasForeignKey(f => f.VehicleId)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<VoyageCargoLoad>().HasOne(p => p.Trailer)
                .WithMany()
                .HasForeignKey(f => f.TrailerId)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<VoyageCargoLoad>().HasOne(p => p.Driver)
                .WithMany()
                .HasForeignKey(f => f.DriverId)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<VoyageCargoLoad>().HasMany(p => p.VoyageCargos)
                .WithOne(p => p.VoyageCargoLoad)
                .HasForeignKey(f => f.VoyageCargoLoadId);
            modelBuilder.Entity<VoyageCargoLoad>().Navigation(x => x.CreatedBy)
                .AutoInclude();
            modelBuilder.Entity<VoyageCargoLoad>().Navigation(x => x.UpdatedBy)
                .AutoInclude();
            modelBuilder.Entity<VoyageCargoLoad>().Property(x => x.Comments)
                .HasMaxLength(255);
            modelBuilder.Entity<VoyageCargoLoad>().Property(p => p.LoadStatus).HasDefaultValue(VoyageCargoLoadStatus.PaperworkNotComplete);

            modelBuilder.Entity<ClusterHistory>().HasKey(x => x.ClusterHistoryId);
            modelBuilder.Entity<ClusterHistory>().HasOne(p => p.CreatedBy)
                .WithMany()
                .HasForeignKey(f => f.CreatedById)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<ClusterHistory>().HasOne(p => p.UpdatedBy)
                .WithMany()
                .HasForeignKey(f => f.UpdatedById)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<ClusterHistory>().HasOne(p => p.ClusterHead)
                .WithMany()
                .HasForeignKey(f => f.ClusterHeadId)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<ClusterHistory>().HasOne(p => p.ClusterChild)
                .WithMany()
                .HasForeignKey(f => f.ClusterChildId)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<ClusterHistory>().HasOne(p => p.ClusterHead)
                .WithMany(p => p.ClusterHistory)
                .HasForeignKey(f => f.ClusterHeadId);
            modelBuilder.Entity<VoyageCargoLoad>().Navigation(x => x.CreatedBy)
                .AutoInclude();
            modelBuilder.Entity<VoyageCargoLoad>().Navigation(x => x.UpdatedBy)
                .AutoInclude();


            modelBuilder.Entity<ClientNameHistory>().HasKey(x => x.ClientNameHistoryId);
            modelBuilder.Entity<ClientNameHistory>().HasOne(p => p.CreatedBy)
                .WithMany()
                .HasForeignKey(f => f.CreatedById)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<ClientNameHistory>().HasOne(p => p.UpdatedBy)
                .WithMany()
                .HasForeignKey(f => f.UpdatedById)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<ClientNameHistory>().HasOne(p => p.Client)
                .WithMany(x => x.ClientNameHistory)
                .HasForeignKey(f => f.ClientId)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<ClientNameHistory>().Navigation(x => x.CreatedBy)
                .AutoInclude();
            modelBuilder.Entity<ClientNameHistory>().Navigation(x => x.UpdatedBy)
                .AutoInclude();
            modelBuilder.Entity<ClientNameHistory>().Property(x => x.Name)
                .HasMaxLength(100);


            #region Contain related

            modelBuilder.Entity<CargoFamily>().HasKey(x => x.CargoFamilyId);
            modelBuilder.Entity<CargoFamily>().Property<bool>("Deleted");
            modelBuilder.Entity<CargoFamily>().HasQueryFilter(x => EF.Property<bool>(x, "Deleted") == false);
            modelBuilder.Entity<CargoFamily>().Property(x => x.Name).HasMaxLength(255);
            modelBuilder.Entity<CargoFamily>().Navigation(x => x.CreatedBy)
                .AutoInclude();
            modelBuilder.Entity<CargoFamily>().Navigation(x => x.UpdatedBy)
                .AutoInclude();
            modelBuilder.Entity<CargoFamily>().HasOne(p => p.CreatedBy)
                .WithMany()
                .HasForeignKey(f => f.CreatedById);
            modelBuilder.Entity<CargoFamily>().HasOne(p => p.UpdatedBy)
                .WithMany()
                .HasForeignKey(f => f.UpdatedById);
            modelBuilder.Entity<CargoFamily>().HasMany(p => p.CargoSizes)
                .WithOne(p => p.CargoFamily)
                .HasForeignKey(f => f.CargoFamilyId);
            modelBuilder.Entity<CargoFamily>().HasMany(p => p.CargoTypes)
                .WithOne(p => p.CargoFamily)
                .HasForeignKey(f => f.CargoFamilyId);

            modelBuilder.Entity<CargoSize>().HasKey(x => x.CargoSizeId);
            modelBuilder.Entity<CargoSize>().Property<bool>("Deleted");
            modelBuilder.Entity<CargoSize>().HasQueryFilter(x => EF.Property<bool>(x, "Deleted") == false);
            modelBuilder.Entity<CargoSize>().Property(x => x.Name).HasMaxLength(255);
            modelBuilder.Entity<CargoSize>().Navigation(x => x.CargoFamily)
                .AutoInclude();
            modelBuilder.Entity<CargoSize>().HasOne(x => x.CargoFamily)
                .WithMany(x => x.CargoSizes)
                .HasForeignKey(x => x.CargoFamilyId);
            modelBuilder.Entity<CargoSize>().Navigation(x => x.CreatedBy)
                .AutoInclude();
            modelBuilder.Entity<CargoSize>().Navigation(x => x.UpdatedBy)
                .AutoInclude();
            modelBuilder.Entity<CargoSize>().HasOne(p => p.CreatedBy)
                .WithMany()
                .HasForeignKey(f => f.CreatedById);
            modelBuilder.Entity<CargoSize>().HasOne(p => p.UpdatedBy)
                .WithMany()
                .HasForeignKey(f => f.UpdatedById);

            modelBuilder.Entity<CargoType>().HasKey(x => x.CargoTypeId);
            modelBuilder.Entity<CargoType>().Property<bool>("Deleted");
            modelBuilder.Entity<CargoType>().HasQueryFilter(x => EF.Property<bool>(x, "Deleted") == false);
            modelBuilder.Entity<CargoType>().Property(x => x.Name).HasMaxLength(255);
            modelBuilder.Entity<CargoType>().Navigation(x => x.CargoFamily)
                .AutoInclude();
            modelBuilder.Entity<CargoType>().HasOne(x => x.CargoFamily)
                .WithMany(x => x.CargoTypes)
                .HasForeignKey(x => x.CargoFamilyId);
            modelBuilder.Entity<CargoType>().Navigation(x => x.CreatedBy)
                .AutoInclude();
            modelBuilder.Entity<CargoType>().Navigation(x => x.UpdatedBy)
                .AutoInclude();
            modelBuilder.Entity<CargoType>().HasOne(p => p.CreatedBy)
                .WithMany()
                .HasForeignKey(f => f.CreatedById);
            modelBuilder.Entity<CargoType>().HasOne(p => p.UpdatedBy)
                .WithMany()
                .HasForeignKey(f => f.UpdatedById);

            modelBuilder.Entity<MovementMatching>().HasKey(x => x.MovementMatchingId);
            modelBuilder.Entity<MovementMatching>().HasOne(p => p.HireRequestCargo)
                .WithMany()
                .HasForeignKey(f => f.HireRequestCargoId)
                .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<HireRequestCargo>().Property<bool>("Deleted");
            modelBuilder.Entity<HireRequestCargo>().HasQueryFilter(x => EF.Property<bool>(x, "Deleted") == false);

            modelBuilder.Entity<HireRequestCargo>().HasIndex(x => x.Deleted)
                .IsUnique(false);

            modelBuilder.Entity<HireRequestCargo>().HasKey(x => x.HireRequestCargoId);
            modelBuilder.Entity<HireRequestCargo>().Navigation(x => x.CreatedBy)
                .AutoInclude();
            modelBuilder.Entity<HireRequestCargo>().Navigation(x => x.UpdatedBy)
                .AutoInclude();
            modelBuilder.Entity<HireRequestCargo>().HasOne(p => p.CreatedBy)
                .WithMany()
                .HasForeignKey(f => f.CreatedById)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<HireRequestCargo>().HasOne(p => p.UpdatedBy)
                .WithMany()
                .HasForeignKey(f => f.UpdatedById)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<HireRequestCargo>().Navigation(x => x.Cargo)
                .AutoInclude();
            modelBuilder.Entity<HireRequestCargo>().HasOne(x => x.Cargo)
                .WithMany()
                .HasForeignKey(x => x.CargoId)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<HireRequestCargo>().HasOne(x => x.HireRequest)
                 .WithMany()
                 .HasForeignKey(x => x.HireRequestId)
                 .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<HireRequestCargo>().Property(x => x.HireCreated)
                .IsRequired();
            modelBuilder.Entity<HireRequestCargo>().Navigation(x => x.Vendor)
                .AutoInclude();
            modelBuilder.Entity<HireRequestCargo>().HasOne(x => x.Vendor)
                .WithMany()
                .HasForeignKey(x => x.VendorId);
            modelBuilder.Entity<HireRequestCargo>().Navigation(x => x.Client)
               .AutoInclude();
            modelBuilder.Entity<HireRequestCargo>().HasOne(x => x.Client)
                .WithMany()
                .HasForeignKey(x => x.ClientId)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<HireRequestCargo>().Navigation(x => x.Asset)
               .AutoInclude();
            modelBuilder.Entity<HireRequestCargo>().Navigation(x => x.BillingAsset)
              .AutoInclude();
            modelBuilder.Entity<HireRequestCargo>().HasOne(x => x.Asset)
                .WithMany()
                .HasForeignKey(x => x.AssetId);
            modelBuilder.Entity<HireRequestCargo>().HasOne(x => x.ContainerPool)
                .WithMany()
                .HasForeignKey(x => x.ContainerPoolId);
            modelBuilder.Entity<HireRequestCargo>().HasOne(x => x.BillingAsset)
                .WithMany()
                .HasForeignKey(x => x.BillingAssetId);
            modelBuilder.Entity<HireRequestCargo>().Property(x => x.Reference).HasMaxLength(255);
            modelBuilder.Entity<HireRequestCargo>().Property(x => x.ConsignmentNumber).HasMaxLength(255);
            modelBuilder.Entity<HireRequestCargo>().Property(x => x.VendorOutbound)
                .HasMaxLength(255);
            modelBuilder.Entity<HireRequestCargo>().Property(x => x.LongTermHire)
                .IsRequired();
            modelBuilder.Entity<HireRequestCargo>().Property(x => x.NetSupplied)
                .IsRequired().HasDefaultValue(false);
            modelBuilder.Entity<HireRequestCargo>().Property(x => x.NetReturned)
                .IsRequired().HasDefaultValue(false);
            modelBuilder.Entity<HireRequestCargo>().Property(x => x.TarpaulinSupplied)
                .IsRequired().HasDefaultValue(false);
            modelBuilder.Entity<HireRequestCargo>().Property(x => x.TarpaulinReturned)
                .IsRequired().HasDefaultValue(false);
            modelBuilder.Entity<HireRequestCargo>().Property(x => x.ShelvesSupplied)
                .IsRequired().HasDefaultValue(false);
            modelBuilder.Entity<HireRequestCargo>().Property(x => x.ShelvesReturned)
                .IsRequired().HasDefaultValue(false);
            modelBuilder.Entity<HireRequestCargo>().Property(x => x.Comments)
                .HasMaxLength(512);
            modelBuilder.Entity<HireRequestCargo>().Property(x => x.ManifestOut)
                .HasMaxLength(512);
            modelBuilder.Entity<HireRequestCargo>().Property(x => x.ManifestIn)
                .HasMaxLength(255);
            modelBuilder.Entity<HireRequestCargo>().Property(x => x.VendorOutbound)
                .HasMaxLength(255);
            modelBuilder.Entity<HireRequestCargo>().Property(x => x.VendorInbound)
                .HasMaxLength(255);

            modelBuilder.Entity<HireRequestCargoEvent>().HasKey(x => x.HireRequestCargoEventId);
            modelBuilder.Entity<HireRequestCargoEvent>().Property<bool>("Deleted");
            modelBuilder.Entity<HireRequestCargoEvent>().HasQueryFilter(x => EF.Property<bool>(x, "Deleted") == false);
            modelBuilder.Entity<HireRequestCargoEvent>().Property(x => x.Details);

            modelBuilder.Entity<HireRequestCargoEvent>().HasOne(x => x.HireRequestCargo)
                 .WithMany()
                 .HasForeignKey(x => x.HireRequestCargoId)
                 .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<HireRequestCargoEvent>().Navigation(x => x.CreatedBy)
                .AutoInclude();

            modelBuilder.Entity<HireRequestCargoEvent>().HasOne(p => p.CreatedBy)
                .WithMany()
                .HasForeignKey(f => f.CreatedById)
                .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<HireRequest>().Property(x => x.IsCancelled).IsRequired().HasDefaultValue(false);
            modelBuilder.Entity<HireRequest>().Property(x => x.IsHighlighted).IsRequired().HasDefaultValue(false);

            modelBuilder.Entity<HireRequest>().HasKey(x => x.HireRequestId);
            modelBuilder.Entity<HireRequest>().Property<bool>("Deleted");
            modelBuilder.Entity<HireRequest>().HasQueryFilter(x => EF.Property<bool>(x, "Deleted") == false);
            modelBuilder.Entity<HireRequest>().Navigation(x => x.CreatedBy)
                .AutoInclude();
            modelBuilder.Entity<HireRequest>().Navigation(x => x.UpdatedBy)
                .AutoInclude();
            modelBuilder.Entity<HireRequest>().HasOne(p => p.CreatedBy)
                .WithMany()
                .HasForeignKey(f => f.CreatedById)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<HireRequest>().HasOne(p => p.UpdatedBy)
                .WithMany()
                .HasForeignKey(f => f.UpdatedById)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<HireRequest>().Navigation(x => x.Client)
                .AutoInclude();
            modelBuilder.Entity<HireRequest>().HasOne(x => x.Client)
                .WithMany()
                .HasForeignKey(x => x.ClientId)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<HireRequest>().Navigation(x => x.Asset)
                .AutoInclude();
            modelBuilder.Entity<HireRequest>().HasOne(x => x.Asset)
                .WithMany()
                .HasForeignKey(x => x.AssetId);
            modelBuilder.Entity<HireRequest>().Property(x => x.RequestedDate)
                .IsRequired();
            modelBuilder.Entity<HireRequest>().Property(x => x.RequestedBy)
                .IsRequired();
            modelBuilder.Entity<HireRequest>().Property(x => x.UnitQuantity)
                .IsRequired();
            modelBuilder.Entity<HireRequest>().Property(x => x.Comments)
                .HasMaxLength(512);
            modelBuilder.Entity<HireRequest>().Property(x => x.CSTRequired)
                .IsRequired().HasDefaultValue(false);
            modelBuilder.Entity<HireRequest>().Property(x => x.NetRequired)
                .IsRequired().HasDefaultValue(false);
            modelBuilder.Entity<HireRequest>().Property(x => x.DoorsRequired)
                .IsRequired().HasDefaultValue(false);
            modelBuilder.Entity<HireRequest>().Property(x => x.TarpaulinRequired)
                .IsRequired().HasDefaultValue(false);
            modelBuilder.Entity<HireRequest>().Property(x => x.RemovableSidesRequired)
                .IsRequired().HasDefaultValue(false);
            modelBuilder.Entity<HireRequest>().Property(x => x.ShelvesRequired)
                .IsRequired().HasDefaultValue(false);
            modelBuilder.Entity<HireRequest>().Property(x => x.AwaitingCollection)
                .IsRequired().HasDefaultValue(false);
            modelBuilder.Entity<HireRequest>().Property(x => x.Status)
                .IsRequired().HasDefaultValue(HireRequestStatus.Waiting);
            modelBuilder.Entity<HireRequestCargo>().Navigation(x => x.Vendor)
                .AutoInclude();
            modelBuilder.Entity<HireRequest>().HasOne(x => x.Vendor)
                .WithMany()
                .HasForeignKey(x => x.VendorId)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<HireRequest>().Property(x => x.OrderTakenBy)
                .HasMaxLength(255);
            modelBuilder.Entity<HireRequest>().Property(x => x.ConfirmedBy)
                .HasMaxLength(255);
            modelBuilder.Entity<HireRequest>().Property(x => x.CollectionReference)
                .HasMaxLength(255);
            modelBuilder.Entity<HireRequest>().HasMany(x => x.Cargoes)
               .WithOne(x => x.HireRequest)
               .HasForeignKey(x => x.HireRequestId)
               .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<CargoEvent>().HasKey(x => x.CargoEventId);
            modelBuilder.Entity<CargoEvent>().Property<bool>("Deleted");
            modelBuilder.Entity<CargoEvent>().HasQueryFilter(x => EF.Property<bool>(x, "Deleted") == false);
            modelBuilder.Entity<CargoEvent>().Property(x => x.Details);

            modelBuilder.Entity<CargoEvent>().HasOne(x => x.Cargo)
                 .WithMany()
                 .HasForeignKey(x => x.CargoId)
                 .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<CargoEvent>().Navigation(x => x.CreatedBy)
                .AutoInclude();

            modelBuilder.Entity<CargoEvent>().HasOne(p => p.CreatedBy)
                .WithMany()
                .HasForeignKey(f => f.CreatedById)
                .OnDelete(DeleteBehavior.NoAction);
            #endregion

            #region ToolBoxTalkRelatedEntities

            modelBuilder.Entity<ToolBoxTalk>()
                .HasKey(t => t.ToolBoxTalkId);
            modelBuilder.Entity<ToolBoxTalk>().Property<bool>("Deleted");
            modelBuilder.Entity<ToolBoxTalk>().HasQueryFilter(x => EF.Property<bool>(x, "Deleted") == false);
            modelBuilder.Entity<ToolBoxTalk>()
                .Property(t => t.Deleted)
                .IsRequired();
            modelBuilder.Entity<ToolBoxTalk>().Navigation(x => x.CreatedBy)
                .AutoInclude();
            modelBuilder.Entity<ToolBoxTalk>().Navigation(x => x.UpdatedBy)
                .AutoInclude();
            modelBuilder.Entity<ToolBoxTalk>().HasOne(p => p.CreatedBy)
                .WithMany()
                .HasForeignKey(f => f.CreatedById)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<ToolBoxTalk>().HasOne(p => p.UpdatedBy)
                .WithMany()
                .HasForeignKey(f => f.UpdatedById)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<ToolBoxTalk>().Navigation(x => x.Location)
                .AutoInclude();
            modelBuilder.Entity<ToolBoxTalk>()
                .HasOne(x => x.Location)
                .WithMany()
                .HasForeignKey(x => x.LocationId)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<ToolBoxTalk>().Navigation(x => x.Squad)
                .AutoInclude();
            modelBuilder.Entity<ToolBoxTalk>().HasOne(x => x.Squad)
                .WithMany()
                .HasForeignKey(x => x.SquadId)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<ToolBoxTalk>()
                .HasMany(t => t.ToolBoxTalkSites)
                .WithOne()
                .HasForeignKey(a => a.ToolBoxTalkId)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<ToolBoxTalk>()
                .HasMany(t => t.ToolBoxTalkEmployees)
                .WithOne()
                .HasForeignKey(e => e.ToolBoxTalkId)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<ToolBoxTalk>()
                .Property(t => t.ExplainInDetailDescription)
                .HasColumnType("nvarchar(max)");
            modelBuilder.Entity<ToolBoxTalk>()
                .Property(t => t.ThirdPartyDescription)
                .HasColumnType("nvarchar(max)");
            modelBuilder.Entity<ToolBoxTalk>()
                .Property(t => t.WorkSiteOverViewDescription)
                .HasColumnType("nvarchar(max)");
            modelBuilder.Entity<ToolBoxTalk>()
                .Property(t => t.OverAllResponsibilityDescription)
                .HasColumnType("nvarchar(max)");
            modelBuilder.Entity<ToolBoxTalk>()
                .Property(t => t.AdditionalResourceDescription)
                .HasColumnType("nvarchar(max)");
            modelBuilder.Entity<ToolBoxTalk>()
                .Property(t => t.TaskRiskAssessmentDescription)
                .HasColumnType("nvarchar(max)");
            modelBuilder.Entity<ToolBoxTalk>()
                .Property(t => t.ProceduresAndInstructionsDescription)
                .HasColumnType("nvarchar(max)");
            modelBuilder.Entity<ToolBoxTalk>()
                .Property(t => t.OtherHazardsDescription)
                .HasColumnType("nvarchar(max)");
            modelBuilder.Entity<ToolBoxTalk>()
                .Property(t => t.ControlMeasureDescription)
                .HasColumnType("nvarchar(max)");
            modelBuilder.Entity<ToolBoxTalk>()
                .Property(t => t.QuestionsAskedDescription)
                .HasColumnType("nvarchar(max)");
            modelBuilder.Entity<ToolBoxTalk>()
                .Property(t => t.VersionNumber)
                .HasMaxLength(50);


            modelBuilder.Entity<ToolBoxTalkSite>().HasKey(t => t.ToolBoxTalkSiteId);
            modelBuilder.Entity<ToolBoxTalkSite>().Property<bool>("Deleted");
            modelBuilder.Entity<ToolBoxTalkSite>().HasQueryFilter(x => EF.Property<bool>(x, "Deleted") == false);
            modelBuilder.Entity<ToolBoxTalkSite>()
               .Property(t => t.Deleted)
               .IsRequired();
            modelBuilder.Entity<ToolBoxTalkSite>().Navigation(x => x.CreatedBy)
               .AutoInclude();
            modelBuilder.Entity<ToolBoxTalkSite>().Navigation(x => x.UpdatedBy)
                .AutoInclude();
            modelBuilder.Entity<ToolBoxTalkSite>().HasOne(p => p.CreatedBy)
                .WithMany()
                .HasForeignKey(f => f.CreatedById)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<ToolBoxTalkSite>().HasOne(p => p.UpdatedBy)
                .WithMany()
                .HasForeignKey(f => f.UpdatedById)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<ToolBoxTalkSite>()
                .HasOne(t => t.Site)
                .WithMany(t => t.ToolBoxTalkSites)
                .HasForeignKey(t => t.SiteId)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<ToolBoxTalkSite>()
                .HasOne(t => t.ToolBoxTalk)
                .WithMany(t => t.ToolBoxTalkSites)
                .HasForeignKey(t => t.ToolBoxTalkId)
                .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<ToolBoxTalkEmployee>()
                .HasKey(t => t.ToolBoxTalkEmployeeId);
            modelBuilder.Entity<ToolBoxTalkEmployee>().Property<bool>("Deleted");
            modelBuilder.Entity<ToolBoxTalkEmployee>().HasQueryFilter(x => EF.Property<bool>(x, "Deleted") == false);
            modelBuilder.Entity<ToolBoxTalkEmployee>()
               .Property(t => t.Deleted)
               .IsRequired();
            modelBuilder.Entity<ToolBoxTalkEmployee>().Navigation(x => x.CreatedBy)
               .AutoInclude();
            modelBuilder.Entity<ToolBoxTalkEmployee>().Navigation(x => x.UpdatedBy)
                .AutoInclude();
            modelBuilder.Entity<ToolBoxTalkEmployee>().HasOne(p => p.CreatedBy)
                .WithMany()
                .HasForeignKey(f => f.CreatedById)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<ToolBoxTalkEmployee>().HasOne(p => p.UpdatedBy)
                .WithMany()
                .HasForeignKey(f => f.UpdatedById)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<ToolBoxTalkEmployee>()
                .HasOne(t => t.ToolBoxTalk)
                .WithMany(t => t.ToolBoxTalkEmployees)
                .HasForeignKey(t => t.ToolBoxTalkId)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<ToolBoxTalkEmployee>()
                .HasOne(t => t.Employee)
                .WithMany()
                .HasForeignKey(t => t.EmployeeId)
                .OnDelete(DeleteBehavior.NoAction);
            modelBuilder.Entity<ToolBoxTalkEmployee>()
                .Property(t => t.Signature)
                .HasMaxLength(70000);
            modelBuilder.Entity<ToolBoxTalkEmployee>()
                .Property(t => t.Deleted)
                .IsRequired();
            #endregion

        }
    }
}
