namespace Lighthouse.Service.Data.MasterData
{
    public class SailingRequestService : ISailingRequestService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IMapper _mapper;
        private readonly IUserService _userService;
        private readonly ITransportRequestService _transportRequestService;
        private readonly ITransportRequestCargoService _transportRequestCargoService;
        private readonly ITransportRequestBulkCargoService _transportRequestBulkCargoService;
        private readonly IVoyageService _voyageService;
        private readonly IRequestToFlowDataSyncService _requestToFlowDataSyncExtension;
        private readonly ITimeZoneConversionService _timeZoneConversionService;
        public SailingRequestService(IUnitOfWork unitOfWork,
            IMapper mapper,
            IUserService userService,
            ITransportRequestService transportRequestService,
            ITransportRequestCargoService transportRequestCargoService,
            ITransportRequestBulkCargoService transportRequestBulkCargoService,
            IVoyageService voyageService,
            IRequestToFlowDataSyncService requestToFlowDataSyncService,
            ITimeZoneConversionService timeZoneConversionService
            ) {
            _unitOfWork = unitOfWork;
            _mapper = mapper;
            _userService = userService;
            _transportRequestService = transportRequestService;
            _transportRequestCargoService = transportRequestCargoService;
            _transportRequestBulkCargoService = transportRequestBulkCargoService;
            _voyageService = voyageService;
            _requestToFlowDataSyncExtension = requestToFlowDataSyncService;
            _timeZoneConversionService = timeZoneConversionService;
        }
        public async Task<IList<SailingRequestModel>> GetAsync() {
            var sailingRequests = await _unitOfWork.Repository<SailingRequest>()
                .Query()
                .AsNoTracking()
                .AsSplitQuery()
                .Include(x => x.SailingRequestUserComments)
                .Include(x => x.SailingRequestActivities)
                    .ThenInclude(x => x.ActivityCategoryType)
                .Include(x => x.SailingRequestAssets)
                    .ThenInclude(x => x.Asset)
                .ToListAsync();
            var sailingRequestModels = _mapper.Map<List<SailingRequestModel>>(sailingRequests);
            return sailingRequestModels;
        }

        public async Task<IList<SailingRequestModel>> GetByLocationIdAsync(Guid locationId, DateOnly startDate, DateOnly endDate) {
            var sailingRequests = await _unitOfWork.Repository<SailingRequest>()
                .Query()
                .AsNoTracking()
                .AsSplitQuery()
                .Include(x => x.Client)
                .Include(x => x.Vessel)
                .Include(x => x.SailingRequestActivities)
                    .ThenInclude(x => x.ActivityCategoryType)
                    .ThenInclude(x => x.ActivityCategory)
                .Include(x => x.SailingRequestAssets)
                    .ThenInclude(x => x.Asset)
                .Include(x => x.SailingRequestUserComments)
                    .ThenInclude(x => x.CommentReadByUsers)
                        .ThenInclude(x => x.ReaderUser)
                .Include(x => x.TransportRequests)
                .Include(x => x.Cluster)
                .Where(x => x.LocationId == locationId &&
                    (x.StartTime <= endDate && x.EndTime >= startDate))
                .ToListAsync();            

            var sailingRequestsModels = _mapper.Map<List<SailingRequestModel>>(sailingRequests);

            foreach(var sailingRequestModel in sailingRequestsModels)
            {
                if (sailingRequestModel.InboundVoyageId.HasValue)
                    await CalculateLifts(sailingRequestModel, sailingRequestModel.InboundVoyageId.Value);

                if (sailingRequestModel.OutboundVoyageId.HasValue)
                    await CalculateLifts(sailingRequestModel, sailingRequestModel.OutboundVoyageId.Value);
            }

            return sailingRequestsModels;
        }

        public async Task CalculateLifts(SailingRequestModel sailingRequest, Guid voyageId)
        {
            var voyageCargoes = await _unitOfWork.Repository<VoyageCargo>()
                .Query()
                .AsNoTracking()
                .AsSplitQuery()
                .Include(x => x.Voyage)
                .Include(x => x.VoyageCargoLifts)                
                .Where(x => !x.Voyage.Deleted && x.VoyageId == voyageId  && x.RtRob != RtRob.RemainingOnBoard && !x.IsBumped && !x.IsCancelled && 
                (
                    (x.Voyage.VoyageStatus == VoyageStatus.Draft && x.Status == VoyageCargoStatus.Draft) ||
                    (x.Voyage.VoyageStatus != VoyageStatus.Draft && x.Status != VoyageCargoStatus.Draft)
                ))
                .ToListAsync();

            if(voyageCargoes.Count > 0)
            {
                var voyageDirection = voyageCargoes.First().Voyage.VoyageDirection;

                var voyageCargoLifts = voyageCargoes.SelectMany(x => x.VoyageCargoLifts);
                var cargoLifts = voyageCargoes.Sum(x => x.NumberOfLifts).Value;

                if(voyageDirection == VoyageDirection.Inbound)
                {
                    sailingRequest.InboundVoyageStatus = voyageCargoes.Count > 0 ? voyageCargoes.Select(s => s.Voyage).First().VoyageStatus : VoyageStatus.Draft;
                }
                else if(voyageDirection == VoyageDirection.Outbound)
                {
                    sailingRequest.OutboundVoyageStatus = voyageCargoes.Count > 0 ? voyageCargoes.Select(s => s.Voyage).First().VoyageStatus : VoyageStatus.Draft;
                }                

                if (voyageDirection == VoyageDirection.Inbound && sailingRequest.InboundVoyageStatus != VoyageStatus.Draft)
                {
                    sailingRequest.InboundLifts = voyageCargoLifts
                                       .Where(lift => !lift.Inactive)
                                       .Sum(lift => lift.CapturedWeightKg >= 0 ? 1 : -1);

                    sailingRequest.InboundLiftPercentage = voyageCargoLifts.Count() > 0 ? sailingRequest.InboundLifts / cargoLifts * 100 : 0;
                    sailingRequest.InboundAllCargoesCount = voyageCargoes.Count();
                    sailingRequest.InboundAtQuayCargoesCount = voyageCargoes.Where(w => w.ArrivalTime.HasValue).Count();
                    sailingRequest.InboundAtQuayCargoesPercentage = sailingRequest.InboundAllCargoesCount > 0 ? sailingRequest.InboundAtQuayCargoesCount / sailingRequest.InboundAllCargoesCount * 100 : 0;
                    sailingRequest.InboundTotalLiftsCount = cargoLifts;
                    
                }
                else if(voyageDirection == VoyageDirection.Outbound && sailingRequest.OutboundVoyageStatus != VoyageStatus.Draft)
                {
                    sailingRequest.OutboundLifts = voyageCargoLifts
                                       .Where(lift => !lift.Inactive)
                                       .Sum(lift => lift.CapturedWeightKg >= 0 ? 1 : -1);

                    sailingRequest.OutboundLiftPercentage = voyageCargoLifts.Count() > 0 ? sailingRequest.OutboundLifts / cargoLifts * 100 : 0;
                    sailingRequest.OutboundAllCargoesCount = voyageCargoes
                        .Where(w => !w.IsCancelled && !w.IsBumped && !w.IsMoved && w.RtRob != RtRob.RoundTrip && w.RtRob != RtRob.RemainingOnBoard).Count();
                    sailingRequest.OutboundAtQuayCargoesCount = voyageCargoes
                        .Where(w => w.ArrivalTime.HasValue && !w.IsCancelled && !w.IsBumped && !w.IsMoved && w.RtRob != RtRob.RoundTrip && w.RtRob != RtRob.RemainingOnBoard).Count();
                    sailingRequest.OutboundAtQuayCargoesPercentage = sailingRequest.OutboundAllCargoesCount > 0 ? sailingRequest.OutboundAtQuayCargoesCount / sailingRequest.OutboundAllCargoesCount * 100 : 0;
                    sailingRequest.OutboundTotalLiftsCount = cargoLifts;
                    
                }               
            }
        }

        public async Task<IList<SailingRequestModel>> GetInPortUnplannedRequests(Guid locationId, DateTime? endDate)
        {
            List<SailingRequest> sailingRequests = await _unitOfWork.Repository<SailingRequest>()
                .Query()
                .AsNoTracking()
                .AsSplitQuery()
                .Include(x => x.Client)
                .Include(x => x.Vessel)
                .Include(x => x.SailingRequestActivities
                    .Where(a => a.IsInPort))
                .ThenInclude(x => x.ActivityCategoryType)
                .ThenInclude(x => x.ActivityCategory)
                .Include(x => x.SailingRequestAssets)
                .ThenInclude(x => x.Asset)
                .Include(x => x.InboundVoyage)
                .Include(x => x.OutboundVoyage)
                .Where(x =>
                    x.LocationId == locationId &&
                    (x.InboundVoyageId != null || x.OutboundVoyageId != null) &&
                    x.SailingRequestActivities.Any(a => a.IsInPort)).ToListAsync();

            List<SailingRequestModel> sailingRequestModels = _mapper.Map<List<SailingRequestModel>>(sailingRequests);

            DateTime now = DateTime.Now;
            
            if (endDate.HasValue)
            {
                sailingRequestModels = sailingRequestModels.Where(h =>
                    h.EndEtd <= endDate.Value && 
                    h.EndEtd >= now
                ).ToList();
            }

            foreach (var request in sailingRequestModels)
            {
                request.SailingRequestActivities = request.SailingRequestActivities
                    .OrderBy(a => a.StartTime ?? DateTime.MinValue)
                    .ToList();
            }

            return sailingRequestModels;
        }
        
        public async Task<IList<SailingRequestModel>> GetOutOfPortUnplannedRequests(Guid locationId)
        {
            var sailingRequests = await _unitOfWork.Repository<SailingRequest>()
                .Query()
                .AsNoTracking()
                .AsSplitQuery()
                .Include(x => x.Client)
                .Include(x => x.Vessel)
                .Include(x => x.SailingRequestActivities
                    .Where(x => !x.IsInPort))
                .ThenInclude(x => x.ActivityCategoryType)
                .ThenInclude(x => x.ActivityCategory)
                .Include(x => x.SailingRequestAssets)
                    .ThenInclude(x => x.Asset)
                .Include(x => x.InboundVoyage)
                .Include(x => x.OutboundVoyage)
                .Where(x =>
                    x.LocationId == locationId &&
                    (x.InboundVoyageId != null || x.OutboundVoyageId != null) &&
                    x.SailingRequestActivities.Any(a => !a.IsInPort)
                )
                .ToListAsync();
            
            foreach (var request in sailingRequests)
            {
                request.SailingRequestActivities = request.SailingRequestActivities
                    .OrderBy(a => a.StartTime == null ? DateTime.MinValue : a.StartTime) 
                    .ToList();
            }
            
            return _mapper.Map<List<SailingRequestModel>>(sailingRequests);
        }

        public async Task<IList<SailingRequestUserCommentModel>> GetSailingRequestCommentsIdAsync(Guid sailingRequestId)
        {
            var sailingRequestUserComments = await _unitOfWork.Repository<SailingRequestUserComment>()
                .Query()
                .AsNoTracking()
                .AsSplitQuery()
                .Include(x => x.CreatedByUser)
                .Include(x => x.CommentReadByUsers)
                    .ThenInclude(x => x.ReaderUser)
                .Where(x => x.SailingRequestId == sailingRequestId)
                .OrderBy(x => x.CreatedDate)
                .ToListAsync();
            return _mapper.Map<List<SailingRequestUserCommentModel>>(sailingRequestUserComments);
        }

        public async Task<Guid> CreateSailingRequestCommentAsync(SailingRequestUserCommentUpsertModel newSailingRequestComment, ClaimsPrincipal currentUser)
        {
            var currentLoggedInUser = await _userService.GetCurrentUser(currentUser);   
            await _unitOfWork.BeginTransactionAsync();

            var sailingRequestUserComment = await _unitOfWork.Repository<SailingRequestUserComment>()
                .CreateAsync(_mapper.Map<SailingRequestUserComment>(newSailingRequestComment));

            sailingRequestUserComment.CreatedByUserId = currentLoggedInUser.UserId;

            await _unitOfWork.SaveChangesAsync();
            await _unitOfWork.CommitAsync();

            return await _unitOfWork.Repository<SailingRequest>()
                .Query()
                .Where(x => x.SailingRequestId == newSailingRequestComment.SailingRequestId)
                .Select(x => x.SailingRequestId)
                .FirstOrDefaultAsync();
        }

        public async Task<Guid> AddUserToCommentReadByAsync(CommentReadByUserUpsertModel addUserToCommentReadBy, ClaimsPrincipal currentUser)
        {
            await _unitOfWork.BeginTransactionAsync();

            var commentReadByUser = await _unitOfWork.Repository<CommentReadByUser>()
                .CreateAsync(_mapper.Map<CommentReadByUser>(addUserToCommentReadBy));

            commentReadByUser.ReaderUserId = addUserToCommentReadBy.ReaderUserId;

            await _unitOfWork.SaveChangesAsync();
            await _unitOfWork.CommitAsync();
            var currentLoggedInUser = await _userService.GetCurrentUser(currentUser);

            return currentLoggedInUser.LocationId.Value;
        }

        public async Task<SailingRequestModel> GetByIdAsync(Guid id) {
            var sailingRequest = await _unitOfWork.Repository<SailingRequest>()
               .Query()
               .AsNoTracking()
               .AsSplitQuery()
               .Include(x => x.Client)
               .Include(x => x.Vessel)
               .Include(x => x.SailingRequestActivities)
                   .ThenInclude(x => x.ActivityCategoryType)
               .Include(x => x.SailingRequestAssets)
                   .ThenInclude(x => x.Asset)
                .Include(x => x.Cluster)
                .Include(x => x.TransportRequests)
               .Where(x => x.SailingRequestId == id)
               .SingleOrDefaultAsync();
            var sailingRequestModel = _mapper.Map<SailingRequestModel>(sailingRequest);
            return sailingRequestModel;
        }

        public async Task<SailingRequestModel> CreateAsync(SailingRequestUpsertModel model) {
            await _unitOfWork.BeginTransactionAsync();

            var currentUser = await _userService.GetCurrentUser();

            await UpdateVoyagePlanningDetails(model, currentUser);
            await ValidateRequest(model, currentUser);

            var sailingRequest = _mapper.Map<SailingRequest>(model);

            sailingRequest.LocationId = currentUser.LocationId;
            sailingRequest.CreatedById = currentUser.UserId;

            sailingRequest = await _unitOfWork.Repository<SailingRequest>().CreateAsync(sailingRequest);

            await CreateTransportRequests(model, sailingRequest, currentUser);

            if (model.RepeatEveryNumberOfWeeks.HasValue && !sailingRequest.InboundVoyageId.HasValue && !sailingRequest.OutboundVoyageId.HasValue)
                await CreateSeries(sailingRequest, model, currentUser);

            await _unitOfWork.SaveChangesAsync();

            if (model.SailingRequestActivities?.Any() ?? false) {
                await CreateNewSailingRequestActivities(model.SailingRequestActivities, sailingRequest, currentUser);
            }

            if (model.SailingRequestAssets?.Any() ?? false)
            {
                var sailingRequestLocations = model.SailingRequestAssets?.Select(x => new SailingRequestAsset()
                {
                    AssetId = x.AssetId,
                    SailingRequestId = sailingRequest.SailingRequestId,
                    CreatedById = currentUser.UserId,
                    UpdatedById = currentUser.UserId,
                }).ToList() ?? new();
                await _unitOfWork.Repository<SailingRequestAsset>().BulkCreateAsync(sailingRequestLocations);
            }

            await _unitOfWork.SaveChangesAsync();
            await _unitOfWork.CommitAsync();

            return await GetByIdAsync(sailingRequest.SailingRequestId);
        }

        public async Task CancellAllTransportRequestLineItemsAsync(Guid sailingRequestId, bool isInboundCancellable, bool isOutBoundCancellable)
        {
            //The Material Details Will also be automatically deleted as the cargoes and bulks are getting deleted
            await _transportRequestCargoService.CancelAllTransportCargoesBySailingRequestId(sailingRequestId);
            await _transportRequestBulkCargoService.CancelAllTransportBulkCargoesBySailingRequestId(sailingRequestId);

            var transportRequestsBySailingRequestId = await _transportRequestService.GetBySailingRequestIdAsync(sailingRequestId);

            foreach (var transportRequest in transportRequestsBySailingRequestId)
            {
                var shouldUpdateSnapshot =
                       transportRequest.TransportRequestMaterialDetails.Any()
                    || transportRequest.TransportRequestCargos.Any()
                    || transportRequest.TransportRequestBulkCargos.Any();
                
                if (shouldUpdateSnapshot)
                {
                    await UpdateSnapshotsAndCreateEditPage(transportRequest, isInboundCancellable, isOutBoundCancellable);
                }
            }
        }

        private async Task UpdateSnapshotsAndCreateEditPage(TransportRequestModel transportRequest, bool isInboundCancellable, bool isOutBoundCancellable)
        {
            if (
                (transportRequest.VoyageDirection == VoyageDirection.Inbound && isInboundCancellable) ||
                (transportRequest.VoyageDirection == VoyageDirection.Outbound && isOutBoundCancellable)
                )
            {
                var currentUser = await _userService.GetCurrentUser();

                var trItems = await _requestToFlowDataSyncExtension.GetTrItemsByTransportRequestIdAsync(transportRequest.TransportRequestId);

                await _requestToFlowDataSyncExtension.DataSyncBetweenRequestAndFlow(transportRequest.VoyageId, DateTime.UtcNow, trItems, currentUser, false);
            }

        }


        public async Task<(bool isInboundCancellable, bool isOutBoundCancellable)> CheckToDeleteOrDeclineTransportRequestBasedOnVoyageCargoStatus(Guid sailingRequestId)
        {
            return await _voyageService.GetVoyagesBySailingRequestId(sailingRequestId);
        }

        public async Task<SailingRequestModel> UpdateAsync(Guid id, SailingRequestUpsertModel model, bool isInboundCancellable, bool isOutBoundCancellable) {
            
            await _unitOfWork.BeginTransactionAsync();

            var currentUser = await _userService.GetCurrentUser();

            var sailingRequest = await _unitOfWork.Repository<SailingRequest>()
               .Query()
               .AsSplitQuery()
               .Where(x => x.SailingRequestId == id)
               .Include(x => x.Parent)
                   .ThenInclude(x => x.Children)
               .Include(x=>x.Children)
               .Include(x => x.SailingRequestActivities)
                   .ThenInclude(x => x.ActivityCategoryType)
               .Include(x => x.SailingRequestAssets)
                   .ThenInclude(x => x.Asset)
                .Include(x => x.TransportRequests)
               .SingleOrDefaultAsync();

            await UpdateVoyagePlanningDetails(model, currentUser);

            if (model.InboundVoyageId != sailingRequest.InboundVoyageId && !isInboundCancellable)
            {
                var inboundTransportRequest = sailingRequest.TransportRequests.FirstOrDefault(x => x.VoyageDirection == VoyageDirection.Inbound);
                
                if (inboundTransportRequest != null && model.InboundVoyageId.HasValue && !inboundTransportRequest.IsComplete)
                {
                    await _transportRequestService.UpdateTransportRequestVoyageId(inboundTransportRequest.TransportRequestId, model.InboundVoyageId.Value);
                }
                else
                {
                    if(inboundTransportRequest != null && !inboundTransportRequest.IsComplete) {
                        await _transportRequestService.UpdateTransportRequestVoyageId(inboundTransportRequest.TransportRequestId, null);
                    }
                }
            }
            
            if (model.OutboundVoyageId != sailingRequest.OutboundVoyageId && !isOutBoundCancellable)
            {
                var outboundTransportRequest = sailingRequest.TransportRequests.FirstOrDefault(x => x.VoyageDirection == VoyageDirection.Outbound);
                var interfieldTransportRequest = sailingRequest.TransportRequests.FirstOrDefault(x => x.VoyageDirection == VoyageDirection.Interfield);

                if (model.OutboundVoyageId.HasValue)
                {
                    if (outboundTransportRequest != null && interfieldTransportRequest != null && !outboundTransportRequest.IsComplete)
                    {
                        await _transportRequestService.UpdateOutBoundInterfieldTransportRequestVoyageId(outboundTransportRequest.TransportRequestId, interfieldTransportRequest.TransportRequestId, model.OutboundVoyageId.Value);
                    }
                    else if (outboundTransportRequest != null && interfieldTransportRequest == null && !outboundTransportRequest.IsComplete)
                    {

                        await _transportRequestService.UpdateTransportRequestVoyageId(outboundTransportRequest.TransportRequestId, model.OutboundVoyageId.Value);
                    }
                } else
                {
                    if (outboundTransportRequest != null && interfieldTransportRequest != null && !outboundTransportRequest.IsComplete)
                    {
                        await _transportRequestService.UpdateOutBoundInterfieldTransportRequestVoyageId(outboundTransportRequest.TransportRequestId, interfieldTransportRequest.TransportRequestId, null);
                    }
                    else if (outboundTransportRequest != null && interfieldTransportRequest == null)
                    {

                        await _transportRequestService.UpdateTransportRequestVoyageId(outboundTransportRequest.TransportRequestId, null);
                    }
                } 
            }

            ValidateUpdateRequest(sailingRequest, model, currentUser);

            if (model.IsInbound != sailingRequest.IsInbound 
                || model.IsOutbound != sailingRequest.IsOutbound 
                || model.IsInterfield != sailingRequest.IsInterfield
                ) {
                await CreateOrRemoveTransportRequests(model, sailingRequest, currentUser, isInboundCancellable, isOutBoundCancellable);
            }

            var currentWeeklyPattern = sailingRequest.Parent != null ? sailingRequest.Parent.WeeklyPattern : sailingRequest.WeeklyPattern;
            sailingRequest = _mapper.Map(model, sailingRequest);

            sailingRequest.UpdatedById = currentUser.UserId;
            sailingRequest.UpdatedDate = DateTime.UtcNow;

            if(model.RepeatEveryNumberOfWeeks.HasValue && !sailingRequest.InboundVoyageId.HasValue && !sailingRequest.OutboundVoyageId.HasValue)
                await CreateSeries(sailingRequest, model, currentUser, currentWeeklyPattern);
            
            await UpdateSailingRequestActivities(model, sailingRequest, currentUser);

            var sailingLocationsToRemove = sailingRequest.SailingRequestAssets?.Where(x => x.SailingRequestId == id).ToList() ?? new();
            if (sailingLocationsToRemove.Any()) {
                _unitOfWork.Repository<SailingRequestAsset>().RemoveRange(sailingLocationsToRemove);
            }

            if (model.SailingRequestAssets?.Any() ?? false)
            {
                var sailingRequestLocation = model.SailingRequestAssets?.Select(x => new SailingRequestAsset()
                {
                    AssetId = x.AssetId,
                    SailingRequestId = sailingRequest.SailingRequestId,
                    CreatedById = currentUser.UserId,
                    UpdatedById = currentUser.UserId,
                }).ToList() ?? new();
                await _unitOfWork.Repository<SailingRequestAsset>().BulkCreateAsync(sailingRequestLocation);
            }

            _unitOfWork.Repository<SailingRequest>().Update(sailingRequest);

            await _unitOfWork.SaveChangesAsync();
            await _unitOfWork.CommitAsync();

            return await GetByIdAsync(sailingRequest.SailingRequestId);
        }

        private Dictionary<int, string> UnboxWeeklyPattern(string weeklyPattern)
        {
            var dayNames = weeklyPattern.Split(',');

            if(!dayNames.Any())
                throw new Exception("Weekly pattern is not correct. it should be like following: Saturday,Sunday,Friday and it cannot be empty in series creation");

            var result = new Dictionary<int, string>();
            foreach (var dayName in dayNames)
            {
                if (!TimeConstants.WeekDays.TryGetValue(dayName, out int weekDayNumber))
                    throw new Exception("Weekly pattern is not correct. it should be like following: Saturday,Sunday,Friday and it cannot be empty in series creation");
                if (!result.TryAdd(weekDayNumber, dayName))
                    throw new Exception("Duplicate days in weekly pattern");
            }

            return result;
        }
        
        private void ValidateUpdateRequest(SailingRequest sailingRequest, SailingRequestUpsertModel upsertModel, UserModel currentUser)
        {

            if ((sailingRequest.SeriesStartTime != upsertModel.SeriesStartTime || sailingRequest.SeriesEndTime != upsertModel.SeriesEndTime) && 
                (upsertModel.InboundVoyageId.HasValue || upsertModel.OutboundVoyageId.HasValue)) {
                throw new Exception("Cannot create or update series when voyage is assigned");
            }

            if ((sailingRequest.StartTime.HasValue && upsertModel.StartTime.HasValue && sailingRequest.StartTime != upsertModel.StartTime) ||
                (sailingRequest.EndTime.HasValue && upsertModel.EndTime.HasValue && sailingRequest.EndTime != upsertModel.EndTime)) {
                if (sailingRequest.SailingRequestActivities != null && sailingRequest.SailingRequestActivities.Any(x => x.Status != SailingRequestActivityStatus.Unplanned)) {
                    throw new Exception("Cannot update scheduled sailing requests with planned activities, please remove from planning board before adjusting date times");
                }
            }

            var planRoles = currentUser.Roles.Where(x => x.Application == "plan").ToList();

            var isCliRoleOnly = planRoles.Count == 1 && planRoles.All(x => x.Roles.Contains("CLI"));

            if(isCliRoleOnly && upsertModel.Type == SailingRequestType.Scheduled) {
                throw new Exception("Client role cannot update scheduled sailing requests");
            }

            if(isCliRoleOnly && currentUser.ClientId != sailingRequest.ClientId) {
                throw new Exception("Client role cannot update other client's sailing requests");
            }

            if (isCliRoleOnly && sailingRequest.Type == SailingRequestType.Requested && sailingRequest.VesselId.HasValue && 
                (sailingRequest.InboundVoyageId.HasValue || sailingRequest.OutboundVoyageId.HasValue)) {
                throw new Exception("Client role cannot update scheduled sailing requests");
            }

            var today = DateOnly.FromDateTime(DateTime.UtcNow.Date);

            if (upsertModel.StartTime.HasValue && upsertModel.StartTime < today)
            {
                throw new Exception("Cannot update Sailing Request in the past");
            }
            if(sailingRequest.SeriesStartTime != upsertModel.SeriesStartTime || sailingRequest.SeriesEndTime != upsertModel.SeriesEndTime) {
                if (upsertModel.SeriesStartTime.HasValue && upsertModel.SeriesStartTime < today)
                {
                    throw new Exception("Cannot update Sailing Request series in the past");
                }
            }
            if (upsertModel.RepeatEveryNumberOfWeeks < 1 || upsertModel.RepeatEveryNumberOfWeeks > 54)
            {
                throw new Exception("Repeat cannot be less than 1 or more than 54");
            }
            if (upsertModel.InboundVoyageId.HasValue && upsertModel.ETA == null && upsertModel.RepeatEveryNumberOfWeeks.HasValue)
            {
                throw new Exception("ETA is required for Inbound Voyage");
            }
            if (upsertModel.OutboundVoyageId.HasValue && upsertModel.ETD == null)
            {
                throw new Exception("ETD is required for Outbound Voyage");
            }
            if(upsertModel.RepeatEveryNumberOfWeeks.HasValue && string.IsNullOrEmpty(upsertModel.WeeklyPattern))
            {
                throw new Exception("Cannot create a series without a weekly pattern");
            }
            if(upsertModel.Type != sailingRequest.Type)
            {
                throw new Exception("Cannot change request's type");
            }
            if (upsertModel.RepeatEveryNumberOfWeeks.HasValue && ((upsertModel.SailingRequestAssets != null && upsertModel.SailingRequestAssets.Any())))
            {
                throw new Exception("Cannot create series when there are assets associated to the sailing request");
            }
        }

        private async Task UpdateVoyagePlanningDetails(SailingRequestUpsertModel model, UserModel currentUser)
        {
            var inboundPlanningDetails = await _unitOfWork.Repository<VoyagePlanningDetail>()
                .Query(x => x.VoyageId == model.InboundVoyageId)
                .OrderByDescending(x => x.CreatedDate)
                .FirstOrDefaultAsync();
        
            var outboundPlanningDetails = await _unitOfWork.Repository<VoyagePlanningDetail>()
                .Query(x => x.VoyageId == model.OutboundVoyageId)
                .OrderByDescending(x => x.CreatedDate)
                .FirstOrDefaultAsync();


            var eta = model.ETA.HasValue && model.StartTime.HasValue
                ? await _timeZoneConversionService.ConvertCurrentUserTimeToUtc(model.StartTime.Value.ToDateTime(model.ETA.Value))
                : (DateTime?)null;
            var etd = model.ETD.HasValue && model.EndTime.HasValue ? await _timeZoneConversionService.ConvertCurrentUserTimeToUtc(model.EndTime.Value.ToDateTime(model.ETD.Value)) : (DateTime?)null;
            var etaHasChanged = inboundPlanningDetails != null && inboundPlanningDetails.ETA != eta;
            var etdHasChanged = outboundPlanningDetails != null && outboundPlanningDetails.ETD != etd;
            if (model.InboundVoyageId.HasValue && etaHasChanged)
            {
                var newInboundDetail = new VoyagePlanningDetail
                {
                    VoyageId = model.InboundVoyageId.Value,
                    ETA = eta,
                    ETD = etd,
                    CreatedById = currentUser.UserId,
                    CreatedDate = DateTime.UtcNow,
                    // Copy other relevant fields from the latest planning detail if it exists
                    Comment = inboundPlanningDetails?.Comment,
                    TuNumber = inboundPlanningDetails?.TuNumber,
                    TuType = inboundPlanningDetails?.TuType,
                    AreaId = inboundPlanningDetails?.AreaId,
                    IsScheduled = inboundPlanningDetails?.IsScheduled ?? false,
                    Time = inboundPlanningDetails?.Time
                };
                await _unitOfWork.Repository<VoyagePlanningDetail>().CreateAsync(newInboundDetail);
            }
            
            if (model.OutboundVoyageId.HasValue && etdHasChanged)
            {
                var newOutboundDetail = new VoyagePlanningDetail
                {
                    VoyageId = model.OutboundVoyageId.Value,
                    ETA = eta,
                    ETD = etd,
                    CreatedById = currentUser.UserId,
                    CreatedDate = DateTime.UtcNow,
                    // Copy other relevant fields from the latest planning detail if it exists
                    Comment = outboundPlanningDetails?.Comment,
                    TuNumber = outboundPlanningDetails?.TuNumber,
                    TuType = outboundPlanningDetails?.TuType,
                    AreaId = outboundPlanningDetails?.AreaId,
                    IsScheduled = outboundPlanningDetails?.IsScheduled ?? false,
                    Time = outboundPlanningDetails?.Time
                };
                await _unitOfWork.Repository<VoyagePlanningDetail>().CreateAsync(newOutboundDetail);
            }
        }

        public async Task CreateSeries(SailingRequest currentSailingRequest, SailingRequestUpsertModel upsertModel, UserModel currentUser, string currentWeeklyPattern = null) {
            if (!_unitOfWork.HasTransaction())
                throw new Exception("CreateSeries must be called within a transaction");

            if(!currentSailingRequest.LocationId.HasValue || currentSailingRequest.LocationId == Guid.Empty)
                throw new Exception("Location cannot be empty");

            var currentLocation = await _unitOfWork.Repository<Location>().GetAsync(currentSailingRequest.LocationId.Value);

            currentSailingRequest.Children ??= new List<SailingRequest>();

            var parent = currentSailingRequest.Parent ?? currentSailingRequest;

            var newWeeklyPattern = UnboxWeeklyPattern(upsertModel.WeeklyPattern);

            //if we need to recreate the children even in the mutual time range as the new series
            bool childrenShouldBeRecreated = false;

            //identify and remove sailing requests of previous series
            if (parent.Children != null && parent.Children.Any())
            {
                if (string.IsNullOrEmpty(parent.WeeklyPattern)) //parent can be a regular sailing request with empty weekly pattern
                    childrenShouldBeRecreated = true;
                else
                {
                    var parentWeeklyPattern = UnboxWeeklyPattern(currentWeeklyPattern);

                    //if count of days are not matching they are different
                    if (parentWeeklyPattern.Count != newWeeklyPattern.Count)
                        childrenShouldBeRecreated = true;
                    //since it can be the same with different orders, we check them unboxed
                    else
                        foreach (var weekDay in parentWeeklyPattern)
                        {
                            if (!newWeeklyPattern.ContainsKey(weekDay.Key))
                            {
                                childrenShouldBeRecreated = true;
                                break;
                            }
                        }
                }

                var sailingRequestsToRemove = new List<SailingRequest>();
                if (childrenShouldBeRecreated)
                    sailingRequestsToRemove.AddRange(parent.Children);
                else
                {
                    var previousSailingRequests = parent.Children.Where(x => x.StartTime < upsertModel.SeriesStartTime).ToList();
                    var nextSailingRequests = parent.Children.Where(x => x.EndTime > upsertModel.SeriesEndTime).ToList();
                    sailingRequestsToRemove.AddRange(previousSailingRequests);
                    sailingRequestsToRemove.AddRange(nextSailingRequests);
                }
                
                sailingRequestsToRemove.Remove(currentSailingRequest);

                foreach (var sailingRequest in sailingRequestsToRemove)
                {
                    parent.Children.Remove(sailingRequest);
                    sailingRequest.Deleted = true;
                }

                await _unitOfWork.SaveChangesAsync();

                foreach (var sailingRequest in parent.Children)
                {
                    sailingRequest.Parent = currentSailingRequest;
                    sailingRequest.ParentId = currentSailingRequest.SailingRequestId;
                    currentSailingRequest.Children.Add(sailingRequest);
                }

                if (parent != currentSailingRequest)
                    parent.Deleted = true;
                
                await _unitOfWork.SaveChangesAsync();
            }
            else
            {
                childrenShouldBeRecreated = true; //if there are no children to current parent we have to try to create anyway
            }

            var childrenDates = new List<DateOnly>();
            var startDateDayOfWeek = upsertModel.SeriesStartTime.Value.DayOfWeek;
            var startDateWeekStartDate = upsertModel.SeriesStartTime.Value.AddDays(-(int)startDateDayOfWeek);


            for(DateOnly weekStart = startDateWeekStartDate; weekStart < upsertModel.SeriesEndTime.Value; weekStart = weekStart.AddDays(7 * upsertModel.RepeatEveryNumberOfWeeks.Value))
            {
                foreach(var patternDay in newWeeklyPattern.Keys.OrderBy(x=>x))
                {
                    var childStartDate = weekStart.AddDays(patternDay);
                    //we still need to check since we are moving from week start before start date to the week after end date
                    if(childStartDate >= upsertModel.SeriesStartTime.Value && childStartDate <= upsertModel.SeriesEndTime.Value)
                        childrenDates.Add(childStartDate);
                }

            }

            if (childrenDates.Count <= 1)
                throw new Exception("A series must at least contain 2 requests");

            childrenDates.Sort();

            var firstDateTime = childrenDates.First();

            childrenDates.RemoveAt(0);

            var childWithSameDateAsParent = currentSailingRequest.Children
                .FirstOrDefault(x => x.StartTime == firstDateTime &&
                                     x.SailingRequestId != currentSailingRequest.SailingRequestId);
            if (childWithSameDateAsParent != null)
            {
                childWithSameDateAsParent.Deleted = true;
                currentSailingRequest.Children.Remove(childWithSameDateAsParent);
            }

            currentSailingRequest.StartTime = firstDateTime;
            currentSailingRequest.EndTime = firstDateTime;
            currentSailingRequest.Status = SailingRequestStatus.Pending;
            currentSailingRequest.ParentId = null;
            currentSailingRequest.Parent = null;

            await _unitOfWork.SaveChangesAsync();

            var sailingRequestsToCreate = new List<SailingRequest>();

            foreach (DateOnly childDate in childrenDates)
            {
                if (!currentSailingRequest.Children.Any(x => x.StartTime.Value == childDate))
                {
                    var newSailingRequest = new SailingRequest()
                    {
                        ParentId = currentSailingRequest.SailingRequestId,
                        StartTime = childDate,
                        EndTime = childDate,
                        Status = SailingRequestStatus.Pending,
                        Type = currentSailingRequest.Type,
                        SeriesEndTime = upsertModel.SeriesEndTime,
                        SeriesStartTime = upsertModel.SeriesStartTime,
                        VesselId = currentSailingRequest.VesselId,
                        ClientId = currentSailingRequest.ClientId,
                        LocationId = currentSailingRequest.LocationId,
                        ClusterID = currentSailingRequest.ClusterID,
                        IsOutbound = currentSailingRequest.IsOutbound,
                        IsInbound = currentSailingRequest.IsInbound,
                        IsInterfield = currentSailingRequest.IsInterfield,
                        IsFlexableTiming = currentSailingRequest.IsFlexableTiming,
                        ArrivalTime = currentSailingRequest.ArrivalTime,
                        FirstInstallationTime = currentSailingRequest.FirstInstallationTime,
                        ClusterTime = currentSailingRequest.ClusterTime,
                        TimeUnit = currentSailingRequest.TimeUnit,
                        isMailbag = currentSailingRequest.isMailbag,
                        isBulkReq = currentSailingRequest.isBulkReq,
                        ClientReference = currentSailingRequest.ClientReference,
                        Remarks = currentSailingRequest.Remarks,
                        Comment = currentSailingRequest.Comment,
                        CreatedDate = DateTime.UtcNow,
                        CreatedById = currentUser.UserId,
                    };
                    sailingRequestsToCreate.Add(newSailingRequest);
                }
            }

            await _unitOfWork.Repository<SailingRequest>().BulkCreateAsync(sailingRequestsToCreate);

            await _unitOfWork.SaveChangesAsync();
        }

        public async Task<SailingRequestModel> DeleteAsync(Guid id)
        {
            await _unitOfWork.BeginTransactionAsync();
            var sailingRequest = await _unitOfWork.Repository<SailingRequest>()
                .Query()
                .Include(x => x.Children)
                .Include(x => x.SailingRequestActivities)
                .Include(x => x.SailingRequestAssets)
                .Include(x => x.SailingRequestUserComments)
                    .ThenInclude(x => x.CommentReadByUsers)
                .Where(x => x.SailingRequestId == id)
                .FirstOrDefaultAsync();
            if (sailingRequest != null) {
                if(sailingRequest.Children.Count > 0) {
                    var firstChild = sailingRequest.Children.OrderBy(x => x.StartTime).FirstOrDefault();
                    firstChild.ParentId = null;
                    firstChild.WeeklyPattern = sailingRequest.WeeklyPattern;
                    _unitOfWork.Repository<SailingRequest>().Update(firstChild);
                    foreach (var child in sailingRequest.Children) {
                        child.ParentId = firstChild.SailingRequestId;
                        _unitOfWork.Repository<SailingRequest>().Update(child);
                    }
                }
                if (sailingRequest.SailingRequestActivities.Count > 0) {
                    foreach (var sailingRequestActivity in sailingRequest.SailingRequestActivities) {
                        sailingRequestActivity.Deleted = true;
                        _unitOfWork.Repository<SailingRequestActivity>().Update(sailingRequestActivity);
                    }
                }
                if (sailingRequest.SailingRequestAssets.Count > 0) {
                    foreach (var sailingRequestAsset in sailingRequest.SailingRequestAssets) {
                        sailingRequestAsset.Deleted = true;
                        _unitOfWork.Repository<SailingRequestAsset>().Update(sailingRequestAsset);
                    }
                }
                if (sailingRequest.SailingRequestUserComments.Count > 0) {
                    foreach (var sailingRequestUserComment in sailingRequest.SailingRequestUserComments) {
                        sailingRequestUserComment.Deleted = true;
                        _unitOfWork.Repository<SailingRequestUserComment>().Update(sailingRequestUserComment);
                        foreach (var commentReadByUser in sailingRequestUserComment.CommentReadByUsers) {
                            commentReadByUser.Deleted = true;
                            _unitOfWork.Repository<CommentReadByUser>().Update(commentReadByUser);
                        }
                    }
                }
            }
            sailingRequest.Deleted = true;
            _unitOfWork.Repository<SailingRequest>().Update(sailingRequest);

            var deletedSailingRequest = _mapper.Map<SailingRequestModel>(sailingRequest);   

            await _unitOfWork.SaveChangesAsync();
            await _unitOfWork.CommitAsync();

            return deletedSailingRequest;
        }

        public async Task ValidateRequest(SailingRequestUpsertModel model, UserModel currentUser) 
        {
            var planRoles = currentUser.Roles.Where(x => x.Application == "plan").ToList();

            var isCliRoleOnly = planRoles.Count == 1 && planRoles.All(x => x.Roles.Contains("CLI"));

            if(isCliRoleOnly && model.Type == SailingRequestType.Scheduled) {
                throw new Exception("Client role cannot create scheduled sailing requests");
            }

            if (model.StartTime.HasValue && model.EndTime.HasValue) {
                var today = DateOnly.FromDateTime(DateTime.UtcNow.Date);
                var startTime = model.StartTime.Value;
                var endTime = model.EndTime.Value;

                if (startTime < today || endTime < today) {
                    throw new Exception("Cannot create Sailing Request in the past");
                }
            }

            if (model.InboundVoyageId.HasValue && model.ETA == null) {
                throw new Exception("ETA is required for Inbound Voyage");
            }

            if (model.OutboundVoyageId.HasValue && model.ETD == null) {
                throw new Exception("ETD is required for Outbound Voyage");
            }

            // need validation to check if sailing request is series you cannot have any voyages or ETA/ETD
        }

        public async Task CreateNewSailingRequestActivities(List<SailingRequestActivityModel> sailingRequestActivities, SailingRequest sailingRequest, UserModel currentUser) {
            var entitiesToCreate = sailingRequestActivities.Select(x => new SailingRequestActivity
            {
                ActivityCategoryTypeId = x.ActivityCategoryTypeId,
                ActivityCategoryType = null,
                Hours = x.Hours,
                Notes = x.Notes,
                Quantity = x.Quantity,
                UnitName = x.UnitName,
                Status = x.Status,
                SailingRequestId = sailingRequest.SailingRequestId,
                IsInPort = !x.ActivityCategoryType?.CreateOutOfPortActivity ?? true,
                AreaId = x.AreaId,
                AssetId = x.AssetId,
                StartTime = x.StartTime,
                EndTime = x.EndTime,
                CreatedById = currentUser.UserId,
                UpdatedById = currentUser.UserId,
                isDisabled = x.isDisabled
            }).ToList();

            await _unitOfWork.Repository<SailingRequestActivity>().BulkCreateAsync(entitiesToCreate);
            await _unitOfWork.SaveChangesAsync();
        }

        private async Task<List<SailingRequestActivity>> GetCurrentSailingRequestActivitiesBySailingRequestId(Guid sailingRequestId)
        {
            return await _unitOfWork.Repository<SailingRequestActivity>().Query().Where(x => x.SailingRequestId == sailingRequestId).ToListAsync();
        }

        private async Task<List<SailingRequestActivity>> DeleteUnassignedSailingRequestActivities(List<SailingRequestActivityModel> sailingRequestActivities, Guid sailingRequestId)
        {
            var existingSailingRequestActivities = await GetCurrentSailingRequestActivitiesBySailingRequestId(sailingRequestId);

            var sailingRequestActivitiesToRemove = existingSailingRequestActivities
                .Where(x => !sailingRequestActivities.Any(y => y.SailingRequestActivityId == x.SailingRequestActivityId))
                .ToList();

            if (sailingRequestActivitiesToRemove.Any())
            {
                foreach (var sailingRequestActivity in sailingRequestActivitiesToRemove)
                {
                    sailingRequestActivity.Deleted = true;
                    sailingRequestActivity.UpdatedDate = DateTime.UtcNow;
                    sailingRequestActivity.UpdatedById = sailingRequestActivity.CreatedById;
                }
            }
            await _unitOfWork.SaveChangesAsync();
            return existingSailingRequestActivities;
        }

        public async Task UpdateSailingRequestActivities(SailingRequestUpsertModel model, SailingRequest sailingRequest, UserModel currentUser) {

            var existingSailingRequestActivities = await DeleteUnassignedSailingRequestActivities(
                model.SailingRequestActivities,
                sailingRequest.SailingRequestId
            );

            model.SailingRequestActivities = model.SailingRequestActivities
                .Where(x => !existingSailingRequestActivities.Any(y => y.SailingRequestActivityId == x.SailingRequestActivityId))
                .ToList();

            await CreateNewSailingRequestActivities(model.SailingRequestActivities, sailingRequest, currentUser);
        }

        public async Task CreateTransportRequests(SailingRequestUpsertModel model, SailingRequest sailingRequest, UserModel currentUser) {
            var voyageDirections = new List<(bool IsSet, VoyageDirection Direction, Guid? voyageId)>
            {
                (model.IsOutbound, VoyageDirection.Outbound, model.OutboundVoyageId),
                (model.IsInbound, VoyageDirection.Inbound, model.InboundVoyageId),
                (model.IsInterfield, VoyageDirection.Interfield, model.OutboundVoyageId)
            };

            if (voyageDirections.Any(x => x.IsSet)) {
                foreach (var (isSet, direction, voyageId) in voyageDirections) {
                    if (isSet) {
                        await _transportRequestService.CreateAsync(sailingRequest.SailingRequestId, voyageId, (int)direction, currentUser);
                    }
                }
            }
        }

        public async Task CreateOrRemoveTransportRequests(
            SailingRequestUpsertModel model,
            SailingRequest sailingRequest,
            UserModel currentUser,
            bool isInboundCancellable, 
            bool isOutBoundCancellable
            ){
            var voyageDirections = new List<(bool IsSet, bool WasSet, VoyageDirection Direction, Guid? voyageId)>
            {
                (model.IsOutbound, sailingRequest.IsOutbound, VoyageDirection.Outbound, model.OutboundVoyageId),
                (model.IsInbound, sailingRequest.IsInbound, VoyageDirection.Inbound, model.InboundVoyageId),
                (model.IsInterfield, sailingRequest.IsInterfield, VoyageDirection.Interfield, model.OutboundVoyageId)
            };

            foreach (var (isSet, wasSet, direction, voyageId) in voyageDirections) {
                if (isSet != wasSet && !isSet) {
                    var transportRequestId = sailingRequest.TransportRequests
                        .Where(x => x.VoyageDirection == direction)
                        .Select(x => x.TransportRequestId)
                        .FirstOrDefault();

                    if (
                        (isInboundCancellable && direction == VoyageDirection.Inbound) ||
                        (isOutBoundCancellable && direction == VoyageDirection.Outbound)
                        )
                    {
                        await _transportRequestService.DeleteAsync(transportRequestId);
                    } 
                } else if(isSet && !wasSet) {
                    await _transportRequestService.CreateAsync(sailingRequest.SailingRequestId, voyageId, (int)direction, currentUser);
                }
            }
        }
    }
}
