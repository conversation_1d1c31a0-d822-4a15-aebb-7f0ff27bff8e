namespace Lighthouse.Service.Data.MasterData {
    public class VoyageCargoService : IVoyageCargoService {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IMapper _mapper;
        private readonly IUserService _userService;
        private readonly IVoyageCargoInspectionService _voyageCargoInspectionService;
        private readonly IVoyageInspectionService _voyageInspectionService;
        private readonly IVendorService _vendorService;
        private readonly IExportService _exportService;
        private readonly IServiceProvider _serviceProvider;
        private readonly IAzureBlobStorageService _storageService;
        private readonly IVoyageCargoWeightUtility _voyageCargoWeightUtility;
        private readonly ILocationService _locationService;
        private readonly IDateTimeService _dateTimeService;
        private readonly IVoyageCargoDangerousGoodService _voyageCargoDangerousGoodService;
        private readonly ITimeZoneConversionService _timeZoneConversionService;
        private readonly string cargoDescriptionAdHoc = "Ad-hoc";
        private readonly IVoyageAttachmentService _voyageAttachmentService;

        public VoyageCargoService(
            IUnitOfWork unitOfWork,
            IMapper mapper,
            IUserService userService,
            IVoyageCargoInspectionService voyageCargoInspectionService,
            IVoyageInspectionService voyageInspectionService,
            IVendorService vendorService,
            IServiceProvider serviceProvider,
            IExportService exportService,
            IAzureBlobStorageService storageService,
            IVoyageCargoWeightUtility voyageCargoWeightUtility,
            ILocationService locationService,
            IDateTimeService dateTimeService,
            IVoyageCargoDangerousGoodService voyageCargoDangerousGoodService,
            ITimeZoneConversionService timeZoneConversionService,
            IVoyageAttachmentService voyageAttachmentService)
        {
            _unitOfWork = unitOfWork;
            _mapper = mapper;
            _userService = userService;
            _voyageCargoInspectionService = voyageCargoInspectionService;
            _voyageInspectionService = voyageInspectionService;
            _vendorService = vendorService;
            _exportService = exportService;
            _serviceProvider = serviceProvider; //we need this for preventing cases with circular dependencies
            _storageService = storageService;
            _voyageCargoWeightUtility = voyageCargoWeightUtility;
            _locationService = locationService;
            _dateTimeService = dateTimeService;
            _voyageCargoDangerousGoodService = voyageCargoDangerousGoodService;
            _timeZoneConversionService = timeZoneConversionService;
            _voyageAttachmentService = voyageAttachmentService;
        }

        private IVoyageOffshoreLocationService _voyageOffshoreLocationService => _serviceProvider.GetRequiredService<IVoyageOffshoreLocationService>();

        public async Task<List<VoyageCargoModel>> GetAllByVoyageIdAsync(Guid voyageId) {
            var voyageCargoes = await _unitOfWork.Repository<VoyageCargo>()
                .Query(x => x.VoyageId == voyageId && x.VoyageCargoParentId == null)
                .AsSplitQuery()
                .Include(x => x.TransportRequestCargo)
                .Include(x => x.VoyageCargoDangerousGoods)
                    .ThenInclude(x => x.DangerousGood)
                .Include(x => x.LiftedAtArea)
                .Include(x => x.VoyageCargoLifts.Where(x => !x.Inactive))
                .Include(x => x.VoyageCargoInspection)
                .Include(x => x.WeightCategory)
                .Include(x => x.Cargo)
                .OrderBy(x => x.RowNumber)
                .AsNoTracking()
                .ToListAsync();

            var result = _mapper.Map<List<VoyageCargoModel>>(voyageCargoes);

            var measurementUnit = await _voyageCargoWeightUtility.GetMeasurementUnitAsync(voyageId);

            var flowVoyageService = _serviceProvider.GetRequiredService<IFlowVoyageService>();

            foreach (var voyageCargo in voyageCargoes) {
                bool hasLifts = voyageCargo.VoyageCargoLifts != null && voyageCargo.VoyageCargoLifts.Count > 0;
                var mappedVoyageCargo = result.First(x => x.VoyageCargoId == voyageCargo.VoyageCargoId);
                mappedVoyageCargo.ActualWeight = !hasLifts ? null : voyageCargo.ActualWeight;
                mappedVoyageCargo.HasLifts = hasLifts;

                mappedVoyageCargo.CargoWeight = _voyageCargoWeightUtility.ConvertWeight(voyageCargo.CargoWeightKg, measurementUnit, true);
                mappedVoyageCargo.MeasurementUnit = measurementUnit.ToString();
                bool isVoyageOwnerCustomsCompliant = await flowVoyageService.IsOwnerCustomsCompliant(voyageId);
                mappedVoyageCargo.CustomsCleared = CalculateCustomsCleared(
                    mappedVoyageCargo.CustomsEntryType,
                    (mappedVoyageCargo.Cargo?.IsApproved) ?? false,
                    mappedVoyageCargo.CustomsApprovedToLoad,
                    isVoyageOwnerCustomsCompliant
                );
            }

            return result;
        }

        public async Task<List<VoyageCargoModel>> GetLatestSubmittedVoyageCargosAsync(Guid voyageId) {
            var voyageCargoSnapshot = await _unitOfWork.Repository<VoyageCargoSnapshot>()
                .Query(x => x.VoyageId == voyageId)
                .AsNoTracking()
                .OrderByDescending(x => x.CreatedDate)
                .FirstOrDefaultAsync();

            if (voyageCargoSnapshot == null) {
                return new List<VoyageCargoModel>();
            }

            if (voyageCargoSnapshot.Content is not null) {
                var measurementUnit = await _voyageCargoWeightUtility.GetMeasurementUnitAsync(voyageId);
                var options = new JsonSerializerOptions {
                    PropertyNameCaseInsensitive = true
                };

                var voyageCargoContentModels = JsonSerializer.Deserialize<List<VoyageCargoModel>>(voyageCargoSnapshot.Content, options);

                foreach (var cargo in voyageCargoContentModels) {
                    cargo.MeasurementUnit = measurementUnit.ToString();

                    if (cargo.CargoWeight.HasValue) {
                        cargo.CargoWeight = _voyageCargoWeightUtility.ConvertWeight(cargo.CargoWeight, measurementUnit, true);

                        if (cargo.VoyageCargoLifts != null && cargo.VoyageCargoLifts.Any()) {
                            var cargoLifts = cargo.VoyageCargoLifts.Where(x => !x.Inactive);

                            foreach (var lift in cargoLifts) {
                                lift.CapturedWeightKg = (double)_voyageCargoWeightUtility.ConvertWeight(lift.CapturedWeightKg, measurementUnit, true);
                            }
                        }
                    }

                    if (cargo.ActualWeight.HasValue)
                    {
                        cargo.ActualWeight = _voyageCargoWeightUtility.ConvertWeight(cargo.ActualWeight, measurementUnit, true);
                    }
                }

                var voyageCargoModels = _mapper.Map<List<VoyageCargoModel>>(voyageCargoContentModels);

                var flowVoyageService = _serviceProvider.GetRequiredService<IFlowVoyageService>();
                bool isVoyageOwnerCustomsCompliant = await flowVoyageService.IsOwnerCustomsCompliant(voyageId);
                voyageCargoModels.ForEach(c => {
                    c.CustomsCleared =
                        CalculateCustomsCleared(c.CustomsEntryType, (c.Cargo?.IsApproved) ?? false, c.CustomsApprovedToLoad, isVoyageOwnerCustomsCompliant);
                });

                voyageCargoModels.Sort((c1, c2) => c1.RowNumber.CompareTo(c2.RowNumber));
                return voyageCargoModels;
            } else {
                return new();
            }
        }

        public async Task PerformSnapshotUpdatesAsync(List<VoyageCargo> voyageCargos) {
            if ((voyageCargos?.Count ?? 0) == 0) return;

            // Due to the circular dependency, it cannot be injected into the constructor.
            var snapshotService = _serviceProvider.GetRequiredService<IVoyageCargoSnapshotService>();

            var snapshot = await snapshotService.GetSnapshotByVoyageId(voyageCargos[0].VoyageId.Value);
            if ((snapshot?.Content.Count ?? 0) == 0) return;

            var cargoIds = voyageCargos
                .Where(c => c.CargoId != null).Select(c => c.CargoId)
                .UnionBy(snapshot.Content.Where(c => c.CargoId != null).Select(s => s.CargoId), c => c)
                .ToList();

            var cargoInfos = await
                _unitOfWork.Repository<Cargo>()
                    .Query(c => cargoIds.Contains(c.CargoId))
                    .IgnoreQueryFilters().Where(c => !c.Deleted)    // The line is to remove "IsApproved" global query filter and only keeps the one for "Deleted"
                    .Select(c => new { c.CargoId, c.IsApproved })
                    .ToDictionaryAsync(a => a.CargoId, a => a.IsApproved);

            var flowVoyageService = _serviceProvider.GetRequiredService<IFlowVoyageService>();
            bool isVoyageOwnerCustomsCompliant = await flowVoyageService.IsOwnerCustomsCompliant(voyageCargos[0].VoyageId.Value);

            var snapshotLinesToUpdate =
                voyageCargos
                .Join(snapshot.Content, c => c.VoyageCargoId, s => s.VoyageCargoId, (c, s) => new { VoyageCargo = c, SnapshotLine = s })
                .Where(a =>
                    // CustomsCleared
                    CalculateCustomsCleared(
                        a.VoyageCargo.CustomsEntryType,
                        a.VoyageCargo.CargoId is not null && cargoInfos[a.VoyageCargo.CargoId.Value],
                        a.VoyageCargo.CustomsApprovedToLoad,
                        isVoyageOwnerCustomsCompliant
                    ) !=
                    CalculateCustomsCleared(
                        a.SnapshotLine.CustomsEntryType,
                        a.SnapshotLine.CargoId is not null && cargoInfos[a.SnapshotLine.CargoId.Value],
                        a.SnapshotLine.CustomsApprovedToLoad,
                        isVoyageOwnerCustomsCompliant) ||

                    // AtQuay
                    (a.VoyageCargo.ArrivalTime is null && a.SnapshotLine.ArrivalTime is not null) ||
                    (a.VoyageCargo.ArrivalTime is not null && a.SnapshotLine.ArrivalTime is null) ||

                    // Inspected
                    a.VoyageCargo.PassedInspection != a.SnapshotLine.PassedInspection ||

                    // Dispached
                    a.VoyageCargo.DispatchTime != a.SnapshotLine.DispatchTime
                )
                .Select(x => _mapper.Map<VoyageCargoModel>(x.VoyageCargo));

            await snapshotService.UpdateContentAsync(snapshot.VoyageCargoSnapshotId, snapshotLinesToUpdate);

            await _unitOfWork.SaveChangesAsync();
        }

        public async Task<List<VoyageCargoModel>> GetBumpedVoyageCargoesForVoyageAndSharersAndOwner(Guid voyageId, string search) {
            var voyage = await _unitOfWork.Repository<Voyage>().Query(q => q.VoyageId == voyageId)
                .Include(x => x.VoyageSharers).ThenInclude(x => x.Client).ThenInclude(x => x.ClientAssets)
                .Include(x => x.Client).ThenInclude(x => x.ClientAssets)
                .AsNoTracking()
                .SingleOrDefaultAsync();

            var siteId = voyage.SiteId;

            if (voyage == null) {
                throw new Exception("Voyage not found");
            }

            var sharerIds = new List<Guid>();
            var ownerIds = new List<Guid>();

            if (voyage.VoyageSharers != null && voyage.VoyageSharers.Count > 0) {
                sharerIds.AddRange(
                    voyage.VoyageSharers
                        .Select(x => x.Client).Where(w => w.ClientAssets != null && w.ClientAssets.Count > 0)
                        .SelectMany(s => s.ClientAssets)
                        .Select(s => s.AssetId)
                );
            } else if (voyage.Client != null && voyage.Client.ClientAssets != null && voyage.Client.ClientAssets.Count > 0) {
                var ownerAssetIds = voyage.Client.ClientAssets.Select(x => x.AssetId);
                sharerIds.AddRange(ownerAssetIds);
            }

            if (voyage.ClientId.HasValue)
                ownerIds.Add(voyage.ClientId.Value);
            var voyageCargoes = await _unitOfWork.Repository<VoyageCargo>()
                .Query(x => (x.Voyage.VoyageDirection == VoyageDirection.Inbound ? x.RtRob == RtRob.RoundTrip || x.IsBumped == true : x.IsBumped == true) &&
                            x.Voyage.SiteId == siteId &&
                            x.Voyage.ClientId.HasValue &&
                            ownerIds.Contains(x.Voyage.ClientId.Value) &&
                            x.AssetId.HasValue &&
                            sharerIds.Contains(x.AssetId.Value) &&
                            (string.IsNullOrWhiteSpace(search) || x.CcuId.Contains(search) || x.CargoDescription.Contains(search)))
                .Include(x => x.Voyage).ThenInclude(x => x.Client)
                .Include(x => x.Asset)
                .AsNoTracking()
                .ToListAsync();
            return _mapper.Map<List<VoyageCargoModel>>(voyageCargoes);
        }

        public async Task<List<VoyageCargoModel>> GetCargoesByTransportRequestCargoIdAndVoyageId(List<Guid> transportRequestCargoIds, Guid voyageId)
        {
            var cargoFound = await GetAllByVoyageIdAsync(voyageId);

            if (cargoFound.Any())
            {
                return cargoFound.Where(x => transportRequestCargoIds.Any(y => y == x.TransportRequestCargoId)).ToList();
            }

            return new();
        }

        public async Task<List<Guid?>> GetTransportRequestCargoesThatCannotBeCancelledByVoyageId(Guid transportRequestId)
        {
                var voyageId = await _unitOfWork.Repository<TransportRequest>()
                    .Query()
                    .AsNoTracking()
                    .Where(x => x.TransportRequestId == transportRequestId)
                    .Select(x => x.VoyageId)
                    .FirstOrDefaultAsync();

            if (voyageId != null)
            {
                var cargoesFromRequestFoundInFlow = await GetAllByVoyageIdAsync((Guid)voyageId);

                if (cargoesFromRequestFoundInFlow.Any() && cargoesFromRequestFoundInFlow.Any(x => x.TransportRequestCargoId != null))
                {
                    var cargoesThatCannotBeCancelled = new List<Guid?>();

                    foreach (var cargoFoundInFlow in cargoesFromRequestFoundInFlow)
                    {
                        if (cargoFoundInFlow.PassedInspection
                            || cargoFoundInFlow.Dispatched
                            || cargoFoundInFlow.ArrivalTime.HasValue
                            || (cargoFoundInFlow.CustomsCleared.HasValue && cargoFoundInFlow.CustomsCleared.Value == true)
                            || cargoFoundInFlow.CompletedLift)
                        {
                            if (cargoFoundInFlow.TransportRequestCargoId.HasValue)
                                cargoesThatCannotBeCancelled.Add((Guid)cargoFoundInFlow.TransportRequestCargoId);
                        }
                    }
                    return cargoesThatCannotBeCancelled;
                }
            }
            return new();
        }

        public async Task<List<VoyageCargo>> GetCargoesByVoyageId(Guid voyageId)
        {
            return await _unitOfWork.Repository<VoyageCargo>()
                .Query(x => x.VoyageId == voyageId)
                .AsNoTracking()
                .ToListAsync();
        }

        public async Task<List<VoyageCargoBulk>> GetBulkCargoesByVoyageId(Guid voyageId)
        {
            return await _unitOfWork.Repository<VoyageCargoBulk>()
                .Query(x => x.VoyageId == voyageId)
                .AsNoTracking()
                .ToListAsync();
        }

        private readonly IVoyageCargoService _voyageCargoService;

        public async Task<VoyageCargoModel> GetByIdAsync(Guid id) {
            var voyage = await _unitOfWork.Repository<VoyageCargo>().Query(x => x.VoyageCargoId == id)
                .Include(x => x.VoyageCargoDangerousGoods)
                .ThenInclude(x => x.DangerousGood)
                .Include(x => x.Cargo)
                .AsNoTracking()
                .FirstOrDefaultAsync();
            var result = _mapper.Map<VoyageCargoModel>(voyage);
            result.HasLifts = await _unitOfWork.Repository<VoyageCargoLift>().Query(l => l.VoyageCargoId == id).AnyAsync();
            return result;
        }

        public async Task<VoyageCargoModel> CreateAsync(VoyageCargoUpsertModel upsertModel, UserModel user) {
            if ((upsertModel.RtRob.HasValue && upsertModel.RtRob == RtRob.RemainingOnBoard) && (!upsertModel.TransportRequest.HasValue || upsertModel.TransportRequest != VoyageCargoTransportRequest.RemainingOnBoard))
                throw new Exception("Transport request should be ROB when RtRob is set to ROB");
            if ((upsertModel.RtRob.HasValue && upsertModel.RtRob == RtRob.RoundTrip) && (!upsertModel.TransportRequest.HasValue || upsertModel.TransportRequest != VoyageCargoTransportRequest.RoundTrip))
                throw new Exception("Transport request should be RT when RtRob is set to RT");

            upsertModel.CargoLength = Math.Round((double)upsertModel.CargoLengthFt * 304.8, 3);
            upsertModel.CargoWidth = Math.Round((double)upsertModel.CargoWidthFt * 304.8, 3);

            var voyageCargo = _mapper.Map<VoyageCargo>(upsertModel);

            var measurementUnit = LocationMeasurementUnit.KG;

            if (user.LocationId.HasValue) {
                measurementUnit = Enum.Parse<LocationMeasurementUnit>(user.MeasurementUnit, ignoreCase: true);
            } else if (!user.LocationId.HasValue && voyageCargo.VoyageId.HasValue) {
                measurementUnit = await _voyageCargoWeightUtility.GetMeasurementUnitAsync(voyageCargo.VoyageId.Value);
            } else if (!user.LocationId.HasValue && !voyageCargo.VoyageId.HasValue && voyageCargo.SiteId.HasValue) {
                measurementUnit = await _voyageCargoWeightUtility.GetMeasurementUnitForCargo(voyageCargo.SiteId.Value);
            }

            voyageCargo.CargoWeightKg = _voyageCargoWeightUtility.ConvertWeight(voyageCargo.CargoWeightKg ?? 0, measurementUnit, false);

            if (voyageCargo.ActualWeight.HasValue) {
                voyageCargo.ActualWeight = _voyageCargoWeightUtility.ConvertWeight(voyageCargo.ActualWeight ?? 0, measurementUnit, false);
            }

            if (voyageCargo.SiteId.HasValue && !voyageCargo.VoyageId.HasValue) {
                voyageCargo.AssetId = null;
                voyageCargo.CargoBackloadStatus = CargoBackloadStatus.Discharged;
                voyageCargo.CargoBackloadDischargeDate = DateTime.UtcNow;
                voyageCargo.IsHighPriority = false;
                voyageCargo.CustomsEntryType = null;
                voyageCargo.Status = VoyageCargoStatus.Submitted;
            }
            
            if (upsertModel.IsCancelled || upsertModel.RtRob == RtRob.RoundTrip || upsertModel.IsBumped)
            {
                voyageCargo.BumpedOrCancelledOrRTDate = DateTime.UtcNow;
            }

            await AssignRowNumber(voyageCargo);

            var cargosByCcuId = await _unitOfWork.Repository<Cargo>()
                .Query(q => 
                    q.LocationId == user.LocationId && 
                    q.CCUId.ToLower() == voyageCargo.CcuId.ToLower() && 
                    !q.Deleted)
                .IgnoreQueryFilters()
                .ToListAsync();

            if (cargosByCcuId.Count > 0)
            {
                if (cargosByCcuId.Count == 1)
                {
                    var cargo = cargosByCcuId.First();
                    voyageCargo.CargoId = cargo.CargoId;
                }
                else if(cargosByCcuId.Count > 1)
                {
                    //this case when we find multiple cargos with the same CCUId under same Location
                    throw new Exception($"Multiple Cargos with the same CCUId {voyageCargo.CcuId} for this Location exist in the system. " +
                        $"Please select the Cargo needed from the Cargo Dropdown based on the Cargo Owner which is displayed in paranthesis.");
                }
            }
            else
            {
                var currentUserLocation = await _unitOfWork.Repository<Location>()
                    .Query(q => q.LocationId == user.LocationId)
                    .AsNoTracking()
                    .FirstOrDefaultAsync();

                if (currentUserLocation.CanCreateOneOffCargo)
                {
                    var adhocCargoDescription = await _unitOfWork.Repository<CargoDescription>()
                        .Query(q => q.IsAdhoc && !q.Deleted)
                        .AsNoTracking()
                        .IgnoreQueryFilters()
                        .FirstOrDefaultAsync();

                    if (adhocCargoDescription == null)
                    {
                        //create the ad-hoc cargo description
                        adhocCargoDescription = await _unitOfWork.Repository<CargoDescription>().CreateAsync(new CargoDescription
                        {
                            Description = cargoDescriptionAdHoc,
                            CreatedDate = DateTime.UtcNow,
                            CreatedById = user.UserId,
                            UpdatedDate = DateTime.UtcNow,
                            UpdatedById = user.UserId,
                            Deleted = false,
                            Disabled = false,
                            IsAdhoc = true
                        });
                    }

                    //this case is when we don't find a match in Cargo table based on the CCUID and Location. In this case we are dealing with an ad-hoc cargo that we need to create in Cargo table in database
                    await _unitOfWork.Repository<Cargo>().CreateAsync(new Cargo
                    {
                        Deleted = false,
                        IsAdhoc = true, //set IsAdhoc to true to differentiate from the other cargos.
                        IsApproved = false,
                        CreatedById = user.UserId,
                        CreatedDate = DateTime.UtcNow,
                        UpdatedById = user.UserId,
                        UpdatedDate = DateTime.UtcNow,
                        CCUId = voyageCargo.CcuId,
                        IsPool = false,
                        VendorId = voyageCargo.VendorId,
                        LengthMm = voyageCargo.CargoLengthMm ?? 0,
                        WidthMm = voyageCargo.CargoWidthMm ?? 0,
                        HeightMm = 0,
                        TareMassKg = 0,
                        MaxGrossWeightKg = voyageCargo.CargoWeightKg ?? 0,
                        LocationId = user.LocationId,
                        IsDeckCargo = false,
                        CargoDescriptionId = adhocCargoDescription.CargoDescriptionId,
                        CcuHireStatus = CargoHireStatus.OffHire,
                        Disabled = false
                    });
                }
                else
                {
                    throw new Exception("You cannot create a Voyage Cargo with this CCUId because your Location does not allow creating ad-hoc Cargos.");
                }
            }

            voyageCargo.CreatedById = user.UserId;
            await _unitOfWork.Repository<VoyageCargo>().CreateAsync(voyageCargo);
            await _unitOfWork.SaveChangesAsync();

            if (voyageCargo.VoyageId.HasValue) {
                await _voyageOffshoreLocationService.AddAllAssetsFromVoyageCargoesToOffshoreLocations(voyageCargo.VoyageId.Value, user);
            }
            
            if (upsertModel.VoyageCargoDangerousGoods.Count > 0)
            {
                foreach (var dangerousGood in upsertModel.VoyageCargoDangerousGoods)
                {
                    dangerousGood.VoyageCargoId = voyageCargo.VoyageCargoId;
                    await _voyageCargoDangerousGoodService.CreateAsync(dangerousGood);
                }
            }

            return await GetByIdAsync(voyageCargo.VoyageCargoId);
        }

        public async Task BatchCreateAsync(List<VoyageCargo> cargoBatch, UserModel user) {

            if (cargoBatch.Count == 0) return;

            var voyage = await _unitOfWork.Repository<Voyage>().GetAsync(cargoBatch[0].VoyageId.Value);
            var userId = user.UserId;
            var repository = _unitOfWork.Repository<VoyageCargo>();

            var latestRowNumber =
                await _unitOfWork.Repository<VoyageCargo>()
                    .Query(x => x.VoyageId == cargoBatch[0].VoyageId)
                    .MaxAsync(x => (int?)x.RowNumber) ?? 0;

            foreach (var cargo in cargoBatch) {
                var rtRob = cargo.RtRob;
                var transportRequest = cargo.TransportRequest;
                if ((rtRob.HasValue && rtRob == RtRob.RemainingOnBoard) && (!transportRequest.HasValue || transportRequest != VoyageCargoTransportRequest.RemainingOnBoard))
                    throw new Exception($"Error in cargo {cargo.CcuId} - Transport request should be ROB when RtRob is set to ROB");
                if ((rtRob.HasValue && rtRob == RtRob.RoundTrip) && (!transportRequest.HasValue || transportRequest != VoyageCargoTransportRequest.RoundTrip))
                    throw new Exception($"Error in cargo {cargo.CcuId} - Transport request should be RT when RtRob is set to RT");
                if ((!rtRob.HasValue || rtRob == RtRob.BoatSkip) && (!transportRequest.HasValue || (transportRequest != VoyageCargoTransportRequest.Collection && transportRequest != VoyageCargoTransportRequest.VendorDelivery)))
                    throw new Exception($"Error in cargo {cargo.CcuId} - Transport request should be either Collection or Vendor Delivery RT when RtRob is set to BoartSkip or null");

                cargo.RowNumber = ++latestRowNumber;
                cargo.Status = VoyageCargoStatus.Draft;

                cargo.CreatedById = userId;
                await repository.CreateAsync(cargo);
            }
            await _unitOfWork.SaveChangesAsync();

            await _voyageOffshoreLocationService.AddAllAssetsFromVoyageCargoesToOffshoreLocations(cargoBatch[0].VoyageId.Value, user);
        }

        public async Task<List<VoyageCargoModel>> BulkUpdateAsync(Guid voyageId, List<VoyageCargoUpsertModel> finalTotalList, UserModel currentUser) {
            if (finalTotalList.Where(x => x.VoyageCargoId.HasValue).Count() != finalTotalList.Where(x => x.VoyageCargoId.HasValue).DistinctBy(x => x.VoyageCargoId).Count())
                throw new Exception("Duplicate Entry in the list");

            var voyage = await _unitOfWork.Repository<Voyage>().GetAsync(voyageId);
            var measurementUnit = await _voyageCargoWeightUtility.GetMeasurementUnitAsync(voyageId);

            await _unitOfWork.BeginTransactionAsync();

            var currentVoyageCargoes = await _unitOfWork.Repository<VoyageCargo>().Query(x => x.VoyageId == voyageId)
                .Include(x => x.MaterialDetails)
                .ToListAsync();

            var cargoesContainingEditList = finalTotalList.Where(x => currentVoyageCargoes.Any(y => y.VoyageCargoId == x.VoyageCargoId)).ToList();
            cargoesContainingEditList.RemoveAll(x => x.IsBumped || x.IsCancelled || x.IsMoved);

            var itemsToDelete = currentVoyageCargoes.Where(x => !finalTotalList.Any(l => l.VoyageCargoId == x.VoyageCargoId)).ToList();
            foreach (var voyageCargoToDelete in itemsToDelete) {
                if (voyageCargoToDelete.MaterialDetails.Any())
                    throw new Exception("some Voyage Cargoes cannot get deleted since there is a material detail with this item.");
                voyageCargoToDelete.Deleted = true;

                if (voyageCargoToDelete.AssetId.HasValue) {
                    await _voyageOffshoreLocationService.DeleteAssetFromOffshoreLocationsNotInVoyageCargoes(voyageCargoToDelete.AssetId.Value, voyageCargoToDelete.VoyageId.Value, currentUser);
                }
                currentVoyageCargoes.Remove(voyageCargoToDelete);
            }
            await _unitOfWork.SaveChangesAsync();

            var voyageCargoesBeforeUpdate = await _unitOfWork.Repository<VoyageCargo>().Query(x => x.VoyageId == voyageId).AsNoTracking().ToListAsync();

            var listToCreate = new List<VoyageCargo>();

            double MaxLengthFt = 100;
            double MaxWidthFt = 40;
            double MaxLengthMm = Length.FromFeet(MaxLengthFt).Millimeters; // 100 ft → 30480 mm
            double MaxWidthMm = Length.FromFeet(MaxWidthFt).Millimeters; // 40 ft  → 12192 mm

            var adhocCargoDescription = await _unitOfWork.Repository<CargoDescription>()
                                    .Query(q => q.IsAdhoc && !q.Deleted)
                                    .AsNoTracking()
                                    .IgnoreQueryFilters()
                                    .FirstOrDefaultAsync();

            if (adhocCargoDescription == null)
            {
                //create the ad-hoc cargo description
                adhocCargoDescription = await _unitOfWork.Repository<CargoDescription>().CreateAsync(new CargoDescription
                {
                    Description = cargoDescriptionAdHoc,
                    CreatedDate = DateTime.UtcNow,
                    CreatedById = currentUser.UserId,
                    UpdatedDate = DateTime.UtcNow,
                    UpdatedById = currentUser.UserId,
                    Deleted = false,
                    Disabled = false,
                    IsAdhoc = true
                });
            }

            var currentUserLocation = await _unitOfWork.Repository<Location>()
                                .Query(q => q.LocationId == currentUser.LocationId)
                                .AsNoTracking()
                                .FirstOrDefaultAsync();

            foreach (var voyageCargoToUpsert in finalTotalList) {
                voyageCargoToUpsert.CargoWeight = _voyageCargoWeightUtility.ConvertWeight(voyageCargoToUpsert.CargoWeight ?? 0, measurementUnit, false);

                if ((voyageCargoToUpsert.RtRob.HasValue && voyageCargoToUpsert.RtRob == RtRob.RemainingOnBoard) &&
                    (!voyageCargoToUpsert.TransportRequest.HasValue || voyageCargoToUpsert.TransportRequest != VoyageCargoTransportRequest.RemainingOnBoard))
                    throw new Exception("Transport request should be ROB when RtRob is set to ROB");
                if ((voyageCargoToUpsert.RtRob.HasValue && voyageCargoToUpsert.RtRob == RtRob.RoundTrip) &&
                    (!voyageCargoToUpsert.TransportRequest.HasValue || voyageCargoToUpsert.TransportRequest != VoyageCargoTransportRequest.RoundTrip))
                    throw new Exception("Transport request should be RT when RtRob is set to RT");

                if (voyageCargoToUpsert.CargoLengthFt > MaxLengthFt)
                    throw new Exception(
                        $"Length too long. Max {MaxLengthFt}ft ({MaxLengthMm}mm).");
                if (voyageCargoToUpsert.CargoWidthFt > MaxWidthFt)
                    throw new Exception(
                        $"Width too wide. Max {MaxWidthFt}ft ({MaxWidthMm}mm).");

                voyageCargoToUpsert.CargoLength = Math.Round(Length.FromFeet((double)voyageCargoToUpsert.CargoLengthFt).Millimeters, 3);
                voyageCargoToUpsert.CargoWidth = Math.Round(Length.FromFeet((double)voyageCargoToUpsert.CargoWidthFt).Millimeters, 3);

                var weightCategory = await _unitOfWork.Repository<WeightCategory>()
                            .Query()
                            .FirstOrDefaultAsync(x => x.WeightCategoryId == voyageCargoToUpsert.WeightCategoryId);

                if (voyageCargoToUpsert.VoyageCargoId.HasValue) //if it's update
                {
                    var voyageCargo = currentVoyageCargoes.SingleOrDefault(x => x.VoyageCargoId == voyageCargoToUpsert.VoyageCargoId);

                    if (voyageCargo == null)
                        throw new Exception($"Some Voyage Cargoes do not belong to this Voyage or do not exist");

                    if (voyageCargoToUpsert.WeightCategoryId != voyageCargo.WeightCategoryId) {
                        if (weightCategory != null && weightCategory.IsActive == false)
                            throw new Exception("Cannot update weight category to an inactive weight category");
                    }

                    if (!voyageCargo.IsCancelled && voyageCargoToUpsert.IsCancelled) {
                        voyageCargo.BumpedOrCancelledOrRTDate = DateTime.UtcNow;
                    } else if (voyageCargo.RtRob != RtRob.RoundTrip && voyageCargoToUpsert.RtRob == RtRob.RoundTrip) {
                        voyageCargo.BumpedOrCancelledOrRTDate = DateTime.UtcNow;
                    } else if (!voyageCargo.IsBumped && voyageCargoToUpsert.IsBumped) {
                        voyageCargo.BumpedOrCancelledOrRTDate = DateTime.UtcNow;
                    }

                    /* PassedInspection cannot be changed on the edit screen and the frontend app never sends it, so
                     * it's always false at this point. It's necessary to borrow it from its current DB value. */
                    voyageCargoToUpsert.PassedInspection = voyageCargo.PassedInspection;

                    _mapper.Map(voyageCargoToUpsert, voyageCargo);

                    voyageCargo.CargoWeightKg = voyageCargoToUpsert.CargoWeight;

                    if (!string.IsNullOrWhiteSpace(voyageCargo.CcuId))
                    {
                        var cargosByCcuId = await _unitOfWork.Repository<Cargo>()
                        .Query(q =>
                            q.LocationId == currentUser.LocationId &&
                            q.CCUId.ToLower() == voyageCargo.CcuId.ToLower() &&
                            !q.Deleted)
                        .IgnoreQueryFilters()
                        .ToListAsync();

                        if (cargosByCcuId.Count > 0)
                        {
                            if (cargosByCcuId.Count == 1)
                            {
                                var cargo = cargosByCcuId.First();
                                voyageCargo.CargoId = cargo.CargoId;
                            }
                            else if (cargosByCcuId.Count > 1)
                            {
                                //this case when we find multiple cargos with the same CCUId under same Location
                                throw new Exception($"Multiple Cargos with the same CCUId {voyageCargo.CcuId} for this Location exist in the system. " +
                                    $"Please select the Cargo needed from the Cargo Dropdown based on the Cargo Owner which is displayed in paranthesis.");
                            }
                        }
                        else
                        {
                            if (currentUserLocation.CanCreateOneOffCargo)
                            {
                                //this case is when we don't find a match in Cargo table based on the CCUID and Location. In this case we are dealing with an ad-hoc cargo that we need to create in Cargo table in database
                                await _unitOfWork.Repository<Cargo>().CreateAsync(new Cargo
                                {
                                    Deleted = false,
                                    IsAdhoc = true, //set IsAdhoc to true to differentiate from the other cargos.
                                    IsApproved = false,
                                    CreatedById = currentUser.UserId,
                                    CreatedDate = DateTime.UtcNow,
                                    UpdatedById = currentUser.UserId,
                                    UpdatedDate = DateTime.UtcNow,
                                    CCUId = voyageCargo.CcuId,
                                    IsPool = false,
                                    VendorId = voyageCargo.VendorId,
                                    LengthMm = voyageCargo.CargoLengthMm ?? 0,
                                    WidthMm = voyageCargo.CargoWidthMm ?? 0,
                                    HeightMm = 0,
                                    TareMassKg = 0,
                                    MaxGrossWeightKg = voyageCargo.CargoWeightKg ?? 0,
                                    LocationId = currentUser.LocationId,
                                    IsDeckCargo = false,
                                    CargoDescriptionId = adhocCargoDescription.CargoDescriptionId,
                                    CcuHireStatus = CargoHireStatus.OffHire,
                                    Disabled = false
                                });
                            }
                            else
                            {
                                throw new Exception("You cannot create a Voyage Cargo with this CCUId because your Location does not allow creating ad-hoc Cargos.");
                            }
                        }
                    }                    

                    voyageCargo.UpdatedById = currentUser.UserId;
                    voyageCargo.UpdatedDate = DateTime.UtcNow;
                    voyageCargo.SiteId = voyage.SiteId;
                } else //if it's create
                  {
                    if (weightCategory != null && !weightCategory.IsActive) {
                        throw new Exception("Cannot update weight category to an inactive weight category");
                    }

                    var voyageCargo = _mapper.Map<VoyageCargo>(voyageCargoToUpsert);

                    _mapper.Map(voyageCargoToUpsert, voyageCargo);

                    if (voyageCargoToUpsert.IsCancelled || voyageCargoToUpsert.RtRob == RtRob.RoundTrip || voyageCargoToUpsert.IsBumped) {
                        voyageCargo.BumpedOrCancelledOrRTDate = DateTime.UtcNow;
                    }

                    voyageCargo.CargoWeightKg = voyageCargoToUpsert.CargoWeight;
                    if (!string.IsNullOrWhiteSpace(voyageCargo.CcuId))
                    {
                        var cargosByCcuId = await _unitOfWork.Repository<Cargo>()
                        .Query(q =>
                            q.LocationId == currentUser.LocationId &&
                            q.CCUId.ToLower() == voyageCargo.CcuId.ToLower() &&
                            !q.Deleted)
                        .IgnoreQueryFilters()
                        .ToListAsync();

                        if (cargosByCcuId.Count > 0)
                        {
                            if (cargosByCcuId.Count == 1)
                            {
                                var cargo = cargosByCcuId.First();
                                voyageCargo.CargoId = cargo.CargoId;
                            }
                            else if (cargosByCcuId.Count > 1)
                            {
                                //this case when we find multiple cargos with the same CCUId under same Location
                                throw new Exception($"Multiple Cargos with the same CCUId {voyageCargo.CcuId} for this Location exist in the system. " +
                                    $"Please select the Cargo needed from the Cargo Dropdown based on the Cargo Owner which is displayed in paranthesis.");
                            }
                        }
                        else
                        {
                            if (currentUserLocation.CanCreateOneOffCargo)
                            {
                                //this case is when we don't find a match in Cargo table based on the CCUID and Location. In this case we are dealing with an ad-hoc cargo that we need to create in Cargo table in database
                                await _unitOfWork.Repository<Cargo>().CreateAsync(new Cargo
                                {
                                    Deleted = false,
                                    IsAdhoc = true, //set IsAdhoc to true to differentiate from the other cargos.
                                    IsApproved = false,
                                    CreatedById = currentUser.UserId,
                                    CreatedDate = DateTime.UtcNow,
                                    UpdatedById = currentUser.UserId,
                                    UpdatedDate = DateTime.UtcNow,
                                    CCUId = voyageCargo.CcuId,
                                    IsPool = false,
                                    VendorId = voyageCargo.VendorId,
                                    LengthMm = voyageCargo.CargoLengthMm ?? 0,
                                    WidthMm = voyageCargo.CargoWidthMm ?? 0,
                                    HeightMm = 0,
                                    TareMassKg = 0,
                                    MaxGrossWeightKg = voyageCargo.CargoWeightKg ?? 0,
                                    LocationId = currentUser.LocationId,
                                    IsDeckCargo = false,
                                    CargoDescriptionId = adhocCargoDescription.CargoDescriptionId,
                                    CcuHireStatus = CargoHireStatus.OffHire,
                                    Disabled = false
                                });
                            }
                            else
                            {
                                throw new Exception("You cannot create a Voyage Cargo with this CCUId because your Location does not allow creating ad-hoc Cargos.");
                            }
                        }
                    }
                    voyageCargo.CreatedById = currentUser.UserId;
                    voyageCargo.SiteId = voyage.SiteId;

                    voyageCargo.RowNumber = listToCreate.Any() ?
                                                (listToCreate.Max(x => x.RowNumber) + 1) :
                                                currentVoyageCargoes.Any() ?
                                                    (currentVoyageCargoes.Max(x => x.RowNumber) + 1) : 1;

                    listToCreate.Add(voyageCargo);
                }
            }

            await _unitOfWork.SaveChangesAsync(); //this applies the updates

            await _unitOfWork.Repository<VoyageCargo>().BulkCreateAsync(listToCreate);

            await _unitOfWork.SaveChangesAsync(); //this applies the creates

            await _voyageOffshoreLocationService.AddAllAssetsFromVoyageCargoesToOffshoreLocations(finalTotalList[0].VoyageId.Value, currentUser);
            await _voyageOffshoreLocationService.UpdateAllOffshoreLocations(voyageCargoesBeforeUpdate, currentUser, finalTotalList[0].VoyageId.Value);

            await _unitOfWork.CommitAsync();
            return await GetAllByVoyageIdAsync(voyageId);
        }

        public async Task<List<VoyageCargoModel>> AddBumpedItemsAtQuayAsync(Guid voyageId, List<VoyageCargoUpsertModel> finalTotalList, UserModel currentUser) {
            var voyageCargoes = await _unitOfWork.Repository<VoyageCargo>()
                .Query(x => finalTotalList.Select(s => s.VoyageCargoId).Contains(x.VoyageCargoId))
                .ToListAsync();

            voyageCargoes.ForEach(item => {
                item.VoyageId = voyageId;
                item.IsBumped = false;
                item.Status = VoyageCargoStatus.Draft;
                item.UpdatedById = currentUser.UserId;
                item.UpdatedDate = DateTime.UtcNow;
                item.BumpedOrCancelledOrRTDate = DateTime.UtcNow;
            });

            await _unitOfWork.Repository<VoyageCargo>().BulkUpdate(voyageCargoes);
            await _unitOfWork.SaveChangesAsync();

            return await GetBumpedVoyageCargoesForVoyageAndSharersAndOwner(voyageId, string.Empty);
        }

        public async Task<List<VoyageCargoModel>> BulkMoveOrCopyCargoItemsAsync(Guid voyageId, List<VoyageCargoUpsertModel> finalTotalList, UserModel currentUser, string actionType = "move") {
            var voyageCargoes = await _unitOfWork.Repository<VoyageCargo>()
                .Query(x => finalTotalList.Select(s => s.VoyageCargoId).Contains(x.VoyageCargoId))
                .ToListAsync();

            var originalVoyageId = voyageCargoes.Select(s => s.VoyageId).First();

            var newVoyageCargoes = voyageCargoes.DeepCopy();

            newVoyageCargoes.ForEach(item => {
                item.VoyageId = voyageId;
                item.Status = VoyageCargoStatus.Draft;
                item.UpdatedById = currentUser.UserId;
                item.UpdatedDate = DateTime.Now;
                item.VoyageCargoId = Guid.NewGuid();
            });

            voyageCargoes.ForEach(item => {
                item.UpdatedById = currentUser.UserId;
                item.UpdatedDate = DateTime.UtcNow;

                if (actionType == "move") {
                    item.IsMoved = true;
                    item.MovedDate = DateTime.UtcNow;
                } else {
                    item.IsCopied = true;
                    item.CopiedDate = DateTime.UtcNow;
                }
            });

            await _unitOfWork.Repository<VoyageCargo>().BulkUpdate(voyageCargoes);
            await _unitOfWork.Repository<VoyageCargo>().BulkCreateAsync(newVoyageCargoes);
            await _unitOfWork.SaveChangesAsync();

            return await GetAllByVoyageIdAsync(originalVoyageId.Value);
        }

        private async Task AssignRowNumber(VoyageCargo voyageCargo) {
            var latestRowNumber = await _unitOfWork.Repository<VoyageCargo>()
            .Query(x => x.VoyageId == voyageCargo.VoyageId)
            .MaxAsync(x => (int?)x.RowNumber) ?? 0;

            voyageCargo.RowNumber = ++latestRowNumber;
        }

        public async Task<VoyageCargoModel> UndoDispatchAsync(Guid id, VoyageCargoUpsertModel upsertModel, UserModel user) {
            await _unitOfWork.BeginTransactionAsync();
            var cargoLoad = await _unitOfWork.Repository<VoyageCargoLoad>().Query(x => x.VoyageCargoLoadId == upsertModel.VoyageCargoLoadId)
                .Include(x => x.VoyageCargos)
                .SingleOrDefaultAsync();

            var voyageCargo = await _unitOfWork.Repository<VoyageCargo>()
                .Query(x => x.VoyageCargoId == id)
                .AsSplitQuery()
                .Include(x => x.VoyageCargoInspection)
                .Include(x => x.VoyageCargoLifts)
                .SingleOrDefaultAsync();

            _mapper.Map(upsertModel, voyageCargo);

            voyageCargo.VoyageCargoLoadId = null;
            voyageCargo.TrailerNumber = null;
            voyageCargo.TrailerId = null;
            voyageCargo.UpdatedDate = DateTime.UtcNow;
            voyageCargo.UpdatedById = user.UserId;

            if (cargoLoad.VoyageCargos.Count == 1) {
                cargoLoad.Deleted = true;
            }
            await _unitOfWork.SaveChangesAsync();

            await _unitOfWork.CommitAsync();

            return await GetByIdAsync(voyageCargo.VoyageCargoId);
        }

        public async Task<VoyageCargoModel> UpdateAsync(Guid id, VoyageCargoUpsertModel upsertModel, UserModel user) {
            var voyageCargoBeforeQuery = await _unitOfWork.Repository<VoyageCargo>()
                                            .Query(x => x.VoyageCargoId == id)
                                            .AsNoTracking()
                                            .Include(x => x.VoyageCargoInspection)
                                            .Include(x => x.VoyageCargoLifts)
                                            .SingleOrDefaultAsync();

            if (upsertModel.VoyageId.HasValue) {
                var voyage = await _unitOfWork.Repository<Voyage>().GetAsync(voyageCargoBeforeQuery.VoyageId.Value);

                if (upsertModel.IsCancelled) {
                    ValidateCancellation(voyageCargoBeforeQuery);
                }

                if (upsertModel.IsBumped && !(voyage.VoyageStatus == VoyageStatus.Released || voyage.VoyageStatus == VoyageStatus.Submitted)) {
                    throw new Exception("The selected cargo(s) are lifted and can not be bumped");
                }

                if (upsertModel.IsBumped && voyage.VoyageStatus == VoyageStatus.Released) {
                    foreach (var cargoLift in voyageCargoBeforeQuery.VoyageCargoLifts) {
                        if (!cargoLift.Inactive) {
                            throw new Exception("The selected cargo(s) are lifted and can not be bumped");
                        }
                    }
                }
            }
            var voyageCargoBeforeChange = voyageCargoBeforeQuery.DeepCopy();

            var voyageCargo = await _unitOfWork.Repository<VoyageCargo>().GetAsync(id);

            if (!voyageCargo.IsCancelled && upsertModel.IsCancelled) {
                voyageCargo.BumpedOrCancelledOrRTDate = DateTime.UtcNow;
            } else if (voyageCargo.RtRob != RtRob.RoundTrip && upsertModel.RtRob == RtRob.RoundTrip) {
                voyageCargo.BumpedOrCancelledOrRTDate = DateTime.UtcNow;
            } else if (!voyageCargo.IsBumped && upsertModel.IsBumped) {
                voyageCargo.BumpedOrCancelledOrRTDate = DateTime.UtcNow;
            }

            if (voyageCargo.SiteId.HasValue && !voyageCargo.VoyageId.HasValue) {
                voyageCargo.AssetId = null;
                voyageCargo.IsHighPriority = false;
                voyageCargo.CustomsEntryType = null;
                voyageCargo.Status = VoyageCargoStatus.Submitted;
            }

            upsertModel.CargoLength = Math.Round((double)upsertModel.CargoLengthFt * 304.8, 3);
            upsertModel.CargoWidth = Math.Round((double)upsertModel.CargoWidthFt * 304.8, 3);

            _mapper.Map(upsertModel, voyageCargo);

            var measurementUnit = LocationMeasurementUnit.KG;

            if (user.LocationId.HasValue) {
                measurementUnit = Enum.Parse<LocationMeasurementUnit>(user.MeasurementUnit, ignoreCase: true);
            } else if (!user.LocationId.HasValue && voyageCargo.VoyageId.HasValue) {
                measurementUnit = await _voyageCargoWeightUtility.GetMeasurementUnitAsync(voyageCargo.VoyageId.Value);
            } else if (!user.LocationId.HasValue && !voyageCargo.VoyageId.HasValue && voyageCargo.SiteId.HasValue) {
                measurementUnit = await _voyageCargoWeightUtility.GetMeasurementUnitForCargo(voyageCargo.SiteId.Value);
            }

            voyageCargo.CargoWeightKg = _voyageCargoWeightUtility.ConvertWeight(voyageCargo.CargoWeightKg ?? 0, measurementUnit, false);

            if (voyageCargo.ActualWeight.HasValue) {
                voyageCargo.ActualWeight = _voyageCargoWeightUtility.ConvertWeight(voyageCargo.ActualWeight ?? 0, measurementUnit, false);
            }

            var existingCargoWithSameCcuId = await _unitOfWork.Repository<Cargo>()
                .Query()
                .FirstOrDefaultAsync(x => x.CCUId == voyageCargo.CcuId);

            voyageCargo.CargoId = existingCargoWithSameCcuId?.CargoId;

            voyageCargo.UpdatedDate = DateTime.UtcNow;
            voyageCargo.UpdatedById = user.UserId;

            await _unitOfWork.SaveChangesAsync();

            if (upsertModel.VoyageId.HasValue) {
                if (upsertModel.AssetId != voyageCargoBeforeChange.AssetId && voyageCargoBeforeChange.AssetId.HasValue) {
                    await _voyageOffshoreLocationService.AddAllAssetsFromVoyageCargoesToOffshoreLocations(voyageCargo.VoyageId.Value, user);
                    await _voyageOffshoreLocationService.DeleteAssetFromOffshoreLocationsNotInVoyageCargoes(voyageCargoBeforeChange.AssetId.Value, voyageCargo.VoyageId.Value, user);
                }
            }

            return await GetByIdAsync(voyageCargo.VoyageCargoId);
        }

        private void ValidateCancellation(VoyageCargo voyageCargoBeforeQuery) {

            if (voyageCargoBeforeQuery.VoyageCargoInspection != null &&
                (
                       voyageCargoBeforeQuery.VoyageCargoInspection.Status == VoyageCargoInspectionStatus.Passed
                    || voyageCargoBeforeQuery.VoyageCargoInspection.Status == VoyageCargoInspectionStatus.Failed)
                ) {
                throw new Exception("The selected cargo(s) have passed inspection and cannot be deleted/cancelled. ");
            }

            if (voyageCargoBeforeQuery.CustomsApprovedToLoad == VoyageCargoCustomsApprovedToLoad.YES) {
                throw new Exception("The selected cargo(s) have been customs cleared and cannot be deleted/cancelled.");

            }

            if (voyageCargoBeforeQuery.ArrivalTime.HasValue) {
                throw new Exception("The selected cargo(s) have arrived and cannot be deleted/cancelled.");
            }

            if (voyageCargoBeforeQuery.DispatchTime.HasValue) {
                throw new Exception("The selected cargo(s) have been dispatched and cannot be deleted/cancelled.  ");
            }

            if (voyageCargoBeforeQuery.VoyageCargoLifts != null && voyageCargoBeforeQuery.VoyageCargoLifts.Where(l => !l.Inactive).Any()) {
                throw new Exception("The selected cargo(s) are lifted and cannot be deleted/cancelled.");
            }
        }

        public async Task BulkUpdateArrivalTimeAsync(List<Guid> ids, DateTime? dateTime, UserModel user) {
            var userId = user.UserId;
            var voyageCargos = await _unitOfWork.Repository<VoyageCargo>().Query(x => ids.Contains(x.VoyageCargoId)).ToListAsync();

            foreach (var voyageCargo in voyageCargos) {
                voyageCargo.ArrivalTime = dateTime ?? null;

                voyageCargo.UpdatedById = userId;
                voyageCargo.UpdatedDate = DateTime.UtcNow;
            }

            await PerformSnapshotUpdatesAsync(voyageCargos);

            await _unitOfWork.SaveChangesAsync();
        }

        public async Task UpdateInspectionDataAsync(Guid id, bool setArrivalTime, string trailerNumber, Guid userId) {
            if (await _unitOfWork.Repository<VoyageCargo>().SingleOrDefaultAsync(x => x.VoyageCargoId == id) is VoyageCargo voyageCargo) {
                if (setArrivalTime) {
                    voyageCargo.ArrivalTime = DateTime.UtcNow;
                } else {
                    voyageCargo.ArrivalTime = null;
                }
                voyageCargo.TrailerNumber = trailerNumber;
                voyageCargo.PassedInspection = trailerNumber is not null;

                voyageCargo.UpdatedById = userId;
                voyageCargo.UpdatedDate = DateTime.UtcNow;

                await PerformSnapshotUpdatesAsync([voyageCargo]);

                await _unitOfWork.SaveChangesAsync();
            } else {
                throw new Exception("The specified voyage cargo was not found");
            }
        }

        public async Task BulkDeleteAsync(List<Guid> ids, UserModel user) {
            var voyageCargoes = await _unitOfWork.Repository<VoyageCargo>()
                .Query(x => ids.Contains(x.VoyageCargoId) && !x.MaterialDetails.Any())
                .Include(x => x.VoyageCargoInspection)
                .Include(x => x.VoyageCargoLifts)
                .ToListAsync();

            if (voyageCargoes.Count != ids.Count) throw new Exception("Not all ids are valid or some of them belong to a Material Detail, aborting delete operation");

            foreach (var cargo in voyageCargoes) {
                ValidateCancellation(cargo);
            }

            voyageCargoes.ForEach(x => {
                x.UpdatedDate = DateTime.UtcNow;
                x.Deleted = true;
            }
            );

            await _unitOfWork.SaveChangesAsync();

            foreach (var voyageCargo in voyageCargoes.Where(x => x.AssetId.HasValue)) {
                await _voyageOffshoreLocationService.DeleteAssetFromOffshoreLocationsNotInVoyageCargoes(
                    voyageCargo.AssetId.Value,
                    voyageCargo.VoyageId.Value,
                    user
                );
            }

        }

        public async Task DeleteAsync(Guid id, UserModel user) {
            var materialDetailExists = await _unitOfWork.Repository<VoyageMaterialDetail>().ExistsAsync(x => x.VoyageCargoId == id);

            if (materialDetailExists)
                throw new Exception("Voyage Cargo cannot get deleted since there is a material detail with this item.");

            var voyageCargo = await _unitOfWork.Repository<VoyageCargo>()
                .Query(x => x.VoyageCargoId == id)
                .AsNoTracking()
                .Include(x => x.VoyageCargoInspection)
                .Include(x => x.VoyageCargoLifts)
                .SingleOrDefaultAsync();

            ValidateCancellation(voyageCargo);

            voyageCargo.Deleted = true;

            await _unitOfWork.SaveChangesAsync();

            if (voyageCargo.AssetId.HasValue) {
                await _voyageOffshoreLocationService.DeleteAssetFromOffshoreLocationsNotInVoyageCargoes(voyageCargo.AssetId.Value, voyageCargo.VoyageId.Value, user);
            }
        }

        public async Task CreateVoyageInspection(Guid voyageId, Guid userId) {
            var voyageInspectionExists = await _voyageInspectionService.VoyageInspectionExists(voyageId);

            VoyageInspectionModel voyageInpsection = !voyageInspectionExists ?
                await _voyageInspectionService.CreateAsync(voyageId, userId) :
                await _voyageInspectionService.GetVoyageInspectionByVoyageId(voyageId);

            try {
                await CreateVoyageCargoInspections(voyageId, voyageInpsection.VoyageInspectionId, userId);
            } catch {
                throw new Exception("Failed to create Voyage Cargo Inspections");
            }
        }

        public async Task BulkUpdateTransportStatusAsync(List<Guid> voyageCargoIds, VoyageCargoTransportStatus transportStatus) {
            if (voyageCargoIds == null || !voyageCargoIds.Any())
                throw new ArgumentException("No VoyageCargoId is provided");

            var voyageCargos = await _unitOfWork.Repository<VoyageCargo>()
                .Query(x => voyageCargoIds.Contains(x.VoyageCargoId))
                .ToListAsync();

            if (voyageCargos.Count != voyageCargoIds.Count)
                throw new Exception("Some Ids are invalid");

            var updater = await _userService.GetCurrentUser();

            voyageCargos.ForEach(x => {
                x.TransportStatus = transportStatus;
                x.UpdatedDate = DateTime.UtcNow;
                x.UpdatedById = updater.UserId;
            });

            await _unitOfWork.SaveChangesAsync();
        }

        public async Task BulkUpdateCustomsApprovedToLoadAsync(List<Guid> voyageCargoIds, VoyageCargoCustomsApprovedToLoad customsApprovedToLoad) {
            if (voyageCargoIds == null || !voyageCargoIds.Any())
                throw new ArgumentException("No VoyageCargoId is provided");

            var voyageCargos = await _unitOfWork.Repository<VoyageCargo>()
                .Query(x => voyageCargoIds.Contains(x.VoyageCargoId))
                .ToListAsync();

            if (voyageCargos.Count != voyageCargoIds.Count)
                throw new Exception("Some Ids are invalid");

            var updater = await _userService.GetCurrentUser();

            voyageCargos.ForEach(x => {
                x.CustomsApprovedToLoad = customsApprovedToLoad;
                x.TransportStatus = customsApprovedToLoad == VoyageCargoCustomsApprovedToLoad.YES ? VoyageCargoTransportStatus.Dispatchable : VoyageCargoTransportStatus.OnHoldCustoms;
                x.UpdatedDate = DateTime.UtcNow;
                x.UpdatedById = updater.UserId;
            });

            await PerformSnapshotUpdatesAsync(voyageCargos);

            await _unitOfWork.SaveChangesAsync();
        }

        public async Task BulkUpdateCustomsTypeAndReferenceNoAsync(List<Guid> voyageCargoIds, VoyageCargoCustomsEntryType customsEntryType, string referenceNumber) {
            if (voyageCargoIds == null || !voyageCargoIds.Any())
                throw new ArgumentException("No VoyageCargoId is provided");

            var voyageCargos = await _unitOfWork.Repository<VoyageCargo>()
                .Query(x => voyageCargoIds.Contains(x.VoyageCargoId))
                .ToListAsync();

            if (voyageCargos.Count != voyageCargoIds.Count)
                throw new Exception("Some Ids are invalid");

            var updater = await _userService.GetCurrentUser();

            voyageCargos.ForEach(x => {
                x.CustomsEntryType = customsEntryType;
                x.CustomReferenceNo = referenceNumber;
                x.UpdatedDate = DateTime.UtcNow;
                x.UpdatedById = updater.UserId;
            });

            await _unitOfWork.SaveChangesAsync();
        }

        public async Task BulkUpdateDispatchTimeAsync(List<Guid> voyageCargoIds, DateTime dispatchDate, string trailerRegistrationNumber, ClaimsPrincipal user) {
            var thisUser = await _userService.GetCurrentUser();
            if (voyageCargoIds == null || !voyageCargoIds.Any())
                throw new ArgumentException("No VoyageCargoId is provided");

            var voyageCargos = await _unitOfWork.Repository<VoyageCargo>()
                .Query(x => voyageCargoIds.Contains(x.VoyageCargoId))
                .Include(x => x.Voyage)
                .Include(x => x.Site)
                .ToListAsync();

            var locationId = voyageCargos[0].VoyageId.HasValue ? voyageCargos[0].Voyage.LocationId : voyageCargos[0].Site.LocationId;

            var trailer = await _unitOfWork.Repository<Trailer>()
                .Query(x => x.RegistrationNumber == trailerRegistrationNumber).FirstOrDefaultAsync();

            if (trailer == null) {
                var newTrailer = new Trailer {
                    RegistrationNumber = trailerRegistrationNumber,
                    LocationId = locationId.Value,
                    CreatedById = thisUser.UserId,
                    CreatedDate = DateTime.UtcNow,
                    IsAdhoc = true,
                };
                newTrailer = await _unitOfWork.Repository<Trailer>().CreateAsync(newTrailer);
                voyageCargos.ForEach(x => {
                    x.TrailerId = newTrailer.TrailerId;
                    x.TrailerNumber = newTrailer.RegistrationNumber;
                });

            } else {
                voyageCargos.ForEach(x => {
                    x.TrailerId = trailer.TrailerId;
                    x.TrailerNumber = trailer.RegistrationNumber;
                });
            }

            if (voyageCargos.Count != voyageCargoIds.Count)
                throw new Exception("Some Ids are invalid");

            voyageCargos.ForEach(x => {
                x.DispatchTime = dispatchDate;
                x.UpdatedDate = DateTime.UtcNow;
                x.UpdatedById = thisUser.UserId;
                x.CargoBackloadStatus = CargoBackloadStatus.Dispatched;
            });

            await PerformSnapshotUpdatesAsync(voyageCargos);

            await _unitOfWork.SaveChangesAsync();
        }

        public async Task BulkUnsetDispatchTimeAsync(List<Guid> voyageCargoIds) {
            if (voyageCargoIds == null || !voyageCargoIds.Any())
                throw new ArgumentException("No VoyageCargoId is provided");

            var voyageCargos = await _unitOfWork.Repository<VoyageCargo>()
                .Query(x => voyageCargoIds.Contains(x.VoyageCargoId))
                .ToListAsync();

            if (voyageCargos.Count != voyageCargoIds.Count)
                throw new Exception("Some Ids are invalid");

            var updater = await _userService.GetCurrentUser();

            voyageCargos.ForEach(x => {
                x.DispatchTime = null;
                x.CargoBackloadStatus = CargoBackloadStatus.Discharged;
                x.UpdatedDate = DateTime.UtcNow;
                x.UpdatedById = updater.UserId;
                x.TrailerId = null;
                x.TrailerNumber = null;
            });

            await PerformSnapshotUpdatesAsync(voyageCargos);

            await _unitOfWork.SaveChangesAsync();
        }

        private async Task CreateVoyageCargoInspections(Guid voyageId, Guid voyageInspectionId, Guid userId) {
            List<VoyageCargo> CreateList = new List<VoyageCargo>();

            var voyageCargos = await _unitOfWork.Repository<VoyageCargo>().Query(x => x.VoyageId == voyageId)
                                                .IgnoreQueryFilters()
                                                .Include(x => x.VoyageCargoInspection)
                                                .ToListAsync();
            CreateList = voyageCargos.Where(x => x.VoyageCargoInspection is null).ToList();

            try {
                await DeleteVoyageCargoInspections(voyageCargos);
                await _voyageCargoInspectionService.BulkCreate(CreateList, userId, voyageInspectionId);
            } catch {
                throw new Exception("Cannot create Cargo inspection ");
            }
        }

        private async Task DeleteVoyageCargoInspections(List<VoyageCargo> voyageCargos) {
            List<VoyageCargoInspection> DeleteList = new List<VoyageCargoInspection>();
            DeleteList = GetDeleteableVoyageCargoInspections(voyageCargos);
            DeleteList.ForEach(x => x.Deleted = true);
            await _unitOfWork.SaveChangesAsync();
        }

        private List<VoyageCargoInspection> GetDeleteableVoyageCargoInspections(List<VoyageCargo> voyageCargos) {
            return voyageCargos
                .Where(x => x.Deleted && x.VoyageCargoInspection != null && !x.VoyageCargoInspection.Deleted)
                .Select(x => x.VoyageCargoInspection)
                .ToList();
        }

        public async Task<List<VoyageCargoLiftingListModel>> GetAllLiftingListVoyageCargoesAsync(Guid voyageId) {
            //manual query so that we don't load unncessary includes for just one field from them
            return await _unitOfWork.Repository<VoyageCargo>()
                .Query(x =>
                x.VoyageId == voyageId &&
                (x.Status == VoyageCargoStatus.Submitted || (x.Status == VoyageCargoStatus.Draft && x.AssetId == null)) &&
                !x.IsBumped &&
                !x.IsCancelled)
                .Include(voyage => voyage.WeightCategory)
                .OrderBy(x => x.RtRob == RtRob.RemainingOnBoard)
                    .ThenBy(x => x.RowNumber)
                .Select(x => new VoyageCargoLiftingListModel() {
                    VoyageCargoId = x.VoyageCargoId,
                    CcuId = x.CcuId == null || x.CcuId == "" ? x.Cargo.CCUId : x.CcuId,
                    LiftedAtAreaName = x.LiftedAtArea.Name,
                    AssetName = x.Asset.Name,
                    CargoDescription = x.CargoDescription,
                    VendorName = x.Vendor.VendorName,
                    Quantity = x.Quantity,
                    CargoWeightKg = x.CargoWeightKg.HasValue
                    ? (x.Voyage.Location.MeasurementUnit == LocationMeasurementUnit.Tonne
                        ? x.CargoWeightKg / 1000
                        : x.CargoWeightKg)
                    : null,
                    ActualWeight = x.Voyage.Location.MeasurementUnit == LocationMeasurementUnit.Tonne
                                ? x.ActualWeight / 1000
                                : x.ActualWeight,
                    LatestCapturedLiftAbsValue = x.VoyageCargoLifts.Where(lift => !lift.Inactive).Any()
                        ? (x.Voyage.Location.MeasurementUnit == LocationMeasurementUnit.Tonne
                            ? Math.Abs(x.VoyageCargoLifts
                                .Where(lift => !lift.Inactive)
                                .OrderByDescending(lift => lift.CreatedDate)
                                .FirstOrDefault().CapturedWeightKg) / 1000
                            : Math.Abs(x.VoyageCargoLifts
                                .Where(lift => !lift.Inactive)
                                .OrderByDescending(lift => lift.CreatedDate)
                                .FirstOrDefault().CapturedWeightKg))
                        : null,
                    QuantityLifted = (x.VoyageCargoLifts.Count(x => !x.Inactive && x.CapturedWeightKg > 0)) - (x.VoyageCargoLifts.Count(x => !x.Inactive && x.CapturedWeightKg < 0)),
                    DeckLocation = x.DeckLocation,
                    PriorityOrder = x.PriorityOrder,
                    IsHighPriority = x.IsHighPriority,
                    DangerousCount = x.VoyageCargoDangerousGoods.Count(),
                    WeightCategoryWeightType = x.WeightCategory != null ? x.WeightCategory.WeightType : string.Empty,
                    RtRob = x.RtRob,
                    LiftingTag = x.LiftingTag,
                    RowNumber = x.RowNumber,
                    TrailerNumber = x.TrailerNumber,
                    CompletedLift = x.CompletedLift,
                    InspectionStatus = x.VoyageCargoInspection == null ? VoyageCargoInspectionStatus.Uninspected : x.VoyageCargoInspection.Status
                })
                .ToListAsync();
        }

        public async Task<bool> DoesVoyageHaveIncompletedCargoes(Guid voyageId) {
            return await _unitOfWork.Repository<VoyageCargo>()
                .ExistsAsync(x => x.VoyageId == voyageId && x.Status == VoyageCargoStatus.Submitted && !x.IsBumped && !x.IsCancelled && !x.CompletedLift);
        }

        public async Task CompleteLiftAsync(Guid voyageCargoId) {
            var voyageCargo = await CompleteVoyageCargoNoSaveChange(voyageCargoId);

            if (!voyageCargo.VoyageCargoLifts.Any(x => !x.Inactive))
                throw new Exception("Voyage Cargo doesn't have any captured lifts");

            await _unitOfWork.SaveChangesAsync();
        }

        private async Task<VoyageCargo> CompleteVoyageCargoNoSaveChange(Guid voyageCargoId) {
            var voyageCargo = await _unitOfWork.Repository<VoyageCargo>()
                .Query(x => x.VoyageCargoId == voyageCargoId)
                .Include(x => x.VoyageCargoLifts)
                .Include(x => x.Voyage)
                .SingleOrDefaultAsync();

            if (voyageCargo == null)
                throw new Exception("Voyage Cargo doesn't exist");

            if (voyageCargo.IsCancelled || voyageCargo.IsBumped)
                throw new Exception("Voyage Cargo cannot be completed since it's bumped or canceled");

            if (!(voyageCargo.Status == VoyageCargoStatus.Submitted && voyageCargo.AssetId.HasValue) && !(voyageCargo.Status == VoyageCargoStatus.Draft && voyageCargo.AssetId == null))
                throw new Exception("Voyage Cargo is not submitted or is not extra");

            if (voyageCargo.Voyage.VoyageStatus != VoyageStatus.Released)
                throw new Exception("Voyage is not released");

            var jobService = _serviceProvider.GetService<IVoyageLiftingJobService>(); //to prevent dependency loop
            var job = await jobService.GetLatestStatusAsync(voyageCargo.VoyageId.Value);

            if (job == null || job.Status != VoyageLiftingJobStatus.Incompleted)
                throw new Exception("Job has not started yet");

            var currentUser = await _userService.GetCurrentUser();
            voyageCargo.CompletedLift = true;
            voyageCargo.CargoBackloadStatus = CargoBackloadStatus.Discharged;
            voyageCargo.CargoBackloadDischargeDate = DateTime.UtcNow;
            voyageCargo.UpdatedById = currentUser.UserId;
            voyageCargo.UpdatedDate = DateTime.UtcNow;
            voyageCargo.LiftedAtAreaId = job.AreaId;

            return voyageCargo;
        }

        public async Task ROBLiftCompleteAsync(Guid voyageCargoId) {
            var voyageCargo = await CompleteVoyageCargoNoSaveChange(voyageCargoId);

            if (voyageCargo.RtRob != RtRob.RemainingOnBoard)
                throw new Exception("Voyage Cargo is not ROB");

            if (voyageCargo.CompletedLift && voyageCargo.LiftingTag.HasValue && voyageCargo.LiftingTag == VoyageCargoLiftingTag.ROB)
                throw new Exception("VoyageCargo is already completed with ROB tag");

            voyageCargo.LiftingTag = VoyageCargoLiftingTag.ROB;

            await _unitOfWork.SaveChangesAsync();
        }

        public async Task NOBLiftCompleteAsync(Guid voyageCargoId) {
            var voyageCargo = await CompleteVoyageCargoNoSaveChange(voyageCargoId);

            if (voyageCargo.Voyage.VoyageStatus != VoyageStatus.Released || voyageCargo.Voyage.VoyageDirection != VoyageDirection.Inbound)
                throw new Exception("Voyage is not released or not inbound");

            if (voyageCargo.CompletedLift && voyageCargo.LiftingTag.HasValue && voyageCargo.LiftingTag == VoyageCargoLiftingTag.NOB)
                throw new Exception("VoyageCargo is already completed with NOB tag");

            voyageCargo.LiftingTag = VoyageCargoLiftingTag.NOB;

            await _unitOfWork.SaveChangesAsync();
        }

        public async Task<List<VoyageCargoLiftedOnVesselModel>> GetLiftedListAsync(Guid voyageId) {
            //manual query so that we don't load unncessary includes for just one field from them
            return await _unitOfWork.Repository<VoyageCargo>()
                .Query(x => x.VoyageId == voyageId &&
                            (x.Voyage.VoyageStatus == VoyageStatus.Loaded || x.Voyage.VoyageStatus == VoyageStatus.Discharged) &&
                            x.CompletedLift == true)
                .OrderBy(x => x.RowNumber)
                    .ThenBy(x => x.CreatedDate)
                .Select(x => new VoyageCargoLiftedOnVesselModel() {
                    CcuId = x.CcuId,
                    LiftedAtAreaName = x.LiftedAtArea.Name,
                    AssetName = x.Asset.Name,
                    VendorName = x.Vendor.VendorName,
                    Quantity = x.Quantity,
                    Weight = _voyageCargoWeightUtility.ConvertWeight(x.CargoWeightKg, x.Voyage.Location.MeasurementUnit, true),
                    ActualWeight = x.ActualWeight,
                    IsHighPriority = x.IsHighPriority,
                    HasDangerousGoods = x.VoyageCargoDangerousGoods.Any(),
                    IsHeavy = x.WeightCategory != null && x.WeightCategory.WeightType == "Heavy",
                    RtRob = x.RtRob,
                    IsDispatched = x.DispatchTime.HasValue,
                    AssetId = x.AssetId,
                    CustomsApprovedToLoad = x.CustomsApprovedToLoad,
                })
                .ToListAsync();
        }

        public async Task<VoyageCargoDeckPlanGroupModel> GetAllDeckPlanCargoesByVoyageId(Guid voyageId) {
            //manual query to not load whole cargo into ram
            var voyageCargoes = await _unitOfWork.Repository<VoyageCargo>()
                .Query(x =>
                x.VoyageId == voyageId &&
                !x.IsBumped &&
                !x.IsCancelled &&
                x.Status == VoyageCargoStatus.Submitted &&
                (x.Voyage.VoyageStatus == VoyageStatus.Released || x.Voyage.VoyageStatus == VoyageStatus.Loaded || x.Voyage.VoyageStatus == VoyageStatus.Discharged) &&
                x.Voyage.VoyageDirection == VoyageDirection.Outbound &&
                x.AssetId.HasValue
                )
                .Select(x => new VoyageCargoDeckPlanModel {
                    VoyageCargoId = x.VoyageCargoId,
                    XVesselPositionMm = x.XVesselPosition,
                    YVesselPositionMm = x.YVesselPosition,
                    CargoLengthMm = x.CargoLengthMm,
                    CargoWidthMm = x.CargoWidthMm,
                    CcuId = x.CcuId == null || x.CcuId == "" ? x.Cargo.CCUId : x.CcuId,
                    RowNumber = x.RowNumber,
                    CargoType = x.Cargo.CargoType.Name,
                    AssetName = x.Asset.Name,
                    AreaName = x.LiftedAtArea.Name,
                    AssetId = x.AssetId.Value,
                    IsRotated = false,
                    HasDangerousGood = x.VoyageCargoDangerousGoods.Any(),
                    ActualWeight = x.Voyage.Location.MeasurementUnit == LocationMeasurementUnit.Tonne
                                ? x.ActualWeight / 1000
                                : x.ActualWeight,
                })
                .ToListAsync();

            if (voyageCargoes.Any(x => !x.CargoWidthMm.HasValue || !x.CargoLengthMm.HasValue))
                throw new Exception("Not all cargoes have Length or Width");

            //to inform UI if vessel must be shown rtl or ltr
            var VesselIsRightToLeft = await _unitOfWork.Repository<Voyage>()
                .Query(x => x.VoyageId == voyageId)
                .Select(x => x.VesselIsRightToLeft)
                .FirstOrDefaultAsync();

            //getting all the assets for voyagecargoes of voyage, to group the cargoes by them
            var assets = await _unitOfWork.Repository<VoyageCargo>()
                .Query(x =>
                x.VoyageId == voyageId &&
                !x.IsBumped &&
                !x.IsCancelled &&
                x.Status == VoyageCargoStatus.Submitted &&
                (x.Voyage.VoyageStatus == VoyageStatus.Released || x.Voyage.VoyageStatus == VoyageStatus.Loaded || x.Voyage.VoyageStatus == VoyageStatus.Discharged) &&
                x.Voyage.VoyageDirection == VoyageDirection.Outbound &&
                x.AssetId.HasValue)
                .Select(x => x.Asset)
                .OrderBy(x => x.Name)
                .ToListAsync();

            assets = assets.DistinctBy(x => x.AssetId).ToList();

            var result = new VoyageCargoDeckPlanGroupModel() {
                VesselIsRightToLeft = VesselIsRightToLeft,
                Items = new()
            };

            //assigning cargoes to each group
            foreach (var asset in assets) {
                result.Items.Add(new VoyageCargoDeckPlanGroupItemModel() {
                    AssetName = asset.Name,
                    AssetId = asset.AssetId,
                    AssetColor = asset.Color,
                    VoyageCargoes = voyageCargoes.Where(x => x.AssetId == asset.AssetId).ToList(),
                });
            }

            return result;
        }

        public async Task CreateExtraAsync(VoyageCargoExtraCreateModel voyageCargoExtraCreateModel) {
            await _unitOfWork.BeginTransactionAsync();

            if (await _unitOfWork.Repository<VoyageCargo>().ExistsAsync(x =>
                    x.VoyageId == voyageCargoExtraCreateModel.VoyageId &&
                    x.CcuId == voyageCargoExtraCreateModel.CcuId &&
                    !x.IsCancelled &&
                    !x.IsBumped &&
                    (x.Status == VoyageCargoStatus.Submitted || (x.Status == VoyageCargoStatus.Draft && x.AssetId == null))))
                throw new Exception($"CcuId {voyageCargoExtraCreateModel.CcuId} already exists in current lifting");

            if (!await _unitOfWork.Repository<Voyage>().ExistsAsync(x => x.VoyageId == voyageCargoExtraCreateModel.VoyageId && x.VoyageDirection == VoyageDirection.Inbound))
                throw new Exception("Voyage is not Inbound");

            var currntUser = await _userService.GetCurrentUser();
            var voyageCargo = _mapper.Map<VoyageCargo>(voyageCargoExtraCreateModel);

            var existingCargoWithSameCcuId = await _unitOfWork.Repository<Cargo>()
               .Query()
               .Include(x => x.Vendor)
               .FirstOrDefaultAsync(x => (x.CCUId + " " + x.Vendor.VendorName) == voyageCargoExtraCreateModel.CcuId);

            if (existingCargoWithSameCcuId != null)
                voyageCargo.CargoId = existingCargoWithSameCcuId.CargoId;

            voyageCargo.CreatedById = currntUser.UserId;
            voyageCargo.Status = VoyageCargoStatus.Draft;
            voyageCargo.IsExtra = true;
            if (voyageCargoExtraCreateModel.BoatSkip)
                voyageCargo.RtRob = RtRob.BoatSkip;
            await AssignRowNumber(voyageCargo);
            await _unitOfWork.Repository<VoyageCargo>().CreateAsync(voyageCargo);
            await _unitOfWork.SaveChangesAsync();

            var voyageCargoLiftService = _serviceProvider.GetService<IVoyageCargoLiftService>(); //we cannot directly inject this service since it will cause circular dependencies

            await voyageCargoLiftService.CreateAndCompleteAsync(new VoyageCargoLiftCreateModel() {
                AreaId = voyageCargoExtraCreateModel.LiftedAtAreaId,
                CapturedWeight = voyageCargoExtraCreateModel.CapturedWeight,
                LoadCellId = voyageCargoExtraCreateModel.LoadCellId,
                VoyageCargoId = voyageCargo.VoyageCargoId,
                IsUnload = false
            });

            await _unitOfWork.CommitAsync();
        }

        public async Task<(byte[] file, string name)> GetVoyageCargoReport(Guid voyageId) {
            var voyageCargoes = await _unitOfWork.Repository<VoyageCargo>()
                .Query(x =>
                    x.VoyageId == voyageId &&
                    (x.Status == VoyageCargoStatus.Submitted || (x.Status == VoyageCargoStatus.Draft && x.AssetId == null)) &&
                    !x.IsBumped &&
                    !x.IsCancelled
                    )
                .OrderBy(x => x.RowNumber)
                .Select(x => new VoyageCargoReportModel {
                    Description = x.CargoDescription,
                    DG = string.Join(',', x.VoyageCargoDangerousGoods.Select(x => x.DangerousGood.Class)),
                    Asset = x.Asset.Name,
                    CcuId = x.CcuId,
                    Comments = x.Comments,
                    CustomsStatus = x.CustomStatus,
                    Length = x.CargoLengthMm,
                    Width = x.CargoWidthMm,
                    Quantity = x.Quantity,
                    UnNo = string.Join(',', x.VoyageCargoDangerousGoods.Select(x => x.DangerousGood.UnNo)),
                    Vendor = x.Vendor.VendorName,
                    Weight = x.VoyageCargoLifts.Any(x => !x.Inactive) ? x.VoyageCargoLifts.Where(x => !x.Inactive).Sum(x => x.CapturedWeightKg) : x.CargoWeightKg,
                    RtRob = x.RtRob,
                })
                .ToListAsync();

            var voyage = await _unitOfWork.Repository<Voyage>().Query(x => x.VoyageId == voyageId)
                .Include(x => x.Vessel)
                .Include(x => x.Client)
                .SingleOrDefaultAsync();

            Stream logoStream = null;

            if (voyage.Client != null && voyage.Client.ClientLogoId.HasValue) {
                logoStream = (await _storageService.DownloadStreamAsync(BlobStorageFolderConstant.Client, voyage.Client.ClientLogoId.Value)).Item1;
            }

            var file = _exportService.ConvertVoyageCargoesToExcel(voyageCargoes, voyage.Vessel.Name, voyage.VoyageNumber, voyage.Client?.VATNumber, voyage.Client?.EUNumber, logoStream);
            var now = DateTime.UtcNow.ToString("dd_MM_yy");
            var fileName = $"{voyage.Vessel.Name}_cargolist_{now}_0000.xlsx";

            await _voyageAttachmentService.AddOrReplaceCargoReportAttachmentAsync(
                voyageId,
                new VoyageAttachmentCargoReportModel
                {
                    Attachment = new FormFile(new MemoryStream(file), 0, file.Length, fileName, fileName)
                    {
                        Headers = new HeaderDictionary(1),
                        ContentType = "application/pdf"
                    }
                }
            );

            return (file, fileName);
        }

        public async Task<(byte[] file, string name)> GetDispatchVoyageCargoReport(List<Guid> cargoIds, string timezone)
        {
            var currntUser = await _userService.GetCurrentUser();
            var measurementUnit = Enum.Parse<LocationMeasurementUnit>(currntUser.MeasurementUnit, ignoreCase: true);

            
            var voyageCargos = await _unitOfWork.Repository<VoyageCargo>()
                .Query()
                .AsNoTracking()
                .AsSplitQuery()
                .Where(vc => cargoIds.Contains(vc.VoyageCargoId))
                .Select(x => new VoyageCargoQueryModel {
                    VoyageCargoId = x.VoyageCargoId,
                    AssetId = x.AssetId,
                    CargoDescription = x.CargoDescription,
                    ActualWeight = measurementUnit == LocationMeasurementUnit.Tonne
                        ? x.ActualWeight / 1000
                        : x.ActualWeight,
                    CcuId = x.CcuId,
                    CreatedDate = x.CreatedDate,
                    Comments = x.Comments,
                    IsHighPriority = x.IsHighPriority,
                    Owner = x.Owner,
                    Quantity = x.Quantity,
                    UpdatedDate = x.UpdatedDate,
                    VendorId = x.VendorId,
                    DistrictId = x.DistrictId,
                    VoyageId = x.VoyageId,
                    VendorName = x.Vendor.VendorName,
                    VoyageVesselId = x.Voyage.Vessel.VesselId,
                    VoyageVesselName = x.Voyage.Vessel.Name,
                    DangerousGoodCount = x.VoyageCargoDangerousGoods.Count,
                    AssetName = x.Asset.Name,
                    VoyageOwnerId = x.VoyageId.HasValue ? x.Voyage.ClientId : x.ClientId,
                    VoyageOwnerName = x.VoyageId.HasValue ? x.Voyage.Client.Name : x.Client.Name,
                    CargoCCUId = x.Cargo.CCUId,
                    DispatchTime = x.DispatchTime,
                    LoadingDate = x.VoyageId.HasValue ? x.VoyageCargoLifts.Min(x => (DateTime?)x.CreatedDate) : x.CreatedDate,
                    DistrictName = x.District.DistrictName,
                    SiteId = x.VoyageId.HasValue ? x.Voyage.SiteId : x.SiteId,
                    ClientId = x.VoyageId.HasValue ? x.Voyage.ClientId : x.ClientId,
                    SiteName = x.VoyageId.HasValue ? x.Voyage.Site.Name : x.Site.Name,
                    DangerousGoods = _mapper.Map<List<VoyageCargoDangerousGoodModel>>(x.VoyageCargoDangerousGoods),
                    DangerousGoodClassValues = x.VoyageCargoDangerousGoods != null 
                        ? string.Join(", ", x.VoyageCargoDangerousGoods
                            .Select(x => x.DangerousGood.Class))  // Evaluated client-side
                        : string.Empty,
                    ManifestNumbers = x.VoyageId != null && x.Asset.VoyageOffshoreLocations != null
                        ? string.Join(", ", x.Asset.VoyageOffshoreLocations
                            .Where(location => location.VoyageId == x.VoyageId)
                            .Select(location => location.ManifestNumber))
                        : string.Empty
                })
                .ToListAsync();
            
            var orderedVoyageCargos = cargoIds
                .Join(voyageCargos, id => id, vc => vc.VoyageCargoId, (id, vc) => vc)
                .ToList();

            var file = _exportService.ConvertEquipmentDispatchVoyageCargosToExcel(orderedVoyageCargos, timezone);
            
            var fileDate = DateTimezoneFormatting.ConvertDateFromUtc(DateTime.UtcNow, timezone).ToString("dd_MM_yyyy_HHmm");
            var fileName = $"Equipment Dispatch_Cargo List_{fileDate}.xlsx";

            return (file, fileName);
        }
        

        public async Task UpdateDeckPlanPositionsAsync(Guid voyageId, VoyageCargoDeckPlanUpdateGroupModel model) {
            if (model.Items == null || model.Items.Count == 0)
                throw new Exception("Invalid data");

            var updatedVoyageCargoes = model.Items
                .SelectMany(x => x.VoyageCargoes)
                .ToList();

            var ids = updatedVoyageCargoes.Select(x => x.VoyageCargoId).ToList();

            //getting all voyage cargoes while still respecting the same requirements as the GET method
            var voyageCargoes = await _unitOfWork.Repository<VoyageCargo>()
                .Query(x =>
                x.VoyageId == voyageId &&
                !x.IsBumped &&
                !x.IsCancelled &&
                x.Status == VoyageCargoStatus.Submitted &&
                (x.Voyage.VoyageStatus == VoyageStatus.Released || x.Voyage.VoyageStatus == VoyageStatus.Loaded || x.Voyage.VoyageStatus == VoyageStatus.Discharged) &&
                x.Voyage.VoyageDirection == VoyageDirection.Outbound &&
                ids.Contains(x.VoyageCargoId))
                .Include(x => x.Voyage)
                    .ThenInclude(x => x.Vessel)
                .ToListAsync();

            if (voyageCargoes.Any(x => !x.CargoWidthMm.HasValue || !x.CargoLengthMm.HasValue))
                throw new Exception("Not all cargoes have Length or Width");

            var currentUser = await _userService.GetCurrentUser();

            if (voyageCargoes.Any()) {
                var vessel = voyageCargoes.First().Voyage.Vessel;

                //updating each voyage cargo's coordinate, but not commiting until making sure they are not overlapping
                foreach (var updateModel in updatedVoyageCargoes) {
                    var entity = voyageCargoes.SingleOrDefault(x => x.VoyageCargoId == updateModel.VoyageCargoId);

                    if (entity == null)
                        throw new Exception("There are some invalid or missing data");

                    var xPositionsAreEqual = (!entity.XVesselPosition.HasValue && !updateModel.XVesselPositionMm.HasValue) ||
                        (entity.XVesselPosition.HasValue && updateModel.XVesselPositionMm.HasValue && Math.Abs(entity.XVesselPosition.Value - updateModel.XVesselPositionMm.Value) < 0.00001);
                    var yPositionsAreEqual = (!entity.YVesselPosition.HasValue && !updateModel.YVesselPositionMm.HasValue) ||
                        (entity.YVesselPosition.HasValue && updateModel.YVesselPositionMm.HasValue && Math.Abs(entity.YVesselPosition.Value - updateModel.YVesselPositionMm.Value) < 0.00001);

                    if (!xPositionsAreEqual || !yPositionsAreEqual || updateModel.IsRotated) {
                        entity.XVesselPosition = updateModel.XVesselPositionMm;
                        entity.YVesselPosition = updateModel.YVesselPositionMm;
                        entity.UpdatedById = currentUser.UserId;
                        entity.UpdatedDate = DateTime.UtcNow;

                        //applying the isRotated (swapping width and length)
                        if (updateModel.IsRotated) {
                            var length = entity.CargoLengthMm;
                            entity.CargoLengthMm = entity.CargoWidthMm;
                            entity.CargoWidthMm = length;
                        }
                    }
                }

                //making sure no cargoes are overlappingn, or no cargoes are outside of vessel boundaries (throws exception if it does)
                SquaresOverlapping(voyageCargoes.Where(x => x.XVesselPosition.HasValue && x.YVesselPosition.HasValue).ToList(), vessel);
            }

            //if voyage's Vessel (showing) direction (ltr or rtl) has changed, then we need to update it in voyage
            var voyage = await _unitOfWork.Repository<Voyage>().GetAsync(voyageId);
            if (voyage.VesselIsRightToLeft != model.VesselIsRightToLeft) {
                voyage.VesselIsRightToLeft = model.VesselIsRightToLeft;
                voyage.UpdatedById = currentUser.UserId;
                voyage.UpdatedDate = DateTime.UtcNow;
            }

            //if any asset has it's color mofidied we need to update them accordingly
            if (model.Items != null && model.Items.Any()) {
                var assetIds = model.Items.Select(x => x.AssetId).ToList();

                if (assetIds.Distinct().Count() != assetIds.Count)
                    throw new Exception("Duplicate AssetId spotted in AssetNewColors");

                //getting all the assets for voyagecargoes of voyage, to group the cargoes by them
                var assets = await _unitOfWork.Repository<VoyageCargo>()
                    .Query(x =>
                    x.VoyageId == voyageId &&
                    !x.IsBumped &&
                    !x.IsCancelled &&
                    x.Status == VoyageCargoStatus.Submitted &&
                    x.Voyage.VoyageStatus == VoyageStatus.Released &&
                    x.Voyage.VoyageDirection == VoyageDirection.Outbound &&
                    x.AssetId.HasValue &&
                    assetIds.Contains(x.AssetId.Value)
                    )
                    .Select(x => x.Asset)
                    .ToListAsync();

                //updating the colors
                foreach (var asset in assets) {
                    asset.Color = model.Items.Single(x => x.AssetId == asset.AssetId).AssetColor;
                    asset.UpdatedById = currentUser.UserId;
                    asset.UpdatedDate = DateTime.UtcNow;
                }
            }

            //commiting the changes
            await _unitOfWork.SaveChangesAsync();
        }

        public async Task<(byte[] file, string name)> GetBulkUploadTemplateFile(Guid voyageId) {
            var voyage = await _unitOfWork.Repository<Voyage>()
                .Query(v => v.VoyageId == voyageId)
                .AsNoTracking()
                .FirstOrDefaultAsync();

            if (voyage is null) throw new InvalidOperationException("The specified voyage cannot be found");

            var locationId = (await _userService.GetCurrentUser()).LocationId;

            var assets = await _unitOfWork.Repository<Asset>()
                .Query(a => a.AssetLocations.Any(l => l.LocationId == locationId))
                .AsNoTracking()
                .Select(a => a.Name)
                .ToArrayAsync();

            var ccuIds = await _unitOfWork.Repository<Cargo>()
                .Query(c => c.LocationId == locationId)
                .AsNoTracking()
                .Select(c => c.CCUId)
                .ToArrayAsync();

            var vendors = (await _vendorService.GetAllAsync(voyageId)).Select(v => v.VendorName).ToArray();

            var dgNames = await _unitOfWork.Repository<DangerousGood>()
                .Query(d => d.DangerousGoodLocations.Any(l => l.LocationId == locationId))
                .AsNoTracking()
                .Select(d => d.UnNo)
                .ToArrayAsync();

            var file = CargoListBulkUploadImporter.DownloadTemplateFile(
                assets, ccuIds, Enum.GetNames<RtRob>(), vendors,
                Enum.GetNames<VoyageCargoDeckLocation>(), Enum.GetNames<VoyageCargoCustomStatus>(),
                Enum.GetNames<VoyageCargoCustomsEntryType>(), Enum.GetNames<CargoWeightCategory>(),
                Enum.GetNames<VoyageCargoPriorityOrder>(), Enum.GetNames<VoyageCargoTransportRequest>(),
                dgNames, ["Yes", "No"], voyage.VoyageDirection == VoyageDirection.Outbound
            );
            return (file, "Cargo List_Template.xlsx");
        }

        public async Task<(byte[] file, string name, int uploadedRows)> ImportBulkUploadTemplateFile(Guid voyageId, IFormFile file, UserModel user) {
            using MemoryStream stream = new();
            await file.CopyToAsync(stream);

            var importer = new CargoListBulkUploadImporter(_timeZoneConversionService, stream.ToArray());
            var importData = importer.ImportTemplateFile();
            var location = await _locationService.GetLocationByIdAsync(user.LocationId.Value);

            if (string.IsNullOrEmpty(importData.ErrorCategory)) {
                if (importData.ImportedRows.Count == 0)
                    throw new InvalidOperationException("The specified template was empty");

                if (await _unitOfWork.Repository<Voyage>().FirstOrDefaultAsync(v => v.VoyageId == voyageId) is not Voyage voyage)
                    throw new InvalidOperationException("No voyage was found for the specified VoyageId");


                var vendorRepo = _unitOfWork.Repository<Vendor>();
                var assetRepo = _unitOfWork.Repository<Asset>();
                var dgRepo = _unitOfWork.Repository<DangerousGood>();
                var vcdgRepo = _unitOfWork.Repository<VoyageCargoDangerousGood>();
                var cargos = new List<VoyageCargo>(importData.ImportedRows.Count);

                var currentUserLocation = await _unitOfWork.Repository<Location>()
                                .Query(q => q.LocationId == user.LocationId)
                                .AsNoTracking()
                                .FirstOrDefaultAsync();

                var adhocCargoDescription = await _unitOfWork.Repository<CargoDescription>()
                                    .Query(q => q.IsAdhoc && !q.Deleted)
                                    .AsNoTracking()
                                    .IgnoreQueryFilters()
                                    .FirstOrDefaultAsync();

                if (adhocCargoDescription == null)
                {
                    //create the ad-hoc cargo description
                    adhocCargoDescription = await _unitOfWork.Repository<CargoDescription>().CreateAsync(new CargoDescription
                    {
                        Description = cargoDescriptionAdHoc,
                        CreatedDate = DateTime.UtcNow,
                        CreatedById = user.UserId,
                        UpdatedDate = DateTime.UtcNow,
                        UpdatedById = user.UserId,
                        Deleted = false,
                        Disabled = false,
                        IsAdhoc = true
                    });
                }

                foreach (CargoListBulkUploadModel row in importData.ImportedRows) {
                    var cargo = _mapper.Map<VoyageCargo>(row);
                    cargo.VoyageId = voyageId;
                    cargo.VendorId = (await vendorRepo.FirstOrDefaultAsync(v => v.VendorName == row.Vendor))?.VendorId;
                    cargo.AssetId = (await assetRepo.FirstOrDefaultAsync(a => a.Name == row.Asset))?.AssetId;
                    cargo.CargoWeightKg = _voyageCargoWeightUtility.ConvertWeight(cargo.CargoWeightKg, location.MeasurementUnit, false);

                    if (!string.IsNullOrWhiteSpace(row.CcuId))
                    {
                        var cargosByCcuId = await _unitOfWork.Repository<Cargo>()
                        .Query(q =>
                            q.LocationId == user.LocationId &&
                            q.CCUId.ToLower() == row.CcuId.ToLower() &&
                            !q.Deleted)
                        .IgnoreQueryFilters()
                        .ToListAsync();

                        if (cargosByCcuId.Count > 0)
                        {
                            if (cargosByCcuId.Count == 1)
                            {
                                var cargoEntity = cargosByCcuId.First();
                                cargo.CargoId = cargoEntity.CargoId;
                            }
                            else if (cargosByCcuId.Count > 1)
                            {
                                //this case when we find multiple cargos with the same CCUId under same Location
                                throw new Exception($"Multiple Cargos with the same CCUId {row.CcuId} for this Location exist in the system. " +
                                    $"Please select the Cargo needed from the Cargo Dropdown based on the Cargo Owner which is displayed in paranthesis.");
                            }
                        }
                        else
                        {
                            if (currentUserLocation.CanCreateOneOffCargo)
                            {
                                //this case is when we don't find a match in Cargo table based on the CCUID and Location. In this case we are dealing with an ad-hoc cargo that we need to create in Cargo table in database
                                await _unitOfWork.Repository<Cargo>().CreateAsync(new Cargo
                                {
                                    Deleted = false,
                                    IsAdhoc = true, //set IsAdhoc to true to differentiate from the other cargos.
                                    IsApproved = false,
                                    CreatedById = user.UserId,
                                    CreatedDate = DateTime.UtcNow,
                                    UpdatedById = user.UserId,
                                    UpdatedDate = DateTime.UtcNow,
                                    CCUId = row.CcuId,
                                    IsPool = false,
                                    VendorId = cargo.VendorId,
                                    LengthMm = cargo.CargoLengthMm ?? 0,
                                    WidthMm = cargo.CargoWidthMm ?? 0,
                                    HeightMm = 0,
                                    TareMassKg = 0,
                                    MaxGrossWeightKg = cargo.CargoWeightKg ?? 0,
                                    LocationId = user.LocationId,
                                    IsDeckCargo = false,
                                    CargoDescriptionId = adhocCargoDescription.CargoDescriptionId,
                                    CargoStatus = CargoStatus.Unkown,
                                    CcuHireStatus = CargoHireStatus.OffHire,
                                    Disabled = false
                                });
                            }
                            else
                            {
                                throw new Exception("You cannot create a Voyage Cargo with this CCUId because your Location does not allow creating ad-hoc Cargos.");
                            }
                        }
                    }

                    if (!string.IsNullOrEmpty(row.Dg)) {
                        if (await dgRepo.FirstOrDefaultAsync(d => d.UnNo == row.Dg) is DangerousGood dg) {
                            var vcdg = new VoyageCargoDangerousGood {
                                DangerousGoodId = dg.DangerousGoodId,
                                LtdQty = false,
                                MarinePollutant = false,
                                VoyageCargo = cargo
                            };
                            await vcdgRepo.CreateAsync(vcdg);
                        }
                    }

                    cargos.Add(cargo);
                }
                await BatchCreateAsync(cargos, user);
                return ([], "Cargo List_Template.xlsx", importData.ImportedRows.Count);
            } else
                return (importData.ValidationFile, $"Cargo List_Template({importData.ErrorCategory}).xlsx", 0);
        }

        private double ConvertAndRound(double? mmValue) => mmValue.HasValue ? Math.Round(Length.FromMillimeters((double)mmValue.Value).Feet, 2) : 0;

        private void SquaresOverlapping(List<VoyageCargo> voyageCargos, Vessel vessel) {
            for (int i = 0; i < voyageCargos.Count; i++) {
                var currentCargo = voyageCargos[i];

                var doesXVesselPositionExist = ConvertAndRound(currentCargo.YVesselPosition) < 0;
                var doesYVesselPositionExist = ConvertAndRound(currentCargo.XVesselPosition) < 0;

                var (deckLength, deckWidth) = vessel.GetDeckDimensioninMillimeters();
                var isCargoWidthLargerThanDeck = ConvertAndRound(currentCargo.YVesselPosition + currentCargo.CargoWidthMm) > ConvertAndRound(deckWidth);
                var isCargoLengthLargerThanDeck = ConvertAndRound(currentCargo.XVesselPosition + currentCargo.CargoLengthMm) > ConvertAndRound(deckLength);

                if (doesXVesselPositionExist || doesYVesselPositionExist || isCargoWidthLargerThanDeck || isCargoLengthLargerThanDeck)
                    throw new Exception($"VoyageCargo {currentCargo.VoyageCargoId} is outside the bounds of vessel");

                for (int j = i + 1; j < voyageCargos.Count; j++) {
                    if (DoCargoesOverlap(currentCargo, voyageCargos[j])) {
                        throw new Exception($"VoyageCargo {currentCargo.VoyageCargoId} overlaps with VoyageCargo {voyageCargos[j].VoyageCargoId}.");
                    }
                }
            }
        }

        private bool DoCargoesOverlap(VoyageCargo cargo1, VoyageCargo cargo2) {
            var left1 = cargo1.XVesselPosition;
            var right1 = cargo1.XVesselPosition + cargo1.CargoLengthMm;
            var bottom1 = cargo1.YVesselPosition;
            var top1 = cargo1.YVesselPosition + cargo1.CargoWidthMm;

            var left2 = cargo2.XVesselPosition;
            var right2 = cargo2.XVesselPosition + cargo2.CargoLengthMm;
            var bottom2 = cargo2.YVesselPosition;
            var top2 = cargo2.YVesselPosition + cargo2.CargoWidthMm;

            bool noOverlap = right1 <= left2 ||
                             left1 >= right2 ||
                             bottom1 <= top2 ||
                             top1 >= bottom2;

            return !noOverlap;
        }

        public async Task<List<VoyageCargoQueryModel>> VoyageCargoOperationsFiltersAsync(OperationsVoyageCargoFilterModel filterModel, UserModel user) {
            var baseQuery = _unitOfWork.Repository<VoyageCargo>()
                .Query(x => x.Status == VoyageCargoStatus.Submitted);

            var query = baseQuery
                .AsNoTracking()
                .AsSplitQuery()
                .Include(x => x.Voyage)
                .Include(x => x.VoyageCargoLifts)
                .Include(x => x.VoyageCargoDangerousGoods)
                    .ThenInclude(x => x.DangerousGood)
                .Where(x => x.Voyage.LocationId == filterModel.LocationId || (x.VoyageId == null && x.Site.LocationId == filterModel.LocationId));

            if (!string.IsNullOrEmpty(filterModel.Search)) {
                query = query.Where(x =>
                    x.CargoDescription.Contains(filterModel.Search) ||
                    x.CcuId.Contains(filterModel.Search) ||
                    x.PickupAddress.Contains(filterModel.Search));
            }

            if (filterModel.Page == OperationsVoyageCargoPages.EquipmentArrival || filterModel.Page == OperationsVoyageCargoPages.CustomsControl) {

                if (filterModel.From.HasValue) {
                    query = query.Where(x => x.Voyage.SailingDischargeDate >= filterModel.From.Value);
                }

                if (filterModel.To.HasValue) {
                    query = query.Where(x => x.Voyage.SailingDischargeDate <= filterModel.To.Value);
                }

                if (filterModel.AssetIds?.Any() == true) {
                    query = query.Where(x => filterModel.AssetIds.Contains(x.AssetId));
                }

                if (filterModel.Page == OperationsVoyageCargoPages.EquipmentArrival) {
                    if (!filterModel.IncludeAlreadyArrived) {
                        query = query.Where(x => x.ArrivalTime == null);
                    }
                    query = query.Where(x => x.Voyage.VoyageDirection == VoyageDirection.Outbound);

                    query = query.Where(x =>
                        (x.VoyageId == null) ||
                        (
                            x.Voyage.VoyageStatus == VoyageStatus.Submitted ||
                            x.Voyage.VoyageStatus == VoyageStatus.Released ||
                            x.Voyage.VoyageStatus == VoyageStatus.Discharged
                        ) &&
                        !x.RtRob.HasValue &&
                        (!x.IsBumped || (x.IsBumped && x.BumpedOrCancelledOrRTDate > x.ArrivalTime)) &&
                        !x.IsCancelled &&
                        (!x.IsMoved || (x.IsMoved && x.MovedDate > x.ArrivalTime)));
                }

                if (filterModel.Page == OperationsVoyageCargoPages.CustomsControl) {
                    query = query.Where(x => x.Voyage.VoyageDirection == filterModel.VoyageDirection);

                    if (!filterModel.AlreadyCustomCleared) {
                        query = query
                            .Where(x => x.CustomsApprovedToLoad == VoyageCargoCustomsApprovedToLoad.NotApplicable || x.CustomsApprovedToLoad == null);
                    }

                    query = query.Where(x =>
                        x.CustomsEntryType == VoyageCargoCustomsEntryType.Full ||
                        x.CustomsEntryType == VoyageCargoCustomsEntryType.Mixed);
                }
            } else if (filterModel.Page == OperationsVoyageCargoPages.EquipmentDispatch) {

                query = query.Where(x => x.VoyageId == null || x.Voyage.VoyageDirection == VoyageDirection.Inbound);

                if (filterModel.From.HasValue) {
                    query = query.Where(x =>
                        (x.VoyageId == null && x.CreatedDate >= filterModel.From.Value)
                        ||
                        (x.VoyageCargoLifts.Any() && x.CompletedLift
                            ? x.VoyageCargoLifts.Min(l => (DateTime?)l.CreatedDate)
                            : x.Voyage.SailingDischargeDate) >= filterModel.From.Value);
                }

                if (filterModel.To.HasValue) {
                    query = query.Where(x =>
                        (x.VoyageId == null && x.CreatedDate <= filterModel.To.Value)
                        ||
                        (x.VoyageCargoLifts.Any() && x.CompletedLift
                            ? x.VoyageCargoLifts.Min(l => (DateTime?)l.CreatedDate)
                            : x.Voyage.SailingDischargeDate) <= filterModel.To.Value);
                }



                if (filterModel.CargoBackloadStatuses?.Any() == true) {
                    query = query.Where(x => x.CargoBackloadStatus.HasValue &&
                                             filterModel.CargoBackloadStatuses.Contains(x.CargoBackloadStatus.Value));
                }

                if (filterModel.VoyageCargoTransportStatuses?.Any() == true) {
                    query = query.Where(x => x.TransportStatus.HasValue &&
                                             filterModel.VoyageCargoTransportStatuses.Contains(x.TransportStatus.Value));
                }

                if (filterModel.VoyageCargoTransportRequests?.Any() == true) {
                    query = query.Where(x => x.TransportRequest.HasValue &&
                                             filterModel.VoyageCargoTransportRequests.Contains(x.TransportRequest.Value));
                }

                if (filterModel.AssetIds?.Any() == true) {
                    query = query.Where(x => filterModel.AssetIds.Contains(x.AssetId));
                }

                if (filterModel.ClientIds?.Any() == true) {
                    query = query.Where(x => x.Voyage.ClientId.HasValue &&
                                             filterModel.ClientIds.Contains(x.Voyage.ClientId.Value));
                }

                if (filterModel.VesselIds?.Any() == true) {
                    query = query.Where(x => filterModel.VesselIds.Contains(x.Voyage.VesselId));
                }

                if (filterModel.VendorIds?.Any() == true) {
                    query = query.Where(x => x.VendorId.HasValue &&
                                             filterModel.VendorIds.Contains(x.VendorId.Value));
                }

                if (filterModel.HighPriority) {
                    query = query.Where(x => x.IsHighPriority);
                }

                if (filterModel.DangerousGoods) {
                    query = query.Where(x => x.VoyageCargoDangerousGoods.Any());
                }

                query = query.Where(x =>
                    (x.VoyageId == null)
                    || (
                        x.Voyage.VoyageStatus == VoyageStatus.Submitted ||
                        x.Voyage.VoyageStatus == VoyageStatus.Released ||
                        x.Voyage.VoyageStatus == VoyageStatus.Discharged || 
                        x.Voyage.VoyageStatus == VoyageStatus.Complete
                    ) &&
                    x.RtRob != RtRob.RemainingOnBoard && x.RtRob != RtRob.RoundTrip &&
                    !x.IsBumped &&
                    !x.IsCancelled &&
                    !x.IsMoved);
            }



            var measurementUnit = Enum.Parse<LocationMeasurementUnit>(user.MeasurementUnit, ignoreCase: true);

            var voyageCargoes = await query.Select(x => new VoyageCargoQueryModel {
                VoyageCargoId = x.VoyageCargoId,
                VoyageCargoParentId = x.VoyageCargoParentId,
                VoyageCargoLoadId = x.VoyageCargoLoadId,
                ArrivalTime = x.ArrivalTime,
                AssetId = x.AssetId,
                CargoDescription = x.CargoDescription,
                CargoId = x.CargoId,
                CargoNextTestDate = x.CargoNextTestDate,
                CargoLengthFt = x.CargoLengthFt,
                CargoUnitType = x.CargoUnitType,
                CargoWeight = x.CargoWeightKg.HasValue
                    ? (x.Voyage.Location.MeasurementUnit == LocationMeasurementUnit.Tonne
                        ? x.CargoWeightKg / 1000
                        : x.CargoWeightKg)
                    : null,
                ActualWeight = measurementUnit == LocationMeasurementUnit.Tonne
                                ? x.ActualWeight / 1000
                                : x.ActualWeight,
                OriginalActualWeight = measurementUnit == LocationMeasurementUnit.Tonne
                                ? x.OriginalActualWeight / 1000
                                : x.OriginalActualWeight,
                MeasurementUnit = measurementUnit.ToString(),
                CollectDate = x.CollectDate,
                CollectTime = x.CollectTime,
                CompletedLift = x.CompletedLift,
                CargoWidthFt = x.CargoWidthFt,
                CcuId = x.CcuId,
                TrailerId = x.TrailerId,
                CreatedDate = x.CreatedDate,
                Comments = x.Comments,
                IsHighPriority = x.IsHighPriority,
                IsCancelled = x.IsCancelled,
                Level = x.Level,
                IsBumped = x.IsBumped,
                CustomReferenceNo = x.CustomReferenceNo,
                NumberOfLifts = x.NumberOfLifts,
                Owner = x.Owner,
                PassedInspection = x.PassedInspection,
                PickupAddress = x.PickupAddress,
                Quantity = x.Quantity,
                OriginalQuantity = x.OriginalQuantity,
                RowNumber = x.RowNumber,
                RtRob = x.RtRob,
                Status = !x.Status.HasValue ? null :
                x.Status == VoyageCargoStatus.Draft ? 0 :
                x.Status == VoyageCargoStatus.Submitted ? 1 : 2,
                TransportName = x.TransportName,
                TransportAddress = x.TransportAddress,
                TransportRequest = !x.TransportRequest.HasValue ? null :
                          x.TransportRequest == VoyageCargoTransportRequest.Collection ? 0 :
                          x.TransportRequest == VoyageCargoTransportRequest.VendorDelivery ? 1 :
                          x.TransportRequest == VoyageCargoTransportRequest.RemainingOnBoard ? 2 : 3,
                UpdatedDate = x.UpdatedDate,
                VendorId = x.VendorId,
                VendorWarehouseId = x.VendorWarehouseId,
                ViaVendorId = x.ViaVendorId,
                ViaVendorWarehouseId = x.ViaVendorWarehouseId,
                DistrictId = x.DistrictId,
                VoyageId = x.VoyageId,
                TransportPhoneNumber = x.TransportPhoneNumber,
                VoyageDirection = x.Voyage.VoyageDirection == VoyageDirection.Inbound ? 0 :
                      x.Voyage.VoyageDirection == VoyageDirection.Outbound ? 1 : 0,
                VendorName = x.Vendor.VendorName,
                VoyageSailingDischargeDate = x.Voyage.SailingDischargeDate,
                VoyageVesselId = x.Voyage.Vessel.VesselId,
                VoyageVesselName = x.Voyage.Vessel.Name,
                DangerousGoodCount = x.VoyageCargoDangerousGoods.Count,
                AssetName = x.Asset.Name,
                VoyageOffshoreLocationIds = x.Voyage.OffshoreLocations.Select(x => x.AssetId),
                VoyageStatus = !x.Voyage.VoyageStatus.HasValue ? null :
                    x.Voyage.VoyageStatus == VoyageStatus.Draft ? 0 :
                    x.Voyage.VoyageStatus == VoyageStatus.Submitted ? 1 :
                    x.Voyage.VoyageStatus == VoyageStatus.Released ? 2 :
                    x.Voyage.VoyageStatus == VoyageStatus.Loaded ? 3 :
                    x.Voyage.VoyageStatus == VoyageStatus.Discharged ? 4 : 5,
                CargoStatus = x.CargoId == null ? null :
                    x.Cargo.CargoStatus == CargoStatus.InStock ? 0 :
                    x.Cargo.CargoStatus == CargoStatus.UnavailableInspection ? 1 :
                    x.Cargo.CargoStatus == CargoStatus.UnavailableRepair ? 2 :
                    x.Cargo.CargoStatus == CargoStatus.UnavailableRejected ? 3 : 4,
                VoyageLocationId = x.Voyage.LocationId,
                VoyageOwnerId = x.VoyageId.HasValue ? x.Voyage.ClientId : x.ClientId,
                VoyageOwnerName = x.VoyageId.HasValue ? x.Voyage.Client.Name : x.Client.Name,
                CargoCCUId = x.Cargo.CCUId,
                TrailerNumber = x.Trailer != null ? x.Trailer.RegistrationNumber : x.TrailerNumber,
                TransportStatus = x.TransportStatus == null ? null :
                      x.TransportStatus == VoyageCargoTransportStatus.OnHold ? 0 :
                      x.TransportStatus == VoyageCargoTransportStatus.OnHoldCustoms ? 1 : 2,
                VoyageNumber = x.Voyage.VoyageNumber,
                CustomsEntryType = !x.CustomsEntryType.HasValue ? null :
                            x.CustomsEntryType == VoyageCargoCustomsEntryType.None ? 0 :
                            x.CustomsEntryType == VoyageCargoCustomsEntryType.EIDR ? 1 :
                            x.CustomsEntryType == VoyageCargoCustomsEntryType.EIDR_AEO ? 2 :
                            x.CustomsEntryType == VoyageCargoCustomsEntryType.EMPTY ? 3 :
                            x.CustomsEntryType == VoyageCargoCustomsEntryType.Full ? 4 :
                            x.CustomsEntryType == VoyageCargoCustomsEntryType.Mixed ? 5 :
                            x.CustomsEntryType == VoyageCargoCustomsEntryType.ROB_RT_BOATSKIP ? 6 :
                            x.CustomsEntryType == VoyageCargoCustomsEntryType.SDBC ? 7 : 8,
                CustomsApprovedToLoad = x.CustomsApprovedToLoad == null ? null :
                            x.CustomsApprovedToLoad == VoyageCargoCustomsApprovedToLoad.NotApplicable ? 0 : 1,
                IsCargoApproved = x.Cargo != null && x.Cargo.IsApproved,
                IsOwnerCustomsCompliant = x.Voyage.Client != null && x.Voyage.Client.CustomsCompliant,
                DispatchTime = x.DispatchTime,
                CanceledBumped = x.IsBumped || x.IsCancelled,
                LoadingDate = x.VoyageId.HasValue ? x.VoyageCargoLifts.Min(x => (DateTime?)x.CreatedDate) : x.CreatedDate,
                CargoBackloadStatus = x.CargoBackloadStatus == null ? null :
                          x.CargoBackloadStatus == CargoBackloadStatus.ToBeDischarged ? 0 :
                          x.CargoBackloadStatus == CargoBackloadStatus.Discharged ? 1 :
                          x.CargoBackloadStatus == CargoBackloadStatus.Dispatched ? 2 : 3,
                DistrictName = x.District.DistrictName,
                SiteId = x.VoyageId.HasValue ? x.Voyage.SiteId : x.SiteId,
                ClientId = x.VoyageId.HasValue ? x.Voyage.ClientId : x.ClientId,
                SiteName = x.VoyageId.HasValue ? x.Voyage.Site.Name : x.Site.Name,
                SepaNumber = x.SepaNumber,
                DangerousGoods = _mapper.Map<List<VoyageCargoDangerousGoodModel>>(x.VoyageCargoDangerousGoods),
                OverdueTime = null,
                BusinessDayFrom = x.VoyageId.HasValue ? x.Voyage.Location.BusinessDaysFrom : x.Site.Location.BusinessDaysFrom,
                BusinessDayTo = x.VoyageId.HasValue ? x.Voyage.Location.BusinessDaysTo : x.Site.Location.BusinessDaysTo,
                BusinessHoursFrom = x.VoyageId.HasValue ? x.Voyage.Location.BusinessHoursFrom : x.Site.Location.BusinessHoursFrom,
                BusinessHoursTo = x.VoyageId.HasValue ? x.Voyage.Location.BusinessHoursTo : x.Site.Location.BusinessHoursTo,
                ChildCargos = new List<VoyageCargoQueryModel>()
            }).ToListAsync();

            var parentDict = voyageCargoes.ToDictionary(x => x.VoyageCargoId);

            if (filterModel.Page == OperationsVoyageCargoPages.EquipmentDispatch) {
                var childCargoes = new List<VoyageCargoQueryModel>();
                var parentIds = voyageCargoes.Select(p => p.VoyageCargoId).ToList();
                var childQuery = _unitOfWork.Repository<VoyageCargo>()
                    .Query(x => x.Status == VoyageCargoStatus.Submitted &&
                                x.VoyageCargoParentId.HasValue &&
                                parentIds.Contains(x.VoyageCargoParentId.Value))
                    .AsNoTracking();

                childCargoes = await childQuery.Select(x => new VoyageCargoQueryModel {
                    VoyageCargoId = x.VoyageCargoId,
                    VoyageCargoParentId = x.VoyageCargoParentId,
                    VoyageCargoLoadId = x.VoyageCargoLoadId,
                    ArrivalTime = x.ArrivalTime,
                    AssetId = x.AssetId,
                    CargoDescription = x.CargoDescription,
                    CargoId = x.CargoId,
                    CargoNextTestDate = x.CargoNextTestDate,
                    CargoLengthFt = x.CargoLengthFt,
                    CargoUnitType = x.CargoUnitType,
                    CargoWeight = x.CargoWeightKg.HasValue
                     ? (measurementUnit == LocationMeasurementUnit.Tonne
                         ? x.CargoWeightKg / 1000
                         : x.CargoWeightKg)
                     : null,
                    ActualWeight = measurementUnit == LocationMeasurementUnit.Tonne
                                 ? x.ActualWeight / 1000
                                 : x.ActualWeight,
                    OriginalActualWeight = measurementUnit == LocationMeasurementUnit.Tonne
                                 ? x.OriginalActualWeight / 1000
                                 : x.OriginalActualWeight,
                    MeasurementUnit = measurementUnit.ToString(),
                    CollectDate = x.CollectDate,
                    CollectTime = x.CollectTime,
                    CompletedLift = x.CompletedLift,
                    CargoWidthFt = x.CargoWidthFt,
                    CcuId = x.CcuId,
                    TrailerId = x.TrailerId,
                    CreatedDate = x.CreatedDate,
                    Comments = x.Comments,
                    IsHighPriority = x.IsHighPriority,
                    IsCancelled = x.IsCancelled,
                    Level = x.Level,
                    IsBumped = x.IsBumped,
                    CustomReferenceNo = x.CustomReferenceNo,
                    NumberOfLifts = x.NumberOfLifts,
                    Owner = x.Owner,
                    PassedInspection = x.PassedInspection,
                    PickupAddress = x.PickupAddress,
                    Quantity = x.Quantity,
                    OriginalQuantity = x.OriginalQuantity,
                    RowNumber = x.RowNumber,
                    RtRob = x.RtRob,
                    Status = !x.Status.HasValue ? null :
                 x.Status == VoyageCargoStatus.Draft ? 0 :
                 x.Status == VoyageCargoStatus.Submitted ? 1 : 2,
                    TransportName = x.TransportName,
                    TransportAddress = x.TransportAddress,
                    TransportRequest = !x.TransportRequest.HasValue ? null :
                           x.TransportRequest == VoyageCargoTransportRequest.Collection ? 0 :
                           x.TransportRequest == VoyageCargoTransportRequest.VendorDelivery ? 1 :
                           x.TransportRequest == VoyageCargoTransportRequest.RemainingOnBoard ? 2 : 3,
                    UpdatedDate = x.UpdatedDate,
                    VendorId = x.VendorId,
                    VendorWarehouseId = x.VendorWarehouseId,
                    ViaVendorId = x.ViaVendorId,
                    ViaVendorWarehouseId = x.ViaVendorWarehouseId,
                    DistrictId = x.DistrictId,
                    VoyageId = x.VoyageId,
                    TransportPhoneNumber = x.TransportPhoneNumber,
                    VoyageDirection = x.Voyage.VoyageDirection == VoyageDirection.Inbound ? 0 :
                       x.Voyage.VoyageDirection == VoyageDirection.Outbound ? 1 : 0,
                    VendorName = x.Vendor.VendorName,
                    VoyageSailingDischargeDate = x.Voyage.SailingDischargeDate,
                    VoyageVesselId = x.Voyage.Vessel.VesselId,
                    VoyageVesselName = x.Voyage.Vessel.Name,
                    DangerousGoodCount = x.VoyageCargoDangerousGoods.Count,
                    AssetName = x.Asset.Name,
                    VoyageOffshoreLocationIds = x.Voyage.OffshoreLocations.Select(x => x.AssetId),
                    VoyageStatus = !x.Voyage.VoyageStatus.HasValue ? null :
                     x.Voyage.VoyageStatus == VoyageStatus.Draft ? 0 :
                     x.Voyage.VoyageStatus == VoyageStatus.Submitted ? 1 :
                     x.Voyage.VoyageStatus == VoyageStatus.Released ? 2 :
                     x.Voyage.VoyageStatus == VoyageStatus.Loaded ? 3 :
                     x.Voyage.VoyageStatus == VoyageStatus.Discharged ? 4 : 5,
                    CargoStatus = x.CargoId == null ? null :
                     x.Cargo.CargoStatus == CargoStatus.InStock ? 0 :
                     x.Cargo.CargoStatus == CargoStatus.UnavailableInspection ? 1 :
                     x.Cargo.CargoStatus == CargoStatus.UnavailableRepair ? 2 :
                     x.Cargo.CargoStatus == CargoStatus.UnavailableRejected ? 3 : 4,
                    VoyageLocationId = x.Voyage.LocationId,
                    VoyageOwnerId = x.VoyageId.HasValue ? x.Voyage.ClientId : x.ClientId,
                    VoyageOwnerName = x.VoyageId.HasValue ? x.Voyage.Client.Name : x.Client.Name,
                    CargoCCUId = x.Cargo.CCUId,
                    TrailerNumber = x.Trailer != null ? x.Trailer.RegistrationNumber : x.TrailerNumber,
                    TransportStatus = x.TransportStatus == null ? null :
                       x.TransportStatus == VoyageCargoTransportStatus.OnHold ? 0 :
                       x.TransportStatus == VoyageCargoTransportStatus.OnHoldCustoms ? 1 : 2,
                    VoyageNumber = x.Voyage.VoyageNumber,
                    CustomsEntryType = !x.CustomsEntryType.HasValue ? null :
                             x.CustomsEntryType == VoyageCargoCustomsEntryType.None ? 0 :
                             x.CustomsEntryType == VoyageCargoCustomsEntryType.EIDR ? 1 :
                             x.CustomsEntryType == VoyageCargoCustomsEntryType.EIDR_AEO ? 2 :
                             x.CustomsEntryType == VoyageCargoCustomsEntryType.EMPTY ? 3 :
                             x.CustomsEntryType == VoyageCargoCustomsEntryType.Full ? 4 :
                             x.CustomsEntryType == VoyageCargoCustomsEntryType.Mixed ? 5 :
                             x.CustomsEntryType == VoyageCargoCustomsEntryType.ROB_RT_BOATSKIP ? 6 :
                             x.CustomsEntryType == VoyageCargoCustomsEntryType.SDBC ? 7 : 8,
                    CustomsApprovedToLoad = x.CustomsApprovedToLoad == null ? null :
                             x.CustomsApprovedToLoad == VoyageCargoCustomsApprovedToLoad.NotApplicable ? 0 : 1,
                    IsCargoApproved = x.Cargo != null && x.Cargo.IsApproved,
                    IsOwnerCustomsCompliant = x.Voyage.Client != null && x.Voyage.Client.CustomsCompliant,
                    DispatchTime = x.DispatchTime.HasValue ? x.DispatchTime : null,
                    CanceledBumped = x.IsBumped || x.IsCancelled,
                    CargoBackloadStatus = x.CargoBackloadStatus == null ? null :
                           x.CargoBackloadStatus == CargoBackloadStatus.ToBeDischarged ? 0 :
                           x.CargoBackloadStatus == CargoBackloadStatus.Discharged ? 1 :
                           x.CargoBackloadStatus == CargoBackloadStatus.Dispatched ? 2 : 3,
                    DistrictName = x.District.DistrictName,
                    SiteId = x.VoyageId.HasValue ? x.Voyage.SiteId : x.SiteId,
                    ClientId = x.VoyageId.HasValue ? x.Voyage.ClientId : x.ClientId,
                    SiteName = x.VoyageId.HasValue ? x.Voyage.Site.Name : x.Site.Name,
                    SepaNumber = x.SepaNumber,
                    OverdueTime = null,
                    BusinessDayFrom = x.VoyageId.HasValue ? x.Voyage.Location.BusinessDaysFrom : x.Site.Location.BusinessDaysFrom,
                    BusinessDayTo = x.VoyageId.HasValue ? x.Voyage.Location.BusinessDaysTo : x.Site.Location.BusinessDaysTo,
                    BusinessHoursFrom = x.VoyageId.HasValue ? x.Voyage.Location.BusinessHoursFrom : x.Site.Location.BusinessHoursFrom,
                    BusinessHoursTo = x.VoyageId.HasValue ? x.Voyage.Location.BusinessHoursTo : x.Site.Location.BusinessHoursTo,
                    TimeZone = x.VoyageId.HasValue ? x.Voyage.Location.TimeZoneInfoId : x.Site.Location.TimeZoneInfoId,
                }).ToListAsync();

                var voyageCargoIds = new HashSet<Guid>(voyageCargoes.Select(x => x.VoyageCargoId));
                var childCargoIdToParentId = childCargoes
                    .Where(x => x.VoyageCargoParentId.HasValue)
                    .ToDictionary(x => x.VoyageCargoId, x => x.VoyageCargoParentId.Value);

                voyageCargoes = voyageCargoes
                    .Where(vc => {
                        if (!childCargoIdToParentId.ContainsKey(vc.VoyageCargoId))
                            return true;
                        var parentId = childCargoIdToParentId[vc.VoyageCargoId];
                        return !voyageCargoIds.Contains(parentId);
                    })
                    .ToList();


                var parentCargoes = new List<VoyageCargoQueryModel>();

                foreach (var voyageCargo in voyageCargoes) {
                    if (voyageCargo.VoyageCargoParentId == null &&
                        voyageCargo.LoadingDate.HasValue &&
                        voyageCargo.CargoBackloadStatus == 1 &&
                        (voyageCargo.CompletedLift || !voyageCargo.VoyageId.HasValue)) {
                        voyageCargo.OverdueTime = CalculateOverdueTime(voyageCargo.LoadingDate,
                            DateTime.UtcNow,
                            voyageCargo.BusinessDayFrom,
                            voyageCargo.BusinessDayTo,
                            voyageCargo.BusinessHoursFrom,
                            voyageCargo.BusinessHoursTo);
                    }
                    if (voyageCargo.VoyageCargoParentId != null) {
                        var parentCargo = await _unitOfWork.Repository<VoyageCargo>()
                            .Query(x => x.VoyageCargoId == voyageCargo.VoyageCargoParentId)
                            .Select(x => new VoyageCargoQueryModel {
                                VoyageCargoId = x.VoyageCargoId,
                                LoadingDate = x.VoyageId.HasValue ? x.VoyageCargoLifts.Min(x => (DateTime?)x.CreatedDate) : x.CreatedDate,
                                DangerousGoods = _mapper.Map<List<VoyageCargoDangerousGoodModel>>(x.VoyageCargoDangerousGoods),
                                DangerousGoodCount = x.VoyageCargoDangerousGoods.Count,
                                CompletedLift = x.CompletedLift,
                                NumberOfLifts = x.NumberOfLifts,
                                BusinessDayFrom = x.VoyageId.HasValue ? x.Voyage.Location.BusinessDaysFrom : x.Site.Location.BusinessDaysFrom,
                                BusinessDayTo = x.VoyageId.HasValue ? x.Voyage.Location.BusinessDaysTo : x.Site.Location.BusinessDaysTo,
                                BusinessHoursFrom = x.VoyageId.HasValue ? x.Voyage.Location.BusinessHoursFrom : x.Site.Location.BusinessHoursFrom,
                                BusinessHoursTo = x.VoyageId.HasValue ? x.Voyage.Location.BusinessHoursTo : x.Site.Location.BusinessHoursTo,
                            })
                            .FirstOrDefaultAsync();

                        parentCargo.OverdueTime = CalculateOverdueTime(
                            parentCargo.LoadingDate,
                            DateTime.UtcNow,
                            parentCargo.BusinessDayFrom,
                            parentCargo.BusinessDayTo,
                            parentCargo.BusinessHoursFrom,
                            parentCargo.BusinessHoursTo);

                        voyageCargo.DangerousGoods = parentCargo.DangerousGoods;
                        voyageCargo.DangerousGoodCount = parentCargo.DangerousGoodCount;
                        voyageCargo.LoadingDate = parentCargo.LoadingDate;
                        voyageCargo.NumberOfLifts = parentCargo.NumberOfLifts;
                        voyageCargo.CompletedLift = parentCargo.CompletedLift;
                        voyageCargo.OverdueTime = parentCargo.OverdueTime;

                    }

                    voyageCargo.TransportStatus ??= (int)(voyageCargo.CustomsCleared ? VoyageCargoTransportStatus.Dispatchable : VoyageCargoTransportStatus.OnHoldCustoms);
                }
                foreach (var child in childCargoes) {
                    if (child.VoyageCargoParentId.HasValue &&
                        parentDict.TryGetValue(child.VoyageCargoParentId.Value, out var parent)) {
                        child.DangerousGoods = parent.DangerousGoods;
                        child.DangerousGoodCount = parent.DangerousGoodCount;
                        child.LoadingDate = parent.LoadingDate;
                        child.NumberOfLifts = parent.NumberOfLifts;
                        child.CompletedLift = parent.CompletedLift;
                        child.OverdueTime = parent.OverdueTime;
                        parent.ChildCargos.Add(child);
                    }
                }

                if (filterModel.OverdueTimeFrom.HasValue) {
                    voyageCargoes = voyageCargoes.Where(x => x.OverdueTime > 0
                                                             && x.OverdueTime >= filterModel.OverdueTimeFrom).ToList();
                }

                if (filterModel.OverdueTimeTo.HasValue) {
                    voyageCargoes = voyageCargoes.Where(x => x.OverdueTime > 0
                                                             && x.OverdueTime <= filterModel.OverdueTimeTo).ToList();
                }

                if (string.Equals(filterModel.OrderBy, "ASC", StringComparison.OrdinalIgnoreCase)) {
                    voyageCargoes = voyageCargoes
                        .OrderBy(x => x.EquipmentDispatchSortDate.HasValue ? x.EquipmentDispatchSortDate : x.CreatedDate)
                        .ToList();
                } else {
                    voyageCargoes = voyageCargoes
                        .OrderByDescending(x => x.EquipmentDispatchSortDate.HasValue ? x.EquipmentDispatchSortDate : x.CreatedDate)
                        .ToList();
                }
            } else {
                voyageCargoes = string.Equals(filterModel.OrderBy, "ASC", StringComparison.OrdinalIgnoreCase)
                    ? voyageCargoes.OrderBy(x => x.VoyageSailingDischargeDate).ToList()
                    : voyageCargoes.OrderByDescending(x => x.VoyageSailingDischargeDate).ToList();
            }

            return voyageCargoes;
        }

        public async Task<List<VoyageCargoQueryModel>> GetVoyageCargoesMatchingSepaAndSiteAsync(Guid locationId, List<VoyageCargoModel> model) {
            var siteSepaPairs = model
                .Where(x => x.SiteId != null && !string.IsNullOrEmpty(x.SepaNumber))
                .Select(x => new { x.SiteId, x.SepaNumber })
                .Distinct()
                .ToList();

            if (!siteSepaPairs.Any())
                return new List<VoyageCargoQueryModel>();

            var voyageCargoes = await _unitOfWork.Repository<VoyageCargo>()
                .Query()
                .AsSplitQuery()
                .AsNoTracking()
                .Include(x => x.Voyage)
                .Include(x => x.Vendor)
                .Include(x => x.VoyageCargoLifts)
                .Where(vc =>
                    vc.Status == VoyageCargoStatus.Submitted &&
                    (vc.VoyageId == null ||
                        (vc.Voyage.VoyageDirection == VoyageDirection.Inbound &&
                         (vc.Voyage.VoyageStatus == VoyageStatus.Submitted ||
                          vc.Voyage.VoyageStatus == VoyageStatus.Released ||
                          vc.Voyage.VoyageStatus == VoyageStatus.Discharged))) &&
                    vc.RtRob != RtRob.RemainingOnBoard &&
                    vc.RtRob != RtRob.RoundTrip &&
                    !vc.IsBumped &&
                    !vc.IsCancelled &&
                    !vc.IsMoved &&
                    vc.CargoBackloadStatus == CargoBackloadStatus.Discharged &&
                    (
                        (vc.Voyage != null && vc.Voyage.LocationId == locationId) ||
                        (vc.Voyage == null && vc.Site.LocationId == locationId)
                    ) &&
                    !model.Select(x => x.VoyageCargoId).Contains(vc.VoyageCargoId)
                ).ToListAsync();


            var matchingCargoes = new List<VoyageCargo>();

            if (voyageCargoes.Count > 0) {
                matchingCargoes = voyageCargoes
                    .Where(vc =>
                        siteSepaPairs.Any(pair =>
                            vc.Voyage != null
                                ? pair.SiteId == vc.Voyage.SiteId && pair.SepaNumber == vc.SepaNumber
                                : pair.SiteId == vc.SiteId && pair.SepaNumber == vc.SepaNumber))
                    .ToList();
            }

            var matchingCargoesMapped = matchingCargoes.Select(x => new VoyageCargoQueryModel {
                VoyageCargoId = x.VoyageCargoId,
                VoyageId = x.VoyageId,
                VoyageCargoParentId = x.VoyageCargoParentId,
                CcuId = x.CcuId,
                CargoDescription = x.CargoDescription,
                Quantity = x.Quantity,
                ActualWeight = x.ActualWeight,
                VendorName = x.Vendor != null ? x.Vendor.VendorName : null,
                TrailerNumber = x.TrailerNumber,
                SepaNumber = x.SepaNumber,
            }).ToList();

            return matchingCargoesMapped;
        }

        public double? CalculateOverdueTime(DateTime? discharageDate, DateTime currentDate, DayOfWeek businessDaysFrom, DayOfWeek businessDaysTo, string businessHoursFrom, string businessHoursTo) {
            if (!discharageDate.HasValue) {
                return null;
            }

            var businessHoursStart = TimeSpan.Parse(businessHoursFrom);
            var businessHoursEnd = TimeSpan.Parse(businessHoursTo);

            currentDate = AdjustToBusinessHours(currentDate, businessHoursStart, businessHoursEnd);
            discharageDate = AdjustToBusinessHours(discharageDate.Value, businessHoursStart, businessHoursEnd);

            if (currentDate <= discharageDate) {
                return null;
            }

            double overdueTimeInHours = 0;

            while (discharageDate < currentDate) {
                if (IsBusinessDay(discharageDate.Value, businessDaysFrom, businessDaysTo)) {
                    DateTime endOfDay = discharageDate.Value.Date + businessHoursEnd;

                    if (currentDate > endOfDay) {
                        overdueTimeInHours += (endOfDay - discharageDate.Value).TotalHours;
                        discharageDate = endOfDay.AddDays(1).Date + businessHoursStart;
                    } else {
                        overdueTimeInHours += (currentDate - discharageDate.Value).TotalHours;
                        discharageDate = currentDate;
                    }
                } else {
                    discharageDate = discharageDate.Value.AddDays(1);
                }
            }

            return Math.Floor(overdueTimeInHours);
        }

        private DateTime AdjustToBusinessHours(DateTime date, TimeSpan businessHoursStart, TimeSpan businessHoursEnd) {
            if (date.Hour < businessHoursStart.Hours || (date.Hour == businessHoursStart.Hours && date.Minute < businessHoursStart.Minutes)) {
                date = date.Date + businessHoursStart;
            } else if (date.Hour > businessHoursEnd.Hours || (date.Hour == businessHoursEnd.Hours && date.Minute > businessHoursEnd.Minutes)) {
                date = date.Date + businessHoursEnd;
            }

            return date;
        }

        public bool IsBusinessDay(DateTime date, DayOfWeek businessDaysFrom, DayOfWeek businessDaysTo) {
            return date.DayOfWeek >= businessDaysFrom && date.DayOfWeek <= businessDaysTo;
        }

        public bool CalculateCustomsCleared(VoyageCargoCustomsEntryType? customsEntryType, bool isCargoApproved, VoyageCargoCustomsApprovedToLoad? customsApprovedToLoad, bool isOwnerCustomsCompliant) {
            switch (customsEntryType) {
                case VoyageCargoCustomsEntryType.EIDR:
                case VoyageCargoCustomsEntryType.EIDR_AEO:
                case VoyageCargoCustomsEntryType.EMPTY:
                    return true;

                case VoyageCargoCustomsEntryType.Full:
                case VoyageCargoCustomsEntryType.Mixed:
                    return isCargoApproved || customsApprovedToLoad == VoyageCargoCustomsApprovedToLoad.YES;

                case null:
                case VoyageCargoCustomsEntryType.None:
                    return isOwnerCustomsCompliant;

                default:
                    return false;
            }
        }
    }
}