﻿namespace Lighthouse.Model.Entity {
    public class TransportRequestMaterialDetail {
        public TransportRequestMaterialDetail() {
            this.TransportRequestMaterialDetailId = Guid.NewGuid();
            this.CreatedDate = DateTime.UtcNow;
        }
        public Guid TransportRequestMaterialDetailId { get; set; }
        public Guid TransportRequestId { get; set; }
        public TransportRequest TransportRequest { get; set; }
        public Guid? VendorId { get; set; }
        public Vendor Vendor { get; set; }
        public Guid? TransportRequestCargoId { get; set; }
        public TransportRequestCargo TransportRequestCargo { get; set; }
        public Guid? TransportRequestBulkCargoId { get; set; }
        public TransportRequestBulkCargo TransportRequestBulkCargo { get; set; }
        public Guid? TransportRequestMaterialDetailDangerousGoodId { get; set; }
        public TransportRequestMaterialDetailDangerousGood TransportRequestMaterialDetailDangerousGood { get; set; }
        public string Comments { get; set; }
        public bool IsCancelled { get; set; }
        public string CancellationReason { get; set; }
        public string  PackagingUnit { get; set; }
        public bool Emballage { get; set; }
        public double EstimatedWeight { get; set; }
        public string OffshoreInstallation { get; set; }
        public Guid? OffshoreInstallationId { get; set; }
        public Asset OffshoreInstallationAsset { get; set; }
        public int Quantity { get; set; }
        public double SupplyQuantity { get; set; }
        public string Description { get; set; }
        public string CountryOfOrigin { get; set; }
        public string PoNumber { get; set; }
        public string WorkOrder { get; set; }
        public string PickupLocation { get; set; }
        public string CustomsDocumentNumber { get; set; }
        public DateTime? CustomsDocumentDate { get; set; }
        public string CommodityCode { get; set; }
        public string SerialNumber { get; set; }
        public bool Waste { get; set; }
        public int EWC { get; set; }
        public string WasteDescription { get; set; }
        public string ManifestNumber { get; set; }
        public string Value { get; set; }
        public string Requestor { get; set; }
        public int MaxStockQuantity { get; set; }
        public string ProperShippingName { get; set; }
        public string MaxStockValue { get; set; }
        public string StockMaterialReturnedAs { get; set; }
        public string ParentCargoItemStatus { get; set; }
        public string ParentCargoItemCategory { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime? UpdatedDate { get; set; }
        public DateTime? SubmittedDate { get; set; }
        public Guid CreatedById { get; set; }
        public User CreatedBy { get; set; }
        public Guid? UpdatedById { get; set; }
        public User UpdatedBy { get; set; }
        public Guid? SubmittedById { get; set; }
        public User SubmittedBy { get; set; }
        public bool Deleted { get; set; }
        public bool? ToDoIMDGComplete { get; set; }
        public TransportRequestCargoStatus Status { get; set; }
        public PackingGroup? PackingGroup { get; set; }
        public VoyageCargoCustomsEntryType? CustomsEntryType { get; set; }
        public CustomsStatus? CustomsStatus { get; set; }
        public SupplyUnitType? SupplyUnitType { get; set; }
        public TransportRequestCargoChangeReason? ChangeReason { get; set; }
        public TodoRejectedReason? RejectedReason { get; set; }
        public ICollection<TransportRequestmaterialDetailAttachment> TransportRequestMaterialDetailAttachments { get; set; }
    }
}
