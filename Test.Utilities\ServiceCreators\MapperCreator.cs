﻿namespace Lighthouse.TestUtilities.ServiceCreators {
    public static class MapperCreator {

        public static IMapper CreateMapper() {
            var mappingConfig = new MapperConfiguration(mc => {
                mc.AddProfile(new ActivityMappingProfile());
                mc.AddProfile(new BillingPeriodMappingProfile());
                mc.AddProfile(new BulkRequestMappingProfile());
                mc.AddProfile(new BulkTransactionMappingProfile());
                mc.AddProfile(new BulkTypeMappingProfile());
                mc.AddProfile(new VoyageMaterialDetailMappingProfile());
                mc.AddProfile(new VoyageMaterialDetailSnapshotMappingProfile());
                mc.AddProfile(new VoyageReservedAreaMappingProfile());
                mc.AddProfile(new VoyagePlanningDetailMappingProfile());
                mc.AddProfile(new VoyageSpecialNoteMappingProfile());
                mc.AddProfile(new VoyageCargoBulkSnapshotMappingProfile());
                mc.AddProfile(new VoyageCargoSnapshotMappingProfile());
                mc.AddProfile(new ClientAssetMappingProfile());
                mc.AddProfile(new CargoMappingProfile());
                mc.AddProfile(new CargoCertificateMappingProfile());
                mc.AddProfile(new VendorMappingProfile());
                mc.AddProfile(new VendorWarehouseMappingProfile());
                mc.AddProfile(new VoyageCommentMappingProfile());
                mc.AddProfile(new VoyageCargoMappingProfile());
                mc.AddProfile(new VoyageCargoBulkMappingProfile());
                mc.AddProfile(new VoyageEventMappingProfile());
                mc.AddProfile(new VoyageLiftingJobMappingProfile());
                mc.AddProfile(new VoyageCargoLiftMappingProfile());
                mc.AddProfile(new VoyageCargoSailingRequestActivityMappingProfile());
                mc.AddProfile(new LiftingPlanMappingProfile());
                mc.AddProfile(new ClientMappingProfile());
                mc.AddProfile(new ClientReportTypeMappingProfile());
                mc.AddProfile(new DeckUsageMappingProfile());
                mc.AddProfile(new DistanceMappingProfile());
                mc.AddProfile(new HireStatementBulkMappingProfile());
                mc.AddProfile(new HireStatementMappingProfile());
                mc.AddProfile(new LoadCellMappingProfile());
                mc.AddProfile(new AssetMappingProfile());
                mc.AddProfile(new ParallelActivityMappingProfile());
                mc.AddProfile(new ReportTypeMappingProfile());
                mc.AddProfile(new PauseReasonMappingProfile());
                mc.AddProfile(new SailingRequestMappingProfile());
                mc.AddProfile(new SailingRequestActivityMappingProfile());
                mc.AddProfile(new SettingMappingProfile());
                mc.AddProfile(new TankStatusMappingProfile());
                mc.AddProfile(new UnitMappingProfile());
                mc.AddProfile(new UserMappingProfile());
                mc.AddProfile(new VesselActivityCostAllocationMappingProfile());
                mc.AddProfile(new VesselActivityMappingProfile());
                mc.AddProfile(new VesselMappingProfile());
                mc.AddProfile(new VesselTankMappingProfile());
                mc.AddProfile(new VoyageBillingPeriodMappingProfile());
                mc.AddProfile(new VoyageMappingProfile());
                mc.AddProfile(new FlowVoyageMappingProfile());
                mc.AddProfile(new BillingPeriodDocumentMappingProfile());
                mc.AddProfile(new DangerousGoodMappingProfile());
                mc.AddProfile(new VoyageCargoDangerousGoodMappingProfile());
                mc.AddProfile(new WeightCategoryMappingProfile());
                mc.AddProfile(new ContentSnapshotMappingProfile());
                mc.AddProfile(new PoolMappingProfile());
            });

            return mappingConfig.CreateMapper();
        }
    }
}
